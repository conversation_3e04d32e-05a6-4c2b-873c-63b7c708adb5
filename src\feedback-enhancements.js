/**
 * ArionComply Feedback Enhancement System
 * Version: 2.0 - Standalone Module with Admin Tracking
 * Purpose: Complete feedback system with user notes and admin tracking
 */

class FeedbackEnhancement {
  constructor() {
    this.feedbackData = [];
    this.adminMode = false;
    this.init();
  }

  init() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.initialize());
    } else {
      this.initialize();
    }
  }

  initialize() {
    console.log('🗨️ Feedback Enhancement loading...');
    
    // Check if user is admin
    this.checkAdminMode();
    
    // Setup feedback system
    this.createFeedbackButton();
    this.setupMessageFeedback();
    this.loadFeedbackData();
    
    console.log('✅ Feedback Enhancement loaded successfully');
  }

  // =============================================================================
  // ADMIN MODE DETECTION
  // =============================================================================

  checkAdminMode() {
    const adminInfo = localStorage.getItem('arion-admin-info');
    const urlParams = new URLSearchParams(window.location.search);
    
    this.adminMode = !!(adminInfo || urlParams.get('admin') === 'true');
    
    if (this.adminMode) {
      console.log('👑 Admin mode detected for feedback system');
      this.setupAdminFeatures();
    }
  }

  setupAdminFeatures() {
    // Add admin panel for feedback management
    this.createAdminFeedbackPanel();
  }

  // =============================================================================
  // FLOATING FEEDBACK BUTTON
  // =============================================================================

  createFeedbackButton() {
    // Create floating feedback button
    const feedbackBtn = document.createElement('button');
    feedbackBtn.innerHTML = '💬 Feedback';
    feedbackBtn.className = 'floating-feedback-btn';
    feedbackBtn.onclick = () => this.showFeedbackModal();
    
    // Add styles
    this.addFeedbackStyles();
    
    document.body.appendChild(feedbackBtn);
  }

  addFeedbackStyles() {
    const style = document.createElement('style');
    style.id = 'feedback-enhancement-styles';
    style.textContent = `
      .floating-feedback-btn {
        position: fixed;
        bottom: 2rem;
        left: 2rem;
        background: #059669;
        color: white;
        border: none;
        padding: 0.75rem 1rem;
        border-radius: 25px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 600;
        box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
        transition: all 0.3s ease;
        z-index: 1000;
      }
      
      .floating-feedback-btn:hover {
        background: #047857;
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(5, 150, 105, 0.4);
      }
      
      .feedback-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        padding: 1rem;
      }
      
      .feedback-content {
        background: white;
        padding: 2.5rem;
        border-radius: 16px;
        max-width: 650px;
        width: 100%;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      }
      
      .feedback-header {
        margin-bottom: 2rem;
      }
      
      .feedback-header h3 {
        color: #059669;
        margin-bottom: 0.5rem;
        font-size: 1.5rem;
      }
      
      .feedback-header p {
        color: #64748b;
        margin: 0;
      }
      
      .feedback-form {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
      }
      
      .feedback-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }
      
      .feedback-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
      }
      
      .feedback-form label {
        font-weight: 600;
        color: #374151;
        font-size: 0.95rem;
      }
      
      .feedback-form .required {
        color: #ef4444;
      }
      
      .feedback-form input,
      .feedback-form textarea,
      .feedback-form select {
        padding: 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        font-family: inherit;
        font-size: 0.95rem;
        transition: border-color 0.2s ease;
        background: white;
      }
      
      .feedback-form input:focus,
      .feedback-form textarea:focus,
      .feedback-form select:focus {
        outline: none;
        border-color: #059669;
        box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
      }
      
      .feedback-form textarea {
        resize: vertical;
        line-height: 1.5;
      }
      
      .feedback-form textarea.large {
        min-height: 120px;
      }
      
      .feedback-form textarea.notes {
        min-height: 80px;
      }
      
      .feedback-form select {
        cursor: pointer;
        appearance: none;
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 1rem center;
        background-size: 1rem;
        padding-right: 3rem;
      }
      
      .feedback-buttons {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e2e8f0;
      }
      
      .btn-feedback {
        padding: 0.875rem 2rem;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 600;
        font-size: 0.95rem;
        transition: all 0.2s ease;
        min-width: 120px;
      }
      
      .btn-primary {
        background: #059669;
        color: white;
      }
      
      .btn-primary:hover {
        background: #047857;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
      }
      
      .btn-primary:disabled {
        background: #94a3b8;
        cursor: not-allowed;
        transform: none;
      }
      
      .btn-secondary {
        background: #f8fafc;
        color: #64748b;
        border: 1px solid #e2e8f0;
      }
      
      .btn-secondary:hover {
        background: #f1f5f9;
        color: #475569;
      }
      
      .admin-section {
        background: #fef3c7;
        border: 1px solid #f59e0b;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
      }
      
      .admin-section h4 {
        color: #92400e;
        margin: 0 0 0.5rem 0;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }
      
      .feedback-success {
        text-align: center;
        padding: 2rem;
      }
      
      .feedback-success-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
        color: #10b981;
      }
      
      .feedback-success h3 {
        color: #059669;
        margin-bottom: 1rem;
      }
      
      .feedback-id {
        background: #f0f9ff;
        border: 1px solid #0ea5e9;
        color: #0c4a6e;
        padding: 0.5rem;
        border-radius: 6px;
        font-family: monospace;
        font-size: 0.8rem;
        margin: 1rem 0;
      }
      
      /* Message feedback styles */
      .message-feedback {
        display: flex;
        gap: 0.5rem;
        margin-top: 0.5rem;
        opacity: 0.6;
        transition: opacity 0.2s;
      }
      
      .message-feedback:hover {
        opacity: 1;
      }
      
      .feedback-btn-small {
        background: none;
        border: none;
        font-size: 1rem;
        cursor: pointer;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        transition: all 0.2s;
      }
      
      .feedback-btn-small:hover {
        background: #f1f5f9;
        transform: scale(1.1);
      }
      
      .feedback-btn-small.selected {
        background: #ecfdf5;
        color: #059669;
      }
      
      /* Mobile responsiveness */
      @media (max-width: 768px) {
        .floating-feedback-btn {
          bottom: 1rem;
          left: 1rem;
          padding: 0.5rem 0.75rem;
          font-size: 0.8rem;
        }
        
        .feedback-content {
          padding: 2rem;
          margin: 1rem;
        }
        
        .feedback-row {
          grid-template-columns: 1fr;
        }
        
        .feedback-buttons {
          flex-direction: column-reverse;
        }
        
        .btn-feedback {
          width: 100%;
        }
      }
    `;
    
    document.head.appendChild(style);
  }

  // =============================================================================
  // FEEDBACK MODAL
  // =============================================================================

  showFeedbackModal() {
    const modal = document.createElement('div');
    modal.className = 'feedback-modal';
    modal.innerHTML = this.buildFeedbackModalHTML();
    
    // Close on backdrop click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });
    
    // Setup form validation
    setTimeout(() => this.setupFormValidation(modal), 100);
    
    document.body.appendChild(modal);
    
    // Focus first input
    setTimeout(() => {
      const firstInput = modal.querySelector('#feedbackType');
      if (firstInput) firstInput.focus();
    }, 100);
  }

  buildFeedbackModalHTML() {
    return `
      <div class="feedback-content">
        <div class="feedback-header">
          <h3>💬 Send Feedback</h3>
          <p>Help us improve your ArionComply experience</p>
        </div>
        
        <form class="feedback-form" id="feedbackForm">
          <div class="feedback-row">
            <div class="feedback-group">
              <label for="feedbackType">Type of Feedback <span class="required">*</span></label>
              <select id="feedbackType" name="feedbackType" required>
                <option value="">Select type...</option>
                <option value="bug">🐛 Bug Report</option>
                <option value="feature">💡 Feature Request</option>
                <option value="improvement">⚡ Improvement Suggestion</option>
                <option value="ui">🎨 UI/UX Feedback</option>
                <option value="performance">⚡ Performance Issue</option>
                <option value="general">💭 General Feedback</option>
              </select>
            </div>
            
            <div class="feedback-group">
              <label for="feedbackPriority">Priority</label>
              <select id="feedbackPriority" name="feedbackPriority">
                <option value="low">Low</option>
                <option value="medium" selected>Medium</option>
                <option value="high">High</option>
                <option value="critical">Critical</option>
              </select>
            </div>
          </div>
          
          <div class="feedback-group">
            <label for="feedbackSubject">Subject <span class="required">*</span></label>
            <input type="text" id="feedbackSubject" name="feedbackSubject" 
                   placeholder="Brief description of your feedback..." required>
          </div>
          
          <div class="feedback-group">
            <label for="feedbackMessage">Detailed Message <span class="required">*</span></label>
            <textarea id="feedbackMessage" name="feedbackMessage" class="large"
                      placeholder="Please provide detailed information about your feedback. Include steps to reproduce if reporting a bug..." required></textarea>
          </div>
          
          <div class="feedback-group">
            <label for="additionalNotes">Additional Notes</label>
            <textarea id="additionalNotes" name="additionalNotes" class="notes"
                      placeholder="Any additional context, environment details, or suggestions..."></textarea>
          </div>
          
          <div class="feedback-row">
            <div class="feedback-group">
              <label for="feedbackRating">Overall Experience (1-5)</label>
              <select id="feedbackRating" name="feedbackRating">
                <option value="">Rate your experience...</option>
                <option value="5">⭐⭐⭐⭐⭐ Excellent (5/5)</option>
                <option value="4">⭐⭐⭐⭐ Good (4/5)</option>
                <option value="3">⭐⭐⭐ Average (3/5)</option>
                <option value="2">⭐⭐ Poor (2/5)</option>
                <option value="1">⭐ Very Poor (1/5)</option>
              </select>
            </div>
            
            <div class="feedback-group">
              <label for="contactMethod">Preferred Contact</label>
              <select id="contactMethod" name="contactMethod">
                <option value="none">No follow-up needed</option>
                <option value="email">Email me updates</option>
                <option value="demo">Contact via demo platform</option>
              </select>
            </div>
          </div>
          
          ${this.adminMode ? this.buildAdminSection() : ''}
        </form>
        
        <div class="feedback-buttons">
          <button type="button" class="btn-feedback btn-secondary" onclick="this.closest('.feedback-modal').remove()">
            Cancel
          </button>
          <button type="button" class="btn-feedback btn-primary" id="submitFeedbackBtn" onclick="feedbackEnhancement.submitFeedback()">
            Send Feedback
          </button>
        </div>
      </div>
    `;
  }

  buildAdminSection() {
    return `
      <div class="admin-section">
        <h4>👑 Admin Tracking</h4>
        <div class="feedback-row">
          <div class="feedback-group">
            <label for="adminStatus">Status</label>
            <select id="adminStatus" name="adminStatus">
              <option value="new">🆕 New</option>
              <option value="reviewing">👀 Reviewing</option>
              <option value="in-progress">🔄 In Progress</option>
              <option value="resolved">✅ Resolved</option>
              <option value="closed">❌ Closed</option>
            </select>
          </div>
          
          <div class="feedback-group">
            <label for="adminAssignee">Assigned To</label>
            <select id="adminAssignee" name="adminAssignee">
              <option value="">Unassigned</option>
              <option value="yusuf">Yusuf (CEO)</option>
              <option value="libor">Libor (CTO)</option>
              <option value="dev-team">Development Team</option>
              <option value="support">Support Team</option>
            </select>
          </div>
        </div>
        
        <div class="feedback-group">
          <label for="adminNotes">Internal Notes</label>
          <textarea id="adminNotes" name="adminNotes" class="notes"
                    placeholder="Internal notes for tracking and resolution..."></textarea>
        </div>
      </div>
    `;
  }

  // =============================================================================
  // FORM VALIDATION & SUBMISSION
  // =============================================================================

  setupFormValidation(modal) {
    const form = modal.querySelector('#feedbackForm');
    const submitBtn = modal.querySelector('#submitFeedbackBtn');
    const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
    
    // Real-time validation
    inputs.forEach(input => {
      input.addEventListener('input', () => this.validateForm(form, submitBtn));
      input.addEventListener('blur', () => this.validateForm(form, submitBtn));
    });
    
    // Initial validation
    this.validateForm(form, submitBtn);
  }

  validateForm(form, submitBtn) {
    const requiredFields = form.querySelectorAll('input[required], textarea[required], select[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        isValid = false;
        field.style.borderColor = '#ef4444';
      } else {
        field.style.borderColor = '#e2e8f0';
      }
    });
    
    submitBtn.disabled = !isValid;
  }

  async submitFeedback() {
    const form = document.getElementById('feedbackForm');
    const submitBtn = document.getElementById('submitFeedbackBtn');
    const formData = new FormData(form);
    
    // Create feedback object
    const feedback = {
      id: this.generateFeedbackId(),
      type: formData.get('feedbackType'),
      priority: formData.get('feedbackPriority'),
      subject: formData.get('feedbackSubject'),
      message: formData.get('feedbackMessage'),
      additionalNotes: formData.get('additionalNotes'),
      rating: formData.get('feedbackRating'),
      contactMethod: formData.get('contactMethod'),
      
      // Admin tracking (if admin)
      adminStatus: formData.get('adminStatus') || 'new',
      adminAssignee: formData.get('adminAssignee') || null,
      adminNotes: formData.get('adminNotes') || null,
      
      // System data
      timestamp: new Date().toISOString(),
      sessionId: this.getSessionId(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      isAdmin: this.adminMode
    };
    
    // Validate required fields
    if (!feedback.type || !feedback.subject || !feedback.message) {
      this.showError('Please fill in all required fields');
      return;
    }
    
    // Show loading state
    submitBtn.disabled = true;
    submitBtn.textContent = 'Sending...';
    
    try {
      // Store locally
      this.feedbackData.push(feedback);
      this.saveFeedbackData();
      
      // TODO: Send to server
      // await ArionUtils.api.call('submit-feedback', feedback, feedback.sessionId);
      
      console.log('📝 Feedback submitted:', feedback);
      
      // Show success state
      this.showFeedbackSuccess(feedback);
      
    } catch (error) {
      console.error('Failed to submit feedback:', error);
      this.showError('Failed to submit feedback. Please try again.');
      
      submitBtn.disabled = false;
      submitBtn.textContent = 'Send Feedback';
    }
  }

  generateFeedbackId() {
    return 'FB' + Date.now().toString(36).toUpperCase() + Math.random().toString(36).substr(2, 3).toUpperCase();
  }

  getSessionId() {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('session') || null;
  }

  showFeedbackSuccess(feedback) {
    const modal = document.querySelector('.feedback-modal');
    const content = modal.querySelector('.feedback-content');
    
    content.innerHTML = `
      <div class="feedback-success">
        <div class="feedback-success-icon">✅</div>
        <h3>Thank You for Your Feedback!</h3>
        <p>Your feedback has been submitted successfully and will help us improve ArionComply.</p>
        
        <div class="feedback-id">
          Feedback ID: <strong>${feedback.id}</strong>
        </div>
        
        <p><strong>What happens next:</strong></p>
        <ul style="text-align: left; max-width: 400px; margin: 0 auto;">
          <li>We'll review your feedback within 24-48 hours</li>
          <li>${feedback.contactMethod === 'email' ? 'You\'ll receive email updates on progress' : 'Updates will be available in the platform'}</li>
          <li>High priority issues are addressed first</li>
          <li>Feature requests are considered for future releases</li>
        </ul>
        
        <div style="margin-top: 2rem;">
          <button class="btn-feedback btn-primary" onclick="this.closest('.feedback-modal').remove()">
            Close
          </button>
        </div>
      </div>
    `;
    
    // Auto close after 10 seconds
    setTimeout(() => {
      if (modal.parentNode) {
        modal.remove();
      }
    }, 10000);
  }

  showError(message) {
    // Create temporary error message
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
      position: fixed;
      top: 2rem;
      right: 2rem;
      background: #fee2e2;
      color: #dc2626;
      padding: 1rem 1.5rem;
      border-radius: 8px;
      border: 1px solid #fecaca;
      z-index: 10001;
      max-width: 300px;
    `;
    errorDiv.textContent = message;
    
    document.body.appendChild(errorDiv);
    
    setTimeout(() => {
      if (errorDiv.parentNode) {
        errorDiv.remove();
      }
    }, 5000);
  }

  // =============================================================================
  // MESSAGE-LEVEL FEEDBACK
  // =============================================================================

  setupMessageFeedback() {
    // Add thumbs up/down to assistant messages
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE && node.classList && 
              node.classList.contains('message') && node.classList.contains('assistant')) {
            setTimeout(() => this.addMessageFeedback(node), 500);
          }
        });
      });
    });
    
    const messagesDiv = document.getElementById('messages');
    if (messagesDiv) {
      observer.observe(messagesDiv, { childList: true });
    }
  }

  addMessageFeedback(messageElement) {
    // Don't add feedback to messages that already have it
    if (messageElement.querySelector('.message-feedback')) return;
    
    const feedbackDiv = document.createElement('div');
    feedbackDiv.className = 'message-feedback';
    feedbackDiv.innerHTML = `
      <button class="feedback-btn-small" data-feedback="helpful" title="Helpful response">👍</button>
      <button class="feedback-btn-small" data-feedback="not-helpful" title="Not helpful">👎</button>
      <button class="feedback-btn-small" data-feedback="report" title="Report issue with this response">⚠️</button>
    `;
    
    // Add click handlers
    feedbackDiv.addEventListener('click', (e) => {
      if (e.target.classList.contains('feedback-btn-small')) {
        this.handleMessageFeedback(e.target, messageElement);
      }
    });
    
    messageElement.appendChild(feedbackDiv);
  }

  handleMessageFeedback(button, messageElement) {
    // Remove selection from other buttons in this message
    const allButtons = messageElement.querySelectorAll('.feedback-btn-small');
    allButtons.forEach(btn => btn.classList.remove('selected'));
    
    // Select clicked button
    button.classList.add('selected');
    
    const messageText = messageElement.querySelector('.message-bubble').textContent;
    const feedbackType = button.dataset.feedback;
    
    // Create feedback record
    const messageFeedback = {
      id: this.generateFeedbackId(),
      type: 'message-feedback',
      messageContent: messageText.substring(0, 200) + (messageText.length > 200 ? '...' : ''),
      feedback: feedbackType,
      timestamp: new Date().toISOString(),
      sessionId: this.getSessionId()
    };
    
    // Store locally
    this.feedbackData.push(messageFeedback);
    this.saveFeedbackData();
    
    console.log('👍 Message feedback:', messageFeedback);
    
    // Show brief confirmation
    if (feedbackType === 'report') {
      this.showQuickFeedbackModal(messageText);
    } else {
      this.showBriefConfirmation(button, feedbackType === 'helpful' ? 'Thanks!' : 'Noted');
    }
  }

  showBriefConfirmation(element, message) {
    const tooltip = document.createElement('div');
    tooltip.textContent = message;
    tooltip.style.cssText = `
      position: absolute;
      background: #059669;
      color: white;
      padding: 0.25rem 0.75rem;
      border-radius: 4px;
      font-size: 0.8rem;
      z-index: 1000;
      transform: translateX(-50%);
      white-space: nowrap;
    `;
    
    const rect = element.getBoundingClientRect();
    tooltip.style.top = (rect.top - 35) + 'px';
    tooltip.style.left = (rect.left + rect.width / 2) + 'px';
    
    document.body.appendChild(tooltip);
    
    setTimeout(() => {
      if (tooltip.parentNode) {
        tooltip.remove();
      }
    }, 2000);
  }

  showQuickFeedbackModal(messageContent) {
    // Quick feedback modal for reported messages
    const modal = document.createElement('div');
    modal.className = 'feedback-modal';
    modal.innerHTML = `
      <div class="feedback-content" style="max-width: 500px;">
        <div class="feedback-header">
          <h3>⚠️ Report Response Issue</h3>
          <p>Help us understand what went wrong with this response</p>
        </div>
        
        <form class="feedback-form" id="quickFeedbackForm">
          <div class="feedback-group">
            <label for="quickIssueType">What's the issue?</label>
            <select id="quickIssueType" name="quickIssueType" required>
              <option value="">Select issue type...</option>
              <option value="incorrect">❌ Incorrect information</option>
              <option value="unhelpful">🤷 Unhelpful response</option>
              <option value="inappropriate">⚠️ Inappropriate content</option>
              <option value="formatting">📝 Poor formatting</option>
              <option value="other">🔧 Other issue</option>
            </select>
          </div>
          
          <div class="feedback-group">
            <label for="quickDescription">Brief description</label>
            <textarea id="quickDescription" name="quickDescription" class="notes" 
                      placeholder="Please describe the issue..." required></textarea>
          </div>
          
          <div style="background: #f8fafc; padding: 1rem; border-radius: 6px; margin: 1rem 0;">
            <strong>Response content:</strong>
            <div style="font-size: 0.8rem; color: #64748b; margin-top: 0.5rem; max-height: 100px; overflow-y: auto;">
              ${messageContent.substring(0, 300)}${messageContent.length > 300 ? '...' : ''}
            </div>
          </div>
        </form>
        
        <div class="feedback-buttons">
          <button type="button" class="btn-feedback btn-secondary" onclick="this.closest('.feedback-modal').remove()">
            Cancel
          </button>
          <button type="button" class="btn-feedback btn-primary" onclick="feedbackEnhancement.submitQuickFeedback('${messageContent}')">
            Report Issue
          </button>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    
    // Focus first input
    setTimeout(() => {
      modal.querySelector('#quickIssueType').focus();
    }, 100);
  }

  async submitQuickFeedback(messageContent) {
    const form = document.getElementById('quickFeedbackForm');
    const formData = new FormData(form);
    
    const feedback = {
      id: this.generateFeedbackId(),
      type: 'message-report',
      issueType: formData.get('quickIssueType'),
      description: formData.get('quickDescription'),
      messageContent: messageContent,
      timestamp: new Date().toISOString(),
      sessionId: this.getSessionId(),
      priority: 'high'
    };
    
    if (!feedback.issueType || !feedback.description) {
      this.showError('Please fill in all fields');
      return;
    }
    
    // Store feedback
    this.feedbackData.push(feedback);
    this.saveFeedbackData();
    
    console.log('⚠️ Message reported:', feedback);
    
    // Close modal and show confirmation
    document.querySelector('.feedback-modal').remove();
    this.showError('Thank you for the report. We\'ll review this response.', false);
  }

  // =============================================================================
  // DATA MANAGEMENT
  // =============================================================================

  loadFeedbackData() {
    try {
      const stored = localStorage.getItem('arion-feedback-data');
      this.feedbackData = stored ? JSON.parse(stored) : [];
      console.log(`📊 Loaded ${this.feedbackData.length} feedback items`);
    } catch (error) {
      console.error('Failed to load feedback data:', error);
      this.feedbackData = [];
    }
  }

  saveFeedbackData() {
    try {
      localStorage.setItem('arion-feedback-data', JSON.stringify(this.feedbackData));
      console.log(`💾 Saved ${this.feedbackData.length} feedback items`);
    } catch (error) {
      console.error('Failed to save feedback data:', error);
    }
  }

  // =============================================================================
  // ADMIN FEATURES
  // =============================================================================

  createAdminFeedbackPanel() {
    // Add admin feedback management panel
    // This could be expanded into a full admin interface
    console.log('👑 Admin feedback panel ready');
  }

  // =============================================================================
  // PUBLIC API
  // =============================================================================

  // Get all feedback data
  getFeedbackData() {
    return [...this.feedbackData]; // Return copy
  }

  // Export feedback data
  exportFeedbackData() {
    const data = this.getFeedbackData();
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `arion-feedback-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    
    URL.revokeObjectURL(url);
    console.log(`📁 Exported ${data.length} feedback items`);
  }

  // Clear all feedback data
  clearFeedbackData() {
    if (confirm('Are you sure you want to clear all feedback data?')) {
      this.feedbackData = [];
      localStorage.removeItem('arion-feedback-data');
      console.log('🗑️ All feedback data cleared');
    }
  }

  // Get feedback statistics
  getFeedbackStats() {
    const total = this.feedbackData.length;
    const byType = {};
    const byPriority = {};
    const byRating = {};
    
    this.feedbackData.forEach(item => {
      byType[item.type] = (byType[item.type] || 0) + 1;
      if (item.priority) byPriority[item.priority] = (byPriority[item.priority] || 0) + 1;
      if (item.rating) byRating[item.rating] = (byRating[item.rating] || 0) + 1;
    });
    
    return { total, byType, byPriority, byRating };
  }
}

// Initialize the feedback enhancement
const feedbackEnhancement = new FeedbackEnhancement();

// Make it globally available
window.feedbackEnhancement = feedbackEnhancement;

// Debug functions for console
window.showFeedback = () => feedbackEnhancement.showFeedbackModal();
window.exportFeedback = () => feedbackEnhancement.exportFeedbackData();
window.clearFeedback = () => feedbackEnhancement.clearFeedbackData();
window.feedbackStats = () => console.table(feedbackEnhancement.getFeedbackStats());

console.log('🎯 Feedback Enhancement ready! Try: showFeedback(), exportFeedback(), feedbackStats()');
