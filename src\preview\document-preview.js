/**
 * ArionComply Document Preview JavaScript
 * Version: 1.1 - Updated for Router API Pattern
 * Purpose: Handle document preview display, navigation, and lead capture
 */

class DocumentPreview {
  /**
   * Constructor - Sets up the document preview system
   * This runs when a new DocumentPreview instance is created
   */
  constructor() {
    // Get session ID from URL parameter or generate a new one
    this.sessionId = this.getSessionFromURL() || ArionUtils.session.generateUUID();
    
    // Array to store all available documents
    this.documents = [];
    
    // Currently viewed document (when in detail view)
    this.currentDocument = null;
    
    // Track which documents the user has viewed (for lead capture)
    this.viewedDocuments = new Set();
    
    // Start initialization
    this.init();
  }

  /**
   * Initialize the document preview system
   * This sets up everything needed for the preview functionality
   */
  init() {
    // Find and store references to HTML elements
    this.bindElements();
    
    // Set up event listeners for user interactions
    this.setupEventListeners();
    
    // Load documents from the backend
    this.loadDocuments();
    
    // Start auto-refresh to check for new documents
    this.startAutoRefresh();
  }

  /**
   * Find and store references to HTML elements we'll need
   * This makes it easier to show/hide elements and handle user interactions
   */
  bindElements() {
    this.elements = {
      // Grid container that shows all available documents
      documentsGrid: document.getElementById('documents-grid'),
      
      // Loading spinner shown while loading documents
      loadingSpinner: document.getElementById('loading-spinner'),
      
      // Error message display area
      errorMessage: document.getElementById('error-message'),
      
      // Document viewer container (full document display)
      documentViewer: document.getElementById('document-viewer'),
      
      // Container for document content
      documentContent: document.getElementById('document-content'),
      
      // Document title display
      documentTitle: document.getElementById('document-title'),
      
      // Document metadata display (type, pages, date)
      documentMeta: document.getElementById('document-meta'),
      
      // Back button to return to document list
      backButton: document.getElementById('back-button'),
      
      // Lead capture modal/popup
      leadModal: document.getElementById('lead-modal'),
      
      // Lead capture form
      leadForm: document.getElementById('lead-form'),
      
      // Input fields in the lead form
      leadFormInputs: {
        firstName: document.getElementById('lead-first-name'),
        lastName: document.getElementById('lead-last-name'),
        email: document.getElementById('lead-email'),
        company: document.getElementById('lead-company'),
        phone: document.getElementById('lead-phone'),
        jobTitle: document.getElementById('lead-job-title'),
        interest: document.getElementById('lead-interest'),
        message: document.getElementById('lead-message'),
        dataProcessingConsent: document.getElementById('lead-data-processing-consent'),
        marketingConsent: document.getElementById('lead-marketing-consent')
      },
      
      // Lead form submit button
      leadSubmitBtn: document.getElementById('lead-submit'),
      
      // Lead form close button
      leadCloseBtn: document.getElementById('lead-close'),
      
      // Empty state display (when no documents available)
      emptyState: document.getElementById('empty-state'),
      
      // Button to return to assistant
      generateButton: document.getElementById('generate-button')
    };
  }

  /**
   * Set up event listeners for user interactions
   * These define what happens when users click buttons, submit forms, etc.
   */
  setupEventListeners() {
    // Back button returns to document list from individual document view
    this.elements.backButton?.addEventListener('click', () => this.showDocumentsList());
    
    // Close lead capture modal
    this.elements.leadCloseBtn?.addEventListener('click', () => this.closeLeadModal());
    
    // Submit lead capture form
    this.elements.leadSubmitBtn?.addEventListener('click', () => this.submitLeadForm());
    
    // Return to assistant to generate more documents
    this.elements.generateButton?.addEventListener('click', () => this.redirectToAssistant());
    
    // Set up form validation for lead form inputs
    Object.values(this.elements.leadFormInputs).forEach(input => {
      if (input) {
        input.addEventListener('input', () => this.validateLeadForm());
      }
    });

    // Allow closing lead modal with Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.elements.leadModal?.classList.contains('show')) {
        this.closeLeadModal();
      }
    });
  }

  /**
   * Load documents from the backend API
   * This gets all available documents for the current user session
   */
  async loadDocuments() {
    try {
      // Show loading spinner while fetching documents
      this.showLoading();
      
      // Make API call using router pattern
      const response = await ArionUtils.api.getDocuments(this.sessionId);

      // If successful, store the documents and display them
      if (response.success) {
        this.documents = response.data.documents || [];
        this.renderDocumentsList();
      } else {
        // If there was an error, show error message
        throw new Error(response.error || 'Failed to load documents');
      }
    } catch (error) {
      console.error('Failed to load documents:', error);
      this.showError('Failed to load documents. Please try again.');
    } finally {
      // Always hide loading spinner when done
      this.hideLoading();
    }
  }

  /**
   * Display the list of available documents in a grid layout
   * This creates cards for each document showing preview information
   */
  renderDocumentsList() {
    // If no documents grid element exists, can't render
    if (!this.elements.documentsGrid) return;

    // If no documents are available, show empty state
    if (this.documents.length === 0) {
      this.showEmptyState();
      return;
    }

    // Hide empty state and show documents
    this.hideEmptyState();
    
    // Create HTML for each document card
    const html = this.documents.map(doc => this.createDocumentCard(doc)).join('');
    this.elements.documentsGrid.innerHTML = html;
    
    // Add click handlers to each document card
    this.elements.documentsGrid.querySelectorAll('.document-card').forEach(card => {
      card.addEventListener('click', () => {
        const docId = card.dataset.docId; // Get document ID from data attribute
        this.viewDocument(docId);
      });
    });
  }

  /**
   * Create HTML for a single document card
   * This shows document type, title, metadata, and preview badge
   */
  createDocumentCard(doc) {
    // Get icon and label for this document type using ArionConfig
    const typeIcon = ArionConfig.documents.typeIcons[doc.document_type] || '📄';
    const typeLabel = ArionConfig.documents.typeLabels[doc.document_type] || 'Document';
    
    // Check if user has already viewed this document
    const isViewed = this.viewedDocuments.has(doc.id);
    
    // Build and return the HTML for the card
    return `
      <div class="document-card ${isViewed ? 'viewed' : ''}" data-doc-id="${doc.id}">
        <div class="document-icon">${typeIcon}</div>
        <div class="document-info">
          <h3 class="document-title">${ArionUtils.string.escapeHtml(doc.title)}</h3>
          <p class="document-type">${typeLabel}</p>
          <div class="document-meta">
            <span class="document-pages">${doc.page_count || 0} pages</span>
            <span class="document-generated">${ArionUtils.datetime.formatDate(doc.created_at)}</span>
          </div>
        </div>
        <div class="document-badge">
          <span class="preview-badge">Preview Only</span>
          ${isViewed ? '<span class="viewed-badge">Viewed</span>' : ''}
        </div>
      </div>
    `;
  }

  /**
   * View a specific document in full-screen mode
   * This loads the complete document content and displays it
   */
  async viewDocument(docId) {
    try {
      // Show loading while fetching document content
      this.showLoading();
      
      // Make API call using router pattern
      const response = await ArionUtils.api.getDocument(this.sessionId, docId);

      if (response.success) {
        // Store the document and show it
        this.currentDocument = response.data.document;
        this.renderDocumentViewer();
        
        // Track that user viewed this document
        this.trackDocumentView(docId);
      } else {
        throw new Error(response.error || 'Failed to load document');
      }
    } catch (error) {
      console.error('Failed to view document:', error);
      this.showError('Failed to load document. Please try again.');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Render the document viewer interface
   * This displays the full document content in a readable format
   */
  renderDocumentViewer() {
    // If no document is loaded or viewer element doesn't exist, return
    if (!this.currentDocument || !this.elements.documentViewer) return;

    const doc = this.currentDocument;
    
    // Update document title in the viewer header
    if (this.elements.documentTitle) {
      this.elements.documentTitle.textContent = doc.title;
    }
    
    // Update document metadata (type, pages, generation date)
    if (this.elements.documentMeta) {
      const typeLabel = ArionConfig.documents.typeLabels[doc.document_type] || 'Document';
      this.elements.documentMeta.innerHTML = `
        <span class="doc-type">${typeLabel}</span>
        <span class="doc-pages">${doc.page_count || 0} pages</span>
        <span class="doc-generated">${ArionUtils.datetime.formatDate(doc.created_at)}</span>
      `;
    }

    // Render the document content
    if (this.elements.documentContent) {
      this.elements.documentContent.innerHTML = this.renderDocumentContent(doc);
    }

    // Hide document grid and show viewer
    this.elements.documentsGrid?.classList.add('hidden');
    this.elements.documentViewer?.classList.remove('hidden');
    this.elements.documentViewer?.classList.add('show');
  }

  /**
   * Convert document data into displayable HTML content
   * This handles different document formats and adds watermarks
   */
/**
 * Convert document data into displayable HTML content
 * This handles different document formats and adds watermarks
 */
renderDocumentContent(doc) {
  // Start with watermark to indicate this is preview only
  let html = '<div class="document-watermark">PREVIEW ONLY - NOT FOR DOWNLOAD</div>';
  
  // If document has sections, render each section separately
  if (doc.sections && doc.sections.length > 0) {
    html += doc.sections.map(section => `
      <div class="document-section">
        <h2 class="section-title">${ArionUtils.string.escapeHtml(section.title)}</h2>
        <div class="section-content">${this.formatContentImproved(section.content)}</div>
      </div>
    `).join('');
  } else {
    // If no sections, render as single body of content
    html += `<div class="document-body">${this.formatContentImproved(doc.content)}</div>`;
  }
  
  return html;
}

  /**
 * Format content text for display with proper HTML structure
 * This converts markdown-style formatting to readable HTML
 */
formatContentImproved(content) {
  if (!content) return '';
  
  return content
    // First handle headers (must be done before paragraph processing)
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    
    // Handle lists
    .replace(/^\* (.*$)/gim, '<li>$1</li>')
    .replace(/^- (.*$)/gim, '<li>$1</li>')
    
    // Handle bold and italic text
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    
    // Convert double newlines to paragraph breaks
    .replace(/\n\n/g, '</p><p>')
    
    // Convert single newlines to line breaks
    .replace(/\n/g, '<br>')
    
    // Wrap content in paragraph tags
    .replace(/^/, '<p>')
    .replace(/$/, '</p>')
    
    // Wrap consecutive list items in <ul> tags
    .replace(/(<li>.*?<\/li>(?:\s*<br>\s*<li>.*?<\/li>)*)/gs, '<ul>$1</ul>')
    
    // Clean up line breaks inside lists
    .replace(/<ul>(.*?)<\/ul>/gs, (match, content) => {
      return '<ul>' + content.replace(/<br>/g, '') + '</ul>';
    })
    
    // Clean up empty paragraphs
    .replace(/<p><\/p>/g, '')
    .replace(/<p>\s*<\/p>/g, '');
}
  /**
   * Return to the document list view
   * This hides the individual document viewer and shows the grid
   */
  showDocumentsList() {
    this.elements.documentViewer?.classList.add('hidden');
    this.elements.documentsGrid?.classList.remove('hidden');
    this.currentDocument = null;
  }

  /**
   * Track when a user views a document
   * This is used for analytics and lead capture timing
   */
  trackDocumentView(docId) {
    // Add to viewed documents set
    this.viewedDocuments.add(docId);
    
    // Check if we should show lead capture modal
    // Show after user has viewed the threshold number of documents
    if (this.viewedDocuments.size >= ArionConfig.documents.leadCaptureThreshold) {
      setTimeout(() => this.showLeadCaptureModal(), 2000); // 2 second delay
    }
    
    // Log the document view for analytics
    this.logDocumentView(docId);
  }

  /**
   * Show the lead capture modal
   * This asks for user's contact information after they've engaged
   */
  showLeadCaptureModal() {
    // Only show if modal exists and isn't already shown
    if (this.elements.leadModal && !this.elements.leadModal.classList.contains('show')) {
      this.elements.leadModal.classList.add('show');
      // Focus on first input field for better UX
      this.elements.leadFormInputs.firstName?.focus();
    }
  }

  /**
   * Close the lead capture modal
   * This hides the contact form
   */
  closeLeadModal() {
    this.elements.leadModal?.classList.remove('show');
  }

  /**
   * Validate the lead capture form
   * This ensures required fields are filled and email is valid
   */
  validateLeadForm() {
    const { firstName, lastName, email, company, dataProcessingConsent } = this.elements.leadFormInputs;
    
    // Check required fields and validation
    const isValid = firstName?.value.trim() && 
                   lastName?.value.trim() && 
                   email?.value.trim() && 
                   ArionUtils.validation.isValidEmail(email.value) &&
                   company?.value.trim() &&
                   dataProcessingConsent?.checked;
    
    // Enable/disable submit button based on validation
    if (this.elements.leadSubmitBtn) {
      this.elements.leadSubmitBtn.disabled = !isValid;
    }
    
    return isValid;
  }

  /**
   * Submit the lead capture form
   * This sends the user's contact information to the backend
   */
  async submitLeadForm() {
    // Don't submit if form is invalid
    if (!this.validateLeadForm()) return;
    
    try {
      // Disable submit button and show loading state
      this.elements.leadSubmitBtn.disabled = true;
      this.elements.leadSubmitBtn.textContent = 'Submitting...';
      
      // Collect form data
      const formData = {
        first_name: this.elements.leadFormInputs.firstName.value.trim(),
        last_name: this.elements.leadFormInputs.lastName.value.trim(),
        email: this.elements.leadFormInputs.email.value.trim(),
        company_name: this.elements.leadFormInputs.company.value.trim(),
        phone_number: this.elements.leadFormInputs.phone?.value.trim() || null,
        job_title: this.elements.leadFormInputs.jobTitle?.value.trim() || null,
        interest_area: this.elements.leadFormInputs.interest?.value || null,
        message: this.elements.leadFormInputs.message?.value.trim() || null,
        data_processing_consented: this.elements.leadFormInputs.dataProcessingConsent.checked,
        marketing_consented: this.elements.leadFormInputs.marketingConsent?.checked || false,
        documents_viewed: Array.from(this.viewedDocuments),
        lead_source: 'demo_preview'
      };

      // Send to backend using router pattern
      const response = await ArionUtils.api.saveContact(this.sessionId, formData);

      if (response.success) {
        // Show success message
        this.showLeadSuccess();
        
        // Track lead capture
        ArionUtils.analytics.track('lead_captured', {
          source: 'document_preview',
          documentsViewed: this.viewedDocuments.size
        }, this.sessionId);
      } else {
        throw new Error(response.error || 'Failed to submit form');
      }
    } catch (error) {
      console.error('Lead form submission failed:', error);
      this.showLeadError();
    } finally {
      // Always reset button state
      this.elements.leadSubmitBtn.disabled = false;
      this.elements.leadSubmitBtn.textContent = 'Submit';
    }
  }

  /**
   * Show success message after lead form submission
   * This replaces the form with a thank you message
   */
  showLeadSuccess() {
    if (this.elements.leadModal) {
      this.elements.leadModal.innerHTML = `
        <div class="lead-success">
          <div class="success-icon">✅</div>
          <h3>Thank you!</h3>
          <p>We've received your information and will be in touch soon.</p>
          <button onclick="documentPreview.closeLeadModal()" class="btn btn-primary">Close</button>
        </div>
      `;
    }
  }

  /**
   * Show error message if lead form submission fails
   * This displays a temporary error message
   */
  showLeadError() {
    // Create error message element
    const errorDiv = document.createElement('div');
    errorDiv.className = 'lead-error';
    errorDiv.textContent = 'Failed to submit form. Please try again.';
    
    // Add to modal
    this.elements.leadModal?.insertBefore(errorDiv, this.elements.leadForm);
    
    // Remove error message after 3 seconds
    setTimeout(() => errorDiv.remove(), 3000);
  }

  /**
   * Start auto-refresh to check for new documents
   * This periodically checks if new documents have been generated
   */
  startAutoRefresh() {
    setInterval(() => {
      // Only refresh if we're not currently viewing a specific document
      if (!this.currentDocument) {
        this.loadDocuments();
      }
    }, ArionConfig.documents.autoRefreshInterval);
  }

  /**
   * Redirect user back to the assistant
   * This takes them back to the chat interface
   */
  redirectToAssistant() {
    window.location.href = '/demo/';
  }

  // =============================================================================
  // UI STATE MANAGEMENT METHODS
  // These methods show/hide different parts of the interface
  // =============================================================================

  /**
   * Show empty state when no documents are available
   * This displays a message encouraging users to use the assistant
   */
  showEmptyState() {
    this.elements.emptyState?.classList.remove('hidden');
    this.elements.documentsGrid?.classList.add('hidden');
  }

  /**
   * Hide empty state when documents are available
   * This shows the document grid instead
   */
  hideEmptyState() {
    this.elements.emptyState?.classList.add('hidden');
    this.elements.documentsGrid?.classList.remove('hidden');
  }

  /**
   * Show loading spinner
   * This indicates that data is being fetched
   */
  showLoading() {
    this.elements.loadingSpinner?.classList.remove('hidden');
  }

  /**
   * Hide loading spinner
   * This indicates that loading is complete
   */
  hideLoading() {
    this.elements.loadingSpinner?.classList.add('hidden');
  }

  /**
   * Show error message
   * This displays error text to the user
   */
  showError(message) {
    if (this.elements.errorMessage) {
      this.elements.errorMessage.textContent = message;
      this.elements.errorMessage.classList.remove('hidden');
    }
  }

  /**
   * Hide error message
   * This clears any displayed error
   */
  hideError() {
    this.elements.errorMessage?.classList.add('hidden');
  }

  // =============================================================================
  // UTILITY METHODS
  // These are helper functions used throughout the class
  // =============================================================================

  /**
   * Get session ID from URL parameters
   * This allows linking from the assistant to the document preview
   */
  getSessionFromURL() {
    const params = new URLSearchParams(window.location.search);
    return params.get('session');
  }

  /**
   * Log document view for analytics
   * This tracks which documents users are viewing
   */
  async logDocumentView(docId) {
    try {
      await ArionUtils.api.logInteraction(this.sessionId, 'document_viewed', {
        documentId: docId,
        viewCount: this.viewedDocuments.size
      });
    } catch (error) {
      console.error('Document view logging failed:', error);
      // Don't throw - logging failures shouldn't break the app
    }
  }
}

// =============================================================================
// INITIALIZATION CODE
// This code runs when the page loads and sets up the document preview
// =============================================================================

// Global variable to store the document preview instance
let documentPreview;

/**
 * Initialize the document preview when the page finishes loading
 * This event listener waits for the HTML to be fully loaded before running
 */
document.addEventListener('DOMContentLoaded', function() {
  // Create a new document preview instance
  documentPreview = new DocumentPreview();
  
  // Make the document preview available globally
  // This is needed for inline event handlers like onclick="documentPreview.method()"
  window.documentPreview = documentPreview;
});

// =============================================================================
// MODULE EXPORTS
// This allows the document preview to be imported in other JavaScript files
// =============================================================================

if (typeof module !== 'undefined' && module.exports) {
  module.exports = DocumentPreview;
}