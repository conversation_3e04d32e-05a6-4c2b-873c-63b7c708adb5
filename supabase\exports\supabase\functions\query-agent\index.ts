// supabase/functions/query-agent/index.ts
// ENHANCED VERSION - Detects when documents should be generated
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-api-key, x-session-id, accept',
  'Access-Control-Allow-Methods': 'POST, OPTIONS, GET'
};
// Enhanced goal detection patterns
const GOAL_PATTERNS = {
  document_generation: [
    /generate.*document/i,
    /create.*form/i,
    /need.*contract/i,
    /draft.*agreement/i,
    /make.*template/i,
    /help.*with.*paperwork/i,
    /create.*proposal/i,
    /generate.*invoice/i,
    /draft.*terms/i,
    /make.*contract/i
  ],
  consultation: [
    /consultation/i,
    /advice/i,
    /guidance/i,
    /recommend/i,
    /suggest/i,
    /what.*should.*i/i
  ],
  information: [
    /what.*is/i,
    /how.*does/i,
    /explain/i,
    /tell.*me.*about/i,
    /information/i
  ]
};
// Document-specific patterns
const DOCUMENT_TYPE_PATTERNS = {
  contract: [
    /contract/i,
    /agreement/i,
    /service.*agreement/i
  ],
  proposal: [
    /proposal/i,
    /business.*proposal/i,
    /project.*proposal/i
  ],
  invoice: [
    /invoice/i,
    /bill/i,
    /payment/i
  ],
  terms_of_service: [
    /terms.*of.*service/i,
    /terms.*and.*conditions/i,
    /tos/i,
    /legal.*terms/i
  ]
};
// Quick reply suggestions based on your original flow
const QUICK_REPLIES = {
  document_generation: [
    "What documents can you create?",
    "How does document generation work?",
    "Show me document templates",
    "I need compliance documentation"
  ],
  consultation: [
    "Tell me about compliance consulting",
    "What compliance areas do you cover?",
    "How do you help with governance?",
    "I need compliance guidance"
  ],
  information: [
    "Tell me more about this topic",
    "What are the next steps?",
    "How do I get started?",
    "What else should I know?"
  ],
  general: [
    "Tell me more about this topic",
    "What are the next steps?",
    "How do I get started?",
    "What else should I know?"
  ]
};
// Context-aware quick replies based on conversation
function getContextualQuickReplies(goal, userMessage, assistantResponse, conversationHistory) {
  console.log('🎯 Generating contextual replies for:', {
    goal,
    userMessage,
    assistantResponse: assistantResponse?.substring(0, 100)
  });
  const lowerMessage = userMessage.toLowerCase();
  const lowerResponse = assistantResponse.toLowerCase();
  // Always generate contextual replies - never use defaults
  let contextualReplies = [];
  // Based on what user asked about
  if (lowerMessage.includes('feature') || lowerMessage.includes('capabilities') || lowerResponse.includes('feature')) {
    contextualReplies = [
      "Tell me more about compliance management",
      "How does document generation work?",
      "Show me reporting capabilities",
      "What about training features?"
    ];
  } else if (lowerMessage.includes('compliance journey') || lowerMessage.includes('get started') || lowerResponse.includes('assessment')) {
    contextualReplies = [
      "Yes, let's start with assessment",
      "What does the initial evaluation involve?",
      "How long does setup typically take?",
      "Can you help with risk assessment?"
    ];
  } else if (lowerMessage.includes('governance') || lowerResponse.includes('governance') || lowerResponse.includes('policy')) {
    contextualReplies = [
      "Tell me about policy management",
      "How do you handle compliance monitoring?",
      "What about stakeholder management?",
      "Show me governance workflows"
    ];
  } else if (lowerMessage.includes('tour') || lowerMessage.includes('platform') || lowerResponse.includes('tour')) {
    contextualReplies = [
      "Start with the dashboard overview",
      "Show me compliance tracking",
      "Walk me through document management",
      "Demonstrate reporting features"
    ];
  } else if (lowerResponse.includes('assessment') || lowerResponse.includes('evaluation')) {
    contextualReplies = [
      "Yes, help me with the assessment",
      "What questions will you ask?",
      "How detailed is the evaluation?",
      "What happens after assessment?"
    ];
  } else if (lowerResponse.includes('step') || lowerResponse.includes('process') || lowerResponse.includes('implementation')) {
    contextualReplies = [
      "Tell me about step 1 in detail",
      "What resources do I need?",
      "How long does each phase take?",
      "Can you guide me through this?"
    ];
  } else {
    // Even for general responses, make them contextual
    contextualReplies = [
      "Can you elaborate on this topic?",
      "What would you recommend next?",
      "How do I take action on this?",
      "What are the key priorities?"
    ];
  }
  console.log('✅ Generated contextual replies:', contextualReplies);
  return contextualReplies;
}
function detectGoal(message) {
  const lowerMessage = message.toLowerCase();
  for (const [goal, patterns] of Object.entries(GOAL_PATTERNS)){
    if (patterns.some((pattern)=>pattern.test(lowerMessage))) {
      return goal;
    }
  }
  return 'general';
}
function detectDocumentType(message) {
  const lowerMessage = message.toLowerCase();
  for (const [docType, patterns] of Object.entries(DOCUMENT_TYPE_PATTERNS)){
    if (patterns.some((pattern)=>pattern.test(lowerMessage))) {
      return docType;
    }
  }
  return null;
}
function getQuickReplies(goal, context) {
  // ALWAYS use contextual replies - never fall back to static ones
  const userMessage = context?.lastMessage || '';
  const assistantResponse = context?.assistantResponse || '';
  const conversationHistory = context?.conversationHistory || [];
  return getContextualQuickReplies(goal, userMessage, assistantResponse, conversationHistory);
}
async function queryVectorDB(supabase, query, sessionId) {
  try {
    // Your existing vector DB logic
    const { data: vectorResults, error } = await supabase.rpc('match_documents', {
      query_embedding: await generateEmbedding(query),
      match_threshold: 0.78,
      match_count: 5
    });
    if (error) {
      console.error('Vector DB query error:', error);
      return null;
    }
    return vectorResults;
  } catch (error) {
    console.error('Vector DB integration error:', error);
    return null;
  }
}
async function generateEmbedding(text) {
  // Mock embedding - replace with actual service
  return new Array(1536).fill(0).map(()=>Math.random());
}
async function callClaudeAPI(messages, context) {
  const CLAUDE_API_KEY = Deno.env.get('ANTHROPIC_API_KEY');
  if (!CLAUDE_API_KEY) {
    throw new Error('ANTHROPIC_API_KEY not configured');
  }
  const systemPrompt = `You are a helpful AI assistant for ArionComply, a professional compliance platform.

You are a knowledgeable guide who helps users with:
1. Platform tours and feature explanations
2. Compliance journey guidance and governance advice
3. General information about compliance and regulatory requirements
4. Document generation (only when explicitly requested)

IMPORTANT INSTRUCTIONS:
- Start conversations naturally and be helpful with compliance guidance
- Only mention document generation if the user specifically asks about creating, generating, or drafting documents
- Focus primarily on compliance advice, platform features, and guidance
- Be conversational and helpful, not pushy about document generation
- When users ask about your capabilities, mention document generation as one of many features
- If they want to generate a document, ask for details and confirm before proceeding

Current context: ${context ? JSON.stringify(context, null, 2) : 'New conversation'}`;
  const requestBody = {
    model: 'claude-3-5-sonnet-20241022',
    max_tokens: 1000,
    temperature: 0.7,
    system: systemPrompt,
    messages: messages.map((msg)=>({
        role: msg.role,
        content: msg.content
      }))
  };
  const response = await fetch('https://api.anthropic.com/v1/messages', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': CLAUDE_API_KEY,
      'anthropic-version': '2023-06-01'
    },
    body: JSON.stringify(requestBody)
  });
  if (!response.ok) {
    throw new Error(`Claude API error: ${response.status}`);
  }
  const data = await response.json();
  return data.content[0].text;
}
serve(async (req)=>{
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  try {
    const supabase = createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_ANON_KEY') ?? '');
    const { message, session_id, user_context } = await req.json();
    if (!message || !session_id) {
      return new Response(JSON.stringify({
        error: 'Message and session_id required'
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Get or create session
    let { data: session, error: sessionError } = await supabase.from('demo_sessions').select('*').eq('session_id', session_id).single();
    if (sessionError && sessionError.code !== 'PGRST116') {
      throw sessionError;
    }
    if (!session) {
      const { data: newSession, error: createError } = await supabase.from('demo_sessions').insert({
        session_id,
        messages: [],
        context: user_context || {},
        created_at: new Date().toISOString()
      }).select().single();
      if (createError) throw createError;
      session = newSession;
    }
    // Enhanced goal detection
    const goal = detectGoal(message);
    const documentType = detectDocumentType(message);
    console.log(`🎯 Detected goal: ${goal}, Document type: ${documentType}`);
    // Build conversation history
    const messages = [
      ...session.messages || [],
      {
        role: 'user',
        content: message,
        timestamp: new Date().toISOString()
      }
    ];
    // Query vector database for context
    const vectorContext = await queryVectorDB(supabase, message, session_id);
    // Enhanced context for Claude
    const enhancedContext = {
      goal,
      documentType,
      vectorContext,
      userContext: user_context,
      sessionContext: session.context,
      messageCount: messages.length,
      previousGoals: session.context?.previous_goals || [],
      lastMessage: message,
      conversationHistory: messages.slice(-3) // Last 3 messages for context
    };
    // Get Claude response
    const assistantResponse = await callClaudeAPI(messages, enhancedContext);
    // Add assistant response to messages
    messages.push({
      role: 'assistant',
      content: assistantResponse,
      timestamp: new Date().toISOString()
    });
    // Update session with enhanced context
    const updatedContext = {
      ...session.context,
      last_goal: goal,
      last_document_type: documentType,
      message_count: messages.length,
      updated_at: new Date().toISOString(),
      previous_goals: [
        ...(session.context?.previous_goals || []).slice(-5),
        goal
      ] // Keep last 5 goals
    };
    const { error: updateError } = await supabase.from('demo_sessions').update({
      messages,
      goal,
      context: updatedContext
    }).eq('session_id', session_id);
    if (updateError) throw updateError;
    // Log interaction with enhanced metadata
    await supabase.from('demo_interactions').insert({
      session_id,
      user_message: message,
      assistant_response: assistantResponse,
      detected_goal: goal,
      context: {
        vectorContext,
        userContext: user_context,
        documentType,
        enhanced: true
      },
      created_at: new Date().toISOString()
    });
    // Generate appropriate quick replies based on the conversation
    let quickReplies = getQuickReplies(goal, session.context);
    // Only modify quick replies for very specific document generation scenarios
    if (goal === 'document_generation' && assistantResponse.toLowerCase().includes('generate')) {
      // Only if AI actually says it will generate something
      if (assistantResponse.match(/I'll generate|I'll create|I'll draft|let me generate|let me create/i)) {
        quickReplies = [
          "📄 Yes, please generate it",
          "📝 Tell me more first",
          "❓ What information do you need?",
          "↩️ Actually, I need something else"
        ];
      }
    }
    const response = {
      response: assistantResponse,
      session_id,
      goal,
      document_type: documentType,
      quick_replies: quickReplies,
      context: {
        message_count: messages.length,
        detected_goal: goal,
        detected_document_type: documentType,
        has_vector_context: !!vectorContext,
        should_generate_document: goal === 'document_generation' // FLAG for frontend
      }
    };
    return new Response(JSON.stringify(response), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Error in query-agent:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: error.message
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});
