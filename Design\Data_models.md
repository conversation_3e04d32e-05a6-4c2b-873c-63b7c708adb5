# ArionComply Demo - Data Models Specification

**Version:** v1.0  
**Date:** 2025-07-14  
**Purpose:** Define all database entities, relationships, and data flows for the ArionComply demo application

---

## 🎯 Overview

The demo application tracks anonymous user sessions through a compliance consultation journey, from initial chat interaction to document preview and optional lead capture. All data is session-based with no user accounts required.

---

## 📊 Entity Relationship Diagram

```
demo_sessions (1) ────── (M) demo_interactions
     │
     ├─── (1) demo_intake_data
     │
     ├─── (M) demo_documents  
     │
     ├─── (0..1) demo_contacts
     │
     └─── (M) demo_browsing_logs
```

---

## 🗃️ Table Specifications

### 1. `demo_sessions`
**Purpose:** Track individual user sessions and overall journey state

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique session identifier |
| `created_at` | TIMESTAMPTZ | NOT NULL, DEFAULT NOW() | Session start time |
| `updated_at` | TIMESTAMPTZ | NOT NULL, DEFAULT NOW() | Last activity timestamp |
| `user_agent` | TEXT | NULL | Browser/device information |
| `referrer` | TEXT | NULL | Source page that led to demo |
| `ip_address` | INET | NULL | Client IP (for analytics, not tracking) |
| `goal_detected` | TEXT | NULL | Detected user goal (iso_certification, gdpr_audit, etc.) |
| `goal_confidence` | DECIMAL(3,2) | NULL, CHECK >= 0 AND <= 1 | Confidence score for goal detection |
| `intake_completed` | BOOLEAN | NOT NULL, DEFAULT FALSE | Whether intake questions finished |
| `documents_generated` | BOOLEAN | NOT NULL, DEFAULT FALSE | Whether documents have been created |
| `preview_ready` | BOOLEAN | NOT NULL, DEFAULT FALSE | Whether user can view documents |
| `lead_captured` | BOOLEAN | NOT NULL, DEFAULT FALSE | Whether contact info was provided |
| `session_tags` | TEXT[] | NULL | Arbitrary tags for categorization |
| `metadata` | JSONB | NULL | Additional session metadata |

**Indexes:**
- `idx_sessions_created_at` ON (created_at)
- `idx_sessions_goal` ON (goal_detected)
- `idx_sessions_status` ON (intake_completed, documents_generated, preview_ready)

---

### 2. `demo_interactions`
**Purpose:** Store all chat messages between user and assistant

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique interaction ID |
| `session_id` | UUID | NOT NULL, REFERENCES demo_sessions(id) ON DELETE CASCADE | Parent session |
| `created_at` | TIMESTAMPTZ | NOT NULL, DEFAULT NOW() | Message timestamp |
| `message_type` | TEXT | NOT NULL, CHECK IN ('user', 'assistant', 'system') | Who sent the message |
| `content` | TEXT | NOT NULL | Message content |
| `intent_detected` | TEXT | NULL | Detected user intent |
| `goal_context` | TEXT | NULL | Goal context at time of message |
| `quick_replies` | TEXT[] | NULL | Quick reply options provided |
| `action_triggered` | TEXT | NULL | Any special actions triggered |
| `response_time_ms` | INTEGER | NULL | Assistant response time |
| `tokens_used` | INTEGER | NULL | LLM tokens consumed |
| `metadata` | JSONB | NULL | Additional interaction data |

**Indexes:**
- `idx_interactions_session` ON (session_id, created_at)
- `idx_interactions_type` ON (message_type)
- `idx_interactions_intent` ON (intent_detected)

---

### 3. `demo_intake_data`
**Purpose:** Store user responses to onboarding questions

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `session_id` | UUID | PRIMARY KEY, REFERENCES demo_sessions(id) ON DELETE CASCADE | Parent session |
| `goal_type` | TEXT | NOT NULL | Goal this intake relates to |
| `created_at` | TIMESTAMPTZ | NOT NULL, DEFAULT NOW() | When intake started |
| `completed_at` | TIMESTAMPTZ | NULL | When intake finished |
| `company_size` | TEXT | NULL | Organization size category |
| `industry` | TEXT | NULL | Industry vertical |
| `infrastructure_type` | TEXT | NULL | Cloud/on-prem/hybrid |
| `key_assets` | TEXT[] | NULL | Assets to protect |
| `existing_policies` | TEXT[] | NULL | Current compliance status |
| `data_processing_types` | TEXT[] | NULL | Types of personal data processed |
| `legal_basis` | TEXT[] | NULL | GDPR legal basis for processing |
| `data_transfers` | TEXT | NULL | International data transfer info |
| `ai_systems` | TEXT[] | NULL | AI systems in use |
| `risk_appetite` | TEXT | NULL | Risk tolerance level |
| `custom_responses` | JSONB | NULL | Additional question responses |
| `completion_percentage` | DECIMAL(3,2) | DEFAULT 0, CHECK >= 0 AND <= 1 | Intake completion rate |

**Indexes:**
- `idx_intake_goal_type` ON (goal_type)
- `idx_intake_completion` ON (completion_percentage)

---

### 4. `demo_documents`
**Purpose:** Generated compliance documents for preview

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique document ID |
| `session_id` | UUID | NOT NULL, REFERENCES demo_sessions(id) ON DELETE CASCADE | Parent session |
| `created_at` | TIMESTAMPTZ | NOT NULL, DEFAULT NOW() | Generation timestamp |
| `document_type` | TEXT | NOT NULL | Type (risk_register, soa, policy, etc.) |
| `title` | TEXT | NOT NULL | Document display title |
| `content` | TEXT | NOT NULL | Full document content |
| `sections` | JSONB | NULL | Structured document sections |
| `metadata` | JSONB | NULL | Generation parameters, templates used |
| `page_count` | INTEGER | DEFAULT 1 | Estimated page count |
| `word_count` | INTEGER | NULL | Content word count |
| `generation_method` | TEXT | NOT NULL | How document was created (template, ai, hybrid) |
| `generation_time_ms` | INTEGER | NULL | Time to generate |
| `preview_count` | INTEGER | DEFAULT 0 | How many times viewed |
| `last_viewed_at` | TIMESTAMPTZ | NULL | Last preview timestamp |

**Indexes:**
- `idx_documents_session` ON (session_id, document_type)
- `idx_documents_type` ON (document_type)
- `idx_documents_created` ON (created_at)

---

### 5. `demo_contacts`
**Purpose:** Lead capture information with full contact details

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique contact ID |
| `session_id` | UUID | NOT NULL, REFERENCES demo_sessions(id) ON DELETE CASCADE | Parent session |
| `created_at` | TIMESTAMPTZ | NOT NULL, DEFAULT NOW() | Contact capture timestamp |
| `first_name` | TEXT | NOT NULL | Contact first name |
| `last_name` | TEXT | NOT NULL | Contact last name |
| `email` | TEXT | NOT NULL, CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}

**Indexes:**
- `idx_contacts_email` ON (email)
- `idx_contacts_company_name` ON (company_name)
- `idx_contacts_created` ON (created_at)
- `idx_contacts_engagement` ON (engagement_score)
- `idx_contacts_retention` ON (data_retention_until)
- `idx_contacts_consent` ON (data_processing_consented, consent_timestamp)
- `idx_contacts_phone` ON (phone_number) WHERE phone_number IS NOT NULL

---

### 6. `demo_browsing_logs`
**Purpose:** Track user navigation and document viewing

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique log entry ID |
| `session_id` | UUID | NOT NULL, REFERENCES demo_sessions(id) ON DELETE CASCADE | Parent session |
| `created_at` | TIMESTAMPTZ | NOT NULL, DEFAULT NOW() | Event timestamp |
| `event_type` | TEXT | NOT NULL | Event type (page_view, document_view, etc.) |
| `page_url` | TEXT | NULL | Page URL visited |
| `document_id` | UUID | NULL, REFERENCES demo_documents(id) | Document viewed |
| `time_spent_seconds` | INTEGER | NULL | Time spent on page/document |
| `scroll_percentage` | DECIMAL(3,2) | NULL | How much content was scrolled |
| `referrer_url` | TEXT | NULL | Previous page |
| `user_agent` | TEXT | NULL | Browser information |
| `viewport_width` | INTEGER | NULL | Screen width |
| `viewport_height` | INTEGER | NULL | Screen height |
| `metadata` | JSONB | NULL | Additional event data |

**Indexes:**
- `idx_browsing_session` ON (session_id, created_at)
- `idx_browsing_event_type` ON (event_type)
- `idx_browsing_document` ON (document_id)

---

## 🔄 Data Lifecycle

### Session Flow
1. **Session Creation** → `demo_sessions` entry created with UUID
2. **Chat Interactions** → Multiple `demo_interactions` entries
3. **Goal Detection** → `demo_sessions.goal_detected` updated
4. **Intake Process** → `demo_intake_data` populated
5. **Document Generation** → `demo_documents` entries created
6. **Preview Activity** → `demo_browsing_logs` entries
7. **Lead Capture** → Optional `demo_contacts` entry

### Data Retention
- **Active Sessions:** Retained indefinitely while in use
- **Completed Sessions (No Contact):** 30 days retention
- **Completed Sessions (With Contact):** 2 years retention
- **Analytics Data:** Aggregated and anonymized after 6 months

---

## 🔒 Security & Privacy

## 🔒 Security & Privacy

### Data Protection Measures
- **Encryption at Rest:** All PII encrypted using Supabase's built-in encryption
- **Encryption in Transit:** TLS 1.3 for all API communications
- **IP Address Hashing:** Raw IP addresses hashed with SHA-256 + salt
- **Session Isolation:** Cryptographically secure UUID generation
- **Input Sanitization:** All user inputs sanitized for XSS/SQL injection prevention
- **Rate Limiting:** API endpoints protected against abuse

### GDPR Compliance
- **Legal Basis:** Legitimate interest for demo functionality, consent for contact capture
- **Data Minimization:** Only essential data collected for stated purposes
- **Consent Management:** Explicit consent required for marketing communications
- **Right to Erasure:** Automated data deletion after retention period
- **Data Portability:** Export functionality for user data
- **Breach Notification:** Automated monitoring and alerting systems

### Access Controls
- **Row-Level Security (RLS):** Session-based data isolation
- **API Authentication:** JWT tokens for Edge Function access
- **Admin Access:** Separate admin roles for support/analytics
- **Audit Logging:** All data access logged for compliance
- **Geographic Restrictions:** EU/US access controls based on regulations

### Data Retention & Deletion
- **Demo Sessions (No Contact):** 30 days automatic deletion
- **Contact Data:** 2 years retention, then automatic deletion
- **Analytics Data:** Aggregated and anonymized after 6 months
- **Audit Logs:** 7 years retention for compliance
- **Right to Deletion:** Manual deletion capability for GDPR requests

### Security Monitoring
- **Failed Login Attempts:** Rate limiting and blocking
- **Suspicious Activity:** Automated detection and alerting
- **Data Export Attempts:** Logging and monitoring
- **API Abuse Detection:** Pattern recognition and blocking

---

## 📈 Analytics Views

### Key Metrics Tracked
- Session completion rates by goal type
- Average time from start to lead capture
- Document generation success rates
- Most effective conversation paths
- Device/browser analytics

### Aggregate Tables (Future)
- Daily session summaries
- Goal detection accuracy metrics
- Document engagement statistics
- Lead quality scores

---

## 🔧 Technical Implementation Notes

### UUID Generation
All UUIDs use `gen_random_uuid()` for cryptographic security

### Timestamps
All timestamps use `TIMESTAMPTZ` for timezone awareness

### JSON Validation
JSONB fields include check constraints for schema validation where appropriate

### Indexing Strategy
Indexes optimized for:
- Session-based queries (most common)
- Time-based analytics
- Goal/document type filtering
- Lead management workflows) | Email address (validated) |
| `phone_number` | TEXT | NULL, CHECK (phone_number ~* '^\+?[1-9]\d{1,14}

**Indexes:**
- `idx_contacts_email` ON (email)
- `idx_contacts_company` ON (company)
- `idx_contacts_created` ON (created_at)
- `idx_contacts_engagement` ON (engagement_score)

---

### 6. `demo_browsing_logs`
**Purpose:** Track user navigation and document viewing

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique log entry ID |
| `session_id` | UUID | NOT NULL, REFERENCES demo_sessions(id) ON DELETE CASCADE | Parent session |
| `created_at` | TIMESTAMPTZ | NOT NULL, DEFAULT NOW() | Event timestamp |
| `event_type` | TEXT | NOT NULL | Event type (page_view, document_view, etc.) |
| `page_url` | TEXT | NULL | Page URL visited |
| `document_id` | UUID | NULL, REFERENCES demo_documents(id) | Document viewed |
| `time_spent_seconds` | INTEGER | NULL | Time spent on page/document |
| `scroll_percentage` | DECIMAL(3,2) | NULL | How much content was scrolled |
| `referrer_url` | TEXT | NULL | Previous page |
| `user_agent` | TEXT | NULL | Browser information |
| `viewport_width` | INTEGER | NULL | Screen width |
| `viewport_height` | INTEGER | NULL | Screen height |
| `metadata` | JSONB | NULL | Additional event data |

**Indexes:**
- `idx_browsing_session` ON (session_id, created_at)
- `idx_browsing_event_type` ON (event_type)
- `idx_browsing_document` ON (document_id)

---

## 🔄 Data Lifecycle

### Session Flow
1. **Session Creation** → `demo_sessions` entry created with UUID
2. **Chat Interactions** → Multiple `demo_interactions` entries
3. **Goal Detection** → `demo_sessions.goal_detected` updated
4. **Intake Process** → `demo_intake_data` populated
5. **Document Generation** → `demo_documents` entries created
6. **Preview Activity** → `demo_browsing_logs` entries
7. **Lead Capture** → Optional `demo_contacts` entry

### Data Retention
- **Active Sessions:** Retained indefinitely while in use
- **Completed Sessions (No Contact):** 30 days retention
- **Completed Sessions (With Contact):** 2 years retention
- **Analytics Data:** Aggregated and anonymized after 6 months

---

## 🔒 Security & Privacy

### Data Protection
- No PII stored without explicit consent (lead capture)
- IP addresses hashed for analytics, not stored raw
- All JSONB fields sanitized for XSS prevention
- Row-level security (RLS) policies on all tables

### Access Controls
- Anonymous read/write only to own session data
- Admin access for aggregated analytics only
- API rate limiting by session and IP

---

## 📈 Analytics Views

### Key Metrics Tracked
- Session completion rates by goal type
- Average time from start to lead capture
- Document generation success rates
- Most effective conversation paths
- Device/browser analytics

### Aggregate Tables (Future)
- Daily session summaries
- Goal detection accuracy metrics
- Document engagement statistics
- Lead quality scores

---

## 🔧 Technical Implementation Notes

### UUID Generation
All UUIDs use `gen_random_uuid()` for cryptographic security

### Timestamps
All timestamps use `TIMESTAMPTZ` for timezone awareness

### JSON Validation
JSONB fields include check constraints for schema validation where appropriate

### Indexing Strategy
Indexes optimized for:
- Session-based queries (most common)
- Time-based analytics
- Goal/document type filtering
- Lead management workflows) | Phone number (E.164 format) |
| `company_name` | TEXT | NOT NULL | Company/organization name |
| `job_title` | TEXT | NULL | Job title/role |
| `interest_area` | TEXT | NULL | Primary compliance interest |
| `message` | TEXT | NULL | Additional feedback/message |
| `documents_viewed` | TEXT[] | NULL | Document types they previewed |
| `engagement_score` | DECIMAL(3,2) | NULL | Calculated engagement level |
| `follow_up_consented` | BOOLEAN | DEFAULT TRUE | Agreed to follow-up contact |
| `marketing_consented` | BOOLEAN | DEFAULT FALSE | Agreed to marketing communications |
| `data_processing_consented` | BOOLEAN | NOT NULL | GDPR: Consented to data processing |
| `consent_timestamp` | TIMESTAMPTZ | NOT NULL, DEFAULT NOW() | When consent was given |
| `consent_ip_address` | INET | NULL | IP address when consent given |
| `lead_source` | TEXT | NULL | How they found the demo |
| `utm_campaign` | TEXT | NULL | Marketing campaign tracking |
| `utm_source` | TEXT | NULL | UTM source tracking |
| `utm_medium` | TEXT | NULL | UTM medium tracking |
| `crm_synced` | BOOLEAN | DEFAULT FALSE | Whether synced to CRM |
| `crm_sync_at` | TIMESTAMPTZ | NULL | When CRM sync occurred |
| `data_retention_until` | DATE | NOT NULL, DEFAULT (CURRENT_DATE + INTERVAL '2 years') | Data retention deadline |

**Indexes:**
- `idx_contacts_email` ON (email)
- `idx_contacts_company` ON (company)
- `idx_contacts_created` ON (created_at)
- `idx_contacts_engagement` ON (engagement_score)

---

### 6. `demo_browsing_logs`
**Purpose:** Track user navigation and document viewing

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique log entry ID |
| `session_id` | UUID | NOT NULL, REFERENCES demo_sessions(id) ON DELETE CASCADE | Parent session |
| `created_at` | TIMESTAMPTZ | NOT NULL, DEFAULT NOW() | Event timestamp |
| `event_type` | TEXT | NOT NULL | Event type (page_view, document_view, etc.) |
| `page_url` | TEXT | NULL | Page URL visited |
| `document_id` | UUID | NULL, REFERENCES demo_documents(id) | Document viewed |
| `time_spent_seconds` | INTEGER | NULL | Time spent on page/document |
| `scroll_percentage` | DECIMAL(3,2) | NULL | How much content was scrolled |
| `referrer_url` | TEXT | NULL | Previous page |
| `user_agent` | TEXT | NULL | Browser information |
| `viewport_width` | INTEGER | NULL | Screen width |
| `viewport_height` | INTEGER | NULL | Screen height |
| `metadata` | JSONB | NULL | Additional event data |

**Indexes:**
- `idx_browsing_session` ON (session_id, created_at)
- `idx_browsing_event_type` ON (event_type)
- `idx_browsing_document` ON (document_id)

---

## 🔄 Data Lifecycle

### Session Flow
1. **Session Creation** → `demo_sessions` entry created with UUID
2. **Chat Interactions** → Multiple `demo_interactions` entries
3. **Goal Detection** → `demo_sessions.goal_detected` updated
4. **Intake Process** → `demo_intake_data` populated
5. **Document Generation** → `demo_documents` entries created
6. **Preview Activity** → `demo_browsing_logs` entries
7. **Lead Capture** → Optional `demo_contacts` entry

### Data Retention
- **Active Sessions:** Retained indefinitely while in use
- **Completed Sessions (No Contact):** 30 days retention
- **Completed Sessions (With Contact):** 2 years retention
- **Analytics Data:** Aggregated and anonymized after 6 months

---

## 🔒 Security & Privacy

### Data Protection
- No PII stored without explicit consent (lead capture)
- IP addresses hashed for analytics, not stored raw
- All JSONB fields sanitized for XSS prevention
- Row-level security (RLS) policies on all tables

### Access Controls
- Anonymous read/write only to own session data
- Admin access for aggregated analytics only
- API rate limiting by session and IP

---

## 📈 Analytics Views

### Key Metrics Tracked
- Session completion rates by goal type
- Average time from start to lead capture
- Document generation success rates
- Most effective conversation paths
- Device/browser analytics

### Aggregate Tables (Future)
- Daily session summaries
- Goal detection accuracy metrics
- Document engagement statistics
- Lead quality scores

---

## 🔧 Technical Implementation Notes

### UUID Generation
All UUIDs use `gen_random_uuid()` for cryptographic security

### Timestamps
All timestamps use `TIMESTAMPTZ` for timezone awareness

### JSON Validation
JSONB fields include check constraints for schema validation where appropriate

### Indexing Strategy
Indexes optimized for:
- Session-based queries (most common)
- Time-based analytics
- Goal/document type filtering
- Lead management workflows