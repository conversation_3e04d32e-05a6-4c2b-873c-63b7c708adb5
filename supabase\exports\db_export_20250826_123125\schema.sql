--
-- PostgreSQL database dump
--

\restrict 4JQuT4Mt1OeWVgCeOwjOQkKpulZQXEeJm6hasWj445k26x4WgAcE28hKLKQVLLM

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.6

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: auth; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA auth;


--
-- Name: extensions; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA extensions;


--
-- Name: graphql; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA graphql;


--
-- Name: graphql_public; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA graphql_public;


--
-- Name: pgbouncer; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA pgbouncer;


--
-- Name: realtime; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA realtime;


--
-- Name: storage; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA storage;


--
-- Name: vault; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA vault;


--
-- Name: pg_graphql; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_graphql WITH SCHEMA graphql;


--
-- Name: EXTENSION pg_graphql; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pg_graphql IS 'pg_graphql: GraphQL support';


--
-- Name: pg_stat_statements; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pg_stat_statements WITH SCHEMA extensions;


--
-- Name: EXTENSION pg_stat_statements; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pg_stat_statements IS 'track planning and execution statistics of all SQL statements executed';


--
-- Name: pgcrypto; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS pgcrypto WITH SCHEMA extensions;


--
-- Name: EXTENSION pgcrypto; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION pgcrypto IS 'cryptographic functions';


--
-- Name: supabase_vault; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS supabase_vault WITH SCHEMA vault;


--
-- Name: EXTENSION supabase_vault; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION supabase_vault IS 'Supabase Vault Extension';


--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA extensions;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: vector; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS vector WITH SCHEMA public;


--
-- Name: EXTENSION vector; Type: COMMENT; Schema: -; Owner: -
--

COMMENT ON EXTENSION vector IS 'vector data type and ivfflat and hnsw access methods';


--
-- Name: aal_level; Type: TYPE; Schema: auth; Owner: -
--

CREATE TYPE auth.aal_level AS ENUM (
    'aal1',
    'aal2',
    'aal3'
);


--
-- Name: code_challenge_method; Type: TYPE; Schema: auth; Owner: -
--

CREATE TYPE auth.code_challenge_method AS ENUM (
    's256',
    'plain'
);


--
-- Name: factor_status; Type: TYPE; Schema: auth; Owner: -
--

CREATE TYPE auth.factor_status AS ENUM (
    'unverified',
    'verified'
);


--
-- Name: factor_type; Type: TYPE; Schema: auth; Owner: -
--

CREATE TYPE auth.factor_type AS ENUM (
    'totp',
    'webauthn',
    'phone'
);


--
-- Name: one_time_token_type; Type: TYPE; Schema: auth; Owner: -
--

CREATE TYPE auth.one_time_token_type AS ENUM (
    'confirmation_token',
    'reauthentication_token',
    'recovery_token',
    'email_change_token_new',
    'email_change_token_current',
    'phone_change_token'
);


--
-- Name: action; Type: TYPE; Schema: realtime; Owner: -
--

CREATE TYPE realtime.action AS ENUM (
    'INSERT',
    'UPDATE',
    'DELETE',
    'TRUNCATE',
    'ERROR'
);


--
-- Name: equality_op; Type: TYPE; Schema: realtime; Owner: -
--

CREATE TYPE realtime.equality_op AS ENUM (
    'eq',
    'neq',
    'lt',
    'lte',
    'gt',
    'gte',
    'in'
);


--
-- Name: user_defined_filter; Type: TYPE; Schema: realtime; Owner: -
--

CREATE TYPE realtime.user_defined_filter AS (
	column_name text,
	op realtime.equality_op,
	value text
);


--
-- Name: wal_column; Type: TYPE; Schema: realtime; Owner: -
--

CREATE TYPE realtime.wal_column AS (
	name text,
	type_name text,
	type_oid oid,
	value jsonb,
	is_pkey boolean,
	is_selectable boolean
);


--
-- Name: wal_rls; Type: TYPE; Schema: realtime; Owner: -
--

CREATE TYPE realtime.wal_rls AS (
	wal jsonb,
	is_rls_enabled boolean,
	subscription_ids uuid[],
	errors text[]
);


--
-- Name: buckettype; Type: TYPE; Schema: storage; Owner: -
--

CREATE TYPE storage.buckettype AS ENUM (
    'STANDARD',
    'ANALYTICS'
);


--
-- Name: email(); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.email() RETURNS text
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.email', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'email')
  )::text
$$;


--
-- Name: FUNCTION email(); Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON FUNCTION auth.email() IS 'Deprecated. Use auth.jwt() -> ''email'' instead.';


--
-- Name: jwt(); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.jwt() RETURNS jsonb
    LANGUAGE sql STABLE
    AS $$
  select 
    coalesce(
        nullif(current_setting('request.jwt.claim', true), ''),
        nullif(current_setting('request.jwt.claims', true), '')
    )::jsonb
$$;


--
-- Name: role(); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.role() RETURNS text
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.role', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'role')
  )::text
$$;


--
-- Name: FUNCTION role(); Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON FUNCTION auth.role() IS 'Deprecated. Use auth.jwt() -> ''role'' instead.';


--
-- Name: uid(); Type: FUNCTION; Schema: auth; Owner: -
--

CREATE FUNCTION auth.uid() RETURNS uuid
    LANGUAGE sql STABLE
    AS $$
  select 
  coalesce(
    nullif(current_setting('request.jwt.claim.sub', true), ''),
    (nullif(current_setting('request.jwt.claims', true), '')::jsonb ->> 'sub')
  )::uuid
$$;


--
-- Name: FUNCTION uid(); Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON FUNCTION auth.uid() IS 'Deprecated. Use auth.jwt() -> ''sub'' instead.';


--
-- Name: grant_pg_cron_access(); Type: FUNCTION; Schema: extensions; Owner: -
--

CREATE FUNCTION extensions.grant_pg_cron_access() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  IF EXISTS (
    SELECT
    FROM pg_event_trigger_ddl_commands() AS ev
    JOIN pg_extension AS ext
    ON ev.objid = ext.oid
    WHERE ext.extname = 'pg_cron'
  )
  THEN
    grant usage on schema cron to postgres with grant option;

    alter default privileges in schema cron grant all on tables to postgres with grant option;
    alter default privileges in schema cron grant all on functions to postgres with grant option;
    alter default privileges in schema cron grant all on sequences to postgres with grant option;

    alter default privileges for user supabase_admin in schema cron grant all
        on sequences to postgres with grant option;
    alter default privileges for user supabase_admin in schema cron grant all
        on tables to postgres with grant option;
    alter default privileges for user supabase_admin in schema cron grant all
        on functions to postgres with grant option;

    grant all privileges on all tables in schema cron to postgres with grant option;
    revoke all on table cron.job from postgres;
    grant select on table cron.job to postgres with grant option;
  END IF;
END;
$$;


--
-- Name: FUNCTION grant_pg_cron_access(); Type: COMMENT; Schema: extensions; Owner: -
--

COMMENT ON FUNCTION extensions.grant_pg_cron_access() IS 'Grants access to pg_cron';


--
-- Name: grant_pg_graphql_access(); Type: FUNCTION; Schema: extensions; Owner: -
--

CREATE FUNCTION extensions.grant_pg_graphql_access() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $_$
DECLARE
    func_is_graphql_resolve bool;
BEGIN
    func_is_graphql_resolve = (
        SELECT n.proname = 'resolve'
        FROM pg_event_trigger_ddl_commands() AS ev
        LEFT JOIN pg_catalog.pg_proc AS n
        ON ev.objid = n.oid
    );

    IF func_is_graphql_resolve
    THEN
        -- Update public wrapper to pass all arguments through to the pg_graphql resolve func
        DROP FUNCTION IF EXISTS graphql_public.graphql;
        create or replace function graphql_public.graphql(
            "operationName" text default null,
            query text default null,
            variables jsonb default null,
            extensions jsonb default null
        )
            returns jsonb
            language sql
        as $$
            select graphql.resolve(
                query := query,
                variables := coalesce(variables, '{}'),
                "operationName" := "operationName",
                extensions := extensions
            );
        $$;

        -- This hook executes when `graphql.resolve` is created. That is not necessarily the last
        -- function in the extension so we need to grant permissions on existing entities AND
        -- update default permissions to any others that are created after `graphql.resolve`
        grant usage on schema graphql to postgres, anon, authenticated, service_role;
        grant select on all tables in schema graphql to postgres, anon, authenticated, service_role;
        grant execute on all functions in schema graphql to postgres, anon, authenticated, service_role;
        grant all on all sequences in schema graphql to postgres, anon, authenticated, service_role;
        alter default privileges in schema graphql grant all on tables to postgres, anon, authenticated, service_role;
        alter default privileges in schema graphql grant all on functions to postgres, anon, authenticated, service_role;
        alter default privileges in schema graphql grant all on sequences to postgres, anon, authenticated, service_role;

        -- Allow postgres role to allow granting usage on graphql and graphql_public schemas to custom roles
        grant usage on schema graphql_public to postgres with grant option;
        grant usage on schema graphql to postgres with grant option;
    END IF;

END;
$_$;


--
-- Name: FUNCTION grant_pg_graphql_access(); Type: COMMENT; Schema: extensions; Owner: -
--

COMMENT ON FUNCTION extensions.grant_pg_graphql_access() IS 'Grants access to pg_graphql';


--
-- Name: grant_pg_net_access(); Type: FUNCTION; Schema: extensions; Owner: -
--

CREATE FUNCTION extensions.grant_pg_net_access() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
  IF EXISTS (
    SELECT 1
    FROM pg_event_trigger_ddl_commands() AS ev
    JOIN pg_extension AS ext
    ON ev.objid = ext.oid
    WHERE ext.extname = 'pg_net'
  )
  THEN
    IF NOT EXISTS (
      SELECT 1
      FROM pg_roles
      WHERE rolname = 'supabase_functions_admin'
    )
    THEN
      CREATE USER supabase_functions_admin NOINHERIT CREATEROLE LOGIN NOREPLICATION;
    END IF;

    GRANT USAGE ON SCHEMA net TO supabase_functions_admin, postgres, anon, authenticated, service_role;

    IF EXISTS (
      SELECT FROM pg_extension
      WHERE extname = 'pg_net'
      -- all versions in use on existing projects as of 2025-02-20
      -- version 0.12.0 onwards don't need these applied
      AND extversion IN ('0.2', '0.6', '0.7', '0.7.1', '0.8', '0.10.0', '0.11.0')
    ) THEN
      ALTER function net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) SECURITY DEFINER;
      ALTER function net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) SECURITY DEFINER;

      ALTER function net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) SET search_path = net;
      ALTER function net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) SET search_path = net;

      REVOKE ALL ON FUNCTION net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) FROM PUBLIC;
      REVOKE ALL ON FUNCTION net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) FROM PUBLIC;

      GRANT EXECUTE ON FUNCTION net.http_get(url text, params jsonb, headers jsonb, timeout_milliseconds integer) TO supabase_functions_admin, postgres, anon, authenticated, service_role;
      GRANT EXECUTE ON FUNCTION net.http_post(url text, body jsonb, params jsonb, headers jsonb, timeout_milliseconds integer) TO supabase_functions_admin, postgres, anon, authenticated, service_role;
    END IF;
  END IF;
END;
$$;


--
-- Name: FUNCTION grant_pg_net_access(); Type: COMMENT; Schema: extensions; Owner: -
--

COMMENT ON FUNCTION extensions.grant_pg_net_access() IS 'Grants access to pg_net';


--
-- Name: pgrst_ddl_watch(); Type: FUNCTION; Schema: extensions; Owner: -
--

CREATE FUNCTION extensions.pgrst_ddl_watch() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  cmd record;
BEGIN
  FOR cmd IN SELECT * FROM pg_event_trigger_ddl_commands()
  LOOP
    IF cmd.command_tag IN (
      'CREATE SCHEMA', 'ALTER SCHEMA'
    , 'CREATE TABLE', 'CREATE TABLE AS', 'SELECT INTO', 'ALTER TABLE'
    , 'CREATE FOREIGN TABLE', 'ALTER FOREIGN TABLE'
    , 'CREATE VIEW', 'ALTER VIEW'
    , 'CREATE MATERIALIZED VIEW', 'ALTER MATERIALIZED VIEW'
    , 'CREATE FUNCTION', 'ALTER FUNCTION'
    , 'CREATE TRIGGER'
    , 'CREATE TYPE', 'ALTER TYPE'
    , 'CREATE RULE'
    , 'COMMENT'
    )
    -- don't notify in case of CREATE TEMP table or other objects created on pg_temp
    AND cmd.schema_name is distinct from 'pg_temp'
    THEN
      NOTIFY pgrst, 'reload schema';
    END IF;
  END LOOP;
END; $$;


--
-- Name: pgrst_drop_watch(); Type: FUNCTION; Schema: extensions; Owner: -
--

CREATE FUNCTION extensions.pgrst_drop_watch() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
  obj record;
BEGIN
  FOR obj IN SELECT * FROM pg_event_trigger_dropped_objects()
  LOOP
    IF obj.object_type IN (
      'schema'
    , 'table'
    , 'foreign table'
    , 'view'
    , 'materialized view'
    , 'function'
    , 'trigger'
    , 'type'
    , 'rule'
    )
    AND obj.is_temporary IS false -- no pg_temp objects
    THEN
      NOTIFY pgrst, 'reload schema';
    END IF;
  END LOOP;
END; $$;


--
-- Name: set_graphql_placeholder(); Type: FUNCTION; Schema: extensions; Owner: -
--

CREATE FUNCTION extensions.set_graphql_placeholder() RETURNS event_trigger
    LANGUAGE plpgsql
    AS $_$
    DECLARE
    graphql_is_dropped bool;
    BEGIN
    graphql_is_dropped = (
        SELECT ev.schema_name = 'graphql_public'
        FROM pg_event_trigger_dropped_objects() AS ev
        WHERE ev.schema_name = 'graphql_public'
    );

    IF graphql_is_dropped
    THEN
        create or replace function graphql_public.graphql(
            "operationName" text default null,
            query text default null,
            variables jsonb default null,
            extensions jsonb default null
        )
            returns jsonb
            language plpgsql
        as $$
            DECLARE
                server_version float;
            BEGIN
                server_version = (SELECT (SPLIT_PART((select version()), ' ', 2))::float);

                IF server_version >= 14 THEN
                    RETURN jsonb_build_object(
                        'errors', jsonb_build_array(
                            jsonb_build_object(
                                'message', 'pg_graphql extension is not enabled.'
                            )
                        )
                    );
                ELSE
                    RETURN jsonb_build_object(
                        'errors', jsonb_build_array(
                            jsonb_build_object(
                                'message', 'pg_graphql is only available on projects running Postgres 14 onwards.'
                            )
                        )
                    );
                END IF;
            END;
        $$;
    END IF;

    END;
$_$;


--
-- Name: FUNCTION set_graphql_placeholder(); Type: COMMENT; Schema: extensions; Owner: -
--

COMMENT ON FUNCTION extensions.set_graphql_placeholder() IS 'Reintroduces placeholder function for graphql_public.graphql';


--
-- Name: get_auth(text); Type: FUNCTION; Schema: pgbouncer; Owner: -
--

CREATE FUNCTION pgbouncer.get_auth(p_usename text) RETURNS TABLE(username text, password text)
    LANGUAGE plpgsql SECURITY DEFINER
    AS $_$
begin
    raise debug 'PgBouncer auth request: %', p_usename;

    return query
    select 
        rolname::text, 
        case when rolvaliduntil < now() 
            then null 
            else rolpassword::text 
        end 
    from pg_authid 
    where rolname=$1 and rolcanlogin;
end;
$_$;


--
-- Name: calculate_lead_score(uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.calculate_lead_score(user_uuid uuid) RETURNS integer
    LANGUAGE plpgsql STABLE
    AS $$
DECLARE
    score INTEGER DEFAULT 0;
    user_record RECORD;
BEGIN
    SELECT * INTO user_record FROM demo_users WHERE id = user_uuid;
    
    IF user_record.id IS NULL THEN
        RETURN 0;
    END IF;
    
    -- Base score for verified email
    IF user_record.email_verified THEN
        score := score + 10;
    END IF;
    
    -- Company size scoring
    CASE user_record.company_size
        WHEN '1-10' THEN score := score + 5;
        WHEN '11-50' THEN score := score + 10;
        WHEN '51-250' THEN score := score + 15;
        WHEN '251-1000' THEN score := score + 20;
        WHEN '1000+' THEN score := score + 25;
        ELSE score := score + 0;
    END CASE;
    
    -- Job title scoring
    IF user_record.job_title ILIKE '%CISO%' OR user_record.job_title ILIKE '%Chief%' THEN
        score := score + 25;
    ELSIF user_record.job_title ILIKE '%Compliance%' OR user_record.job_title ILIKE '%Security%' THEN
        score := score + 20;
    ELSIF user_record.job_title ILIKE '%Manager%' OR user_record.job_title ILIKE '%Director%' THEN
        score := score + 15;
    ELSE
        score := score + 5;
    END IF;
    
    -- Industry scoring
    IF user_record.industry IN ('Financial Services', 'Healthcare', 'Government') THEN
        score := score + 15;
    ELSIF user_record.industry IN ('Technology', 'Manufacturing') THEN
        score := score + 10;
    ELSE
        score := score + 5;
    END IF;
    
    -- Marketing consent bonus
    IF user_record.marketing_consent THEN
        score := score + 5;
    END IF;
    
    RETURN score;
END;
$$;


--
-- Name: cleanup_expired_admin_codes(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.cleanup_expired_admin_codes() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM admin_verification_codes 
    WHERE expires_at < NOW() - INTERVAL '1 hour'
    AND is_used = FALSE;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;


--
-- Name: cleanup_expired_verifications(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.cleanup_expired_verifications() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM demo_verification_requests 
    WHERE expires_at < NOW() - INTERVAL '7 days'
    AND is_used = FALSE;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;


--
-- Name: cleanup_old_demo_data(integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.cleanup_old_demo_data(retention_days integer DEFAULT 30) RETURNS integer
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete sessions older than retention period
    WITH deleted AS (
        DELETE FROM demo_sessions 
        WHERE created_at < NOW() - (retention_days || ' days')::INTERVAL
        RETURNING 1
    )
    SELECT COUNT(*) INTO deleted_count FROM deleted;
    
    RETURN deleted_count;
END;
$$;


--
-- Name: FUNCTION cleanup_old_demo_data(retention_days integer); Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON FUNCTION public.cleanup_old_demo_data(retention_days integer) IS 'Removes demo data older than specified retention period';


--
-- Name: cleanup_old_sessions(integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.cleanup_old_sessions(days_old integer DEFAULT 30) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM demo_sessions 
    WHERE created_at < NOW() - INTERVAL '1 day' * days_old
    AND status != 'lead_captured';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;


--
-- Name: cleanup_unverified_registrations(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.cleanup_unverified_registrations() RETURNS integer
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete unverified registrations older than 7 days
    DELETE FROM public.product_registrations 
    WHERE email_verified = FALSE 
    AND user_id IS NULL
    AND created_at < timezone('utc', now()) - INTERVAL '7 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$;


--
-- Name: complete_email_verification(text, uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.complete_email_verification(verification_token text, verified_session_id uuid) RETURNS TABLE(success boolean, user_id uuid, email text, error_message text)
    LANGUAGE plpgsql
    AS $$
DECLARE
    verification_record RECORD;
    new_user_id UUID;
    user_data_json JSONB;
BEGIN
    -- Get verification record
    SELECT * INTO verification_record
    FROM demo_verification_requests
    WHERE demo_verification_requests.verification_token = complete_email_verification.verification_token
    AND is_used = FALSE
    AND expires_at > NOW();
    
    -- Check if verification record exists and is valid
    IF verification_record.id IS NULL THEN
        RETURN QUERY SELECT FALSE, NULL::UUID, NULL::TEXT, 'Invalid or expired verification token';
        RETURN;
    END IF;
    
    -- Extract user data
    user_data_json := verification_record.user_data;
    
    -- Create or update user record
    INSERT INTO demo_users (
        first_name,
        last_name,
        full_name,
        email,
        company,
        job_title,
        company_size,
        industry,
        compliance_needs,
        email_verified,
        verified_at,
        verification_token,
        agreed_to_terms,
        marketing_consent,
        registration_source,
        user_agent,
        referrer,
        demo_session_id
    ) VALUES (
        user_data_json->>'firstName',
        user_data_json->>'lastName',
        (user_data_json->>'firstName') || ' ' || (user_data_json->>'lastName'),
        verification_record.email,
        user_data_json->>'company',
        user_data_json->>'jobTitle',
        user_data_json->>'companySize',
        COALESCE(user_data_json->>'industry', 'Technology'),
        user_data_json->>'complianceInterest',
        TRUE,
        NOW(),
        verification_record.verification_token,
        TRUE,
        COALESCE((user_data_json->>'marketingConsent')::BOOLEAN, FALSE),
        COALESCE(user_data_json->>'requestSource', 'email_verification'),
        user_data_json->>'userAgent',
        user_data_json->>'referrer',
        verified_session_id
    ) 
    ON CONFLICT (email) DO UPDATE SET
        email_verified = TRUE,
        verified_at = NOW(),
        verification_token = verification_record.verification_token,
        demo_session_id = verified_session_id,
        updated_at = NOW()
    RETURNING id INTO new_user_id;
    
    -- Mark verification request as used
    UPDATE demo_verification_requests
    SET 
        is_used = TRUE,
        used_at = NOW(),
        session_id = verified_session_id,
        updated_at = NOW()
    WHERE demo_verification_requests.verification_token = complete_email_verification.verification_token;
    
    -- Update session with user reference
    UPDATE demo_sessions
    SET 
        user_id = new_user_id,
        user_email = verification_record.email,
        registration_required = FALSE,
        status = 'verified',
        updated_at = NOW()
    WHERE id = verified_session_id;
    
    -- Calculate and update lead score
    UPDATE demo_users 
    SET lead_score = calculate_lead_score(new_user_id)
    WHERE id = new_user_id;
    
    RETURN QUERY SELECT TRUE, new_user_id, verification_record.email, NULL::TEXT;
END;
$$;


--
-- Name: get_admin_session_info(uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_admin_session_info(session_uuid uuid) RETURNS TABLE(session_id uuid, user_email text, user_type text, admin_role text, admin_permissions text[], is_valid boolean)
    LANGUAGE plpgsql STABLE
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.id as session_id,
        s.user_email,
        s.user_type,
        s.admin_role,
        u.admin_permissions,
        CASE 
            WHEN s.user_type = 'admin' AND u.user_type = 'admin' THEN TRUE
            ELSE FALSE
        END as is_valid
    FROM demo_sessions s
    LEFT JOIN demo_users u ON s.user_email = u.email
    WHERE s.id = session_uuid;
END;
$$;


--
-- Name: get_contextual_prompt(character varying); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_contextual_prompt(prompt_id character varying) RETURNS TABLE(id character varying, name character varying, description text, system_prompt text, response_guidelines text[], tone character varying, max_tokens integer, temperature double precision, forbidden_topics text[], required_elements text[], format_instructions text, example_responses text[])
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- Increment usage count
    UPDATE contextual_prompts 
    SET usage_count = usage_count + 1,
        updated_at = NOW()
    WHERE contextual_prompts.id = prompt_id AND is_active = true;
    
    -- Return prompt data
    RETURN QUERY
    SELECT 
        p.id, p.name, p.description, p.system_prompt,
        p.response_guidelines, p.tone, p.max_tokens, p.temperature,
        p.forbidden_topics, p.required_elements, p.format_instructions,
        p.example_responses
    FROM contextual_prompts p
    WHERE p.id = prompt_id AND p.is_active = true;
END;
$$;


--
-- Name: get_response_template(text, text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_response_template(intent_name text, category_name text) RETURNS TABLE(id uuid, template_format text, required_variables text[], followup_suggestions text[])
    LANGUAGE sql STABLE
    AS $$
    SELECT
        rt.id,
        rt.template_format,
        rt.required_variables,
        rt.followup_suggestions
    FROM response_templates rt
    WHERE 
        rt.intent_name = get_response_template.intent_name
        AND rt.category_name = get_response_template.category_name
        AND rt.status = 'active'
    ORDER BY rt.priority DESC
    LIMIT 1;
$$;


--
-- Name: get_session_stats(uuid); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_session_stats(session_uuid uuid) RETURNS jsonb
    LANGUAGE plpgsql STABLE
    AS $$
DECLARE
    result JSONB;
BEGIN
    SELECT jsonb_build_object(
        'session_id', s.id,
        'status', s.status,
        'interaction_count', s.interaction_count,
        'document_count', s.document_count,
        'contact_captured', s.contact_captured,
        'created_at', s.created_at,
        'last_interaction', s.last_interaction,
        'detected_goal', s.detected_goal,
        'user_id', s.user_id,
        'user_email', s.user_email
    ) INTO result
    FROM demo_sessions s
    WHERE s.id = session_uuid;
    
    RETURN COALESCE(result, '{}'::jsonb);
END;
$$;


--
-- Name: get_verification_metrics(integer); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_verification_metrics(days_back integer DEFAULT 7) RETURNS TABLE(date date, requests integer, verified integer, verification_rate numeric)
    LANGUAGE sql STABLE
    AS $$
    SELECT 
        DATE(created_at),
        COUNT(*)::INTEGER,
        COUNT(*) FILTER (WHERE is_used = TRUE)::INTEGER,
        ROUND(
            (COUNT(*) FILTER (WHERE is_used = TRUE) * 100.0 / COUNT(*)), 2
        )
    FROM demo_verification_requests
    WHERE created_at >= CURRENT_DATE - INTERVAL '1 day' * days_back
    GROUP BY DATE(created_at)
    ORDER BY DATE(created_at) DESC;
$$;


--
-- Name: get_verification_status(text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.get_verification_status(user_email text) RETURNS TABLE(has_verified_user boolean, has_pending_request boolean, pending_expired boolean, can_resend boolean, last_request_time timestamp with time zone)
    LANGUAGE plpgsql STABLE
    AS $$
DECLARE
    verified_user_exists BOOLEAN DEFAULT FALSE;
    pending_request_record RECORD;
    now_time TIMESTAMPTZ DEFAULT NOW();
BEGIN
    -- Check if verified user exists
    SELECT EXISTS(
        SELECT 1 FROM demo_users 
        WHERE email = user_email AND email_verified = TRUE
    ) INTO verified_user_exists;
    
    -- Get most recent pending verification request
    SELECT * INTO pending_request_record
    FROM demo_verification_requests
    WHERE email = user_email AND is_used = FALSE
    ORDER BY created_at DESC
    LIMIT 1;
    
    -- Return status information
    RETURN QUERY SELECT
        verified_user_exists,
        pending_request_record.id IS NOT NULL,
        CASE 
            WHEN pending_request_record.id IS NOT NULL 
            THEN now_time > pending_request_record.expires_at
            ELSE FALSE
        END,
        CASE
            WHEN pending_request_record.id IS NULL THEN TRUE
            WHEN now_time > pending_request_record.expires_at THEN TRUE
            WHEN (now_time - pending_request_record.created_at) > INTERVAL '2 minutes' THEN TRUE
            ELSE FALSE
        END,
        pending_request_record.created_at;
END;
$$;


--
-- Name: link_registration_with_auth_user(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.link_registration_with_auth_user() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
    -- Link any pending registrations with the same email to this auth user
    UPDATE public.product_registrations 
    SET 
        user_id = NEW.id,
        email_verified = COALESCE(NEW.email_confirmed_at IS NOT NULL, FALSE),
        email_verified_at = NEW.email_confirmed_at,
        status = CASE 
            WHEN NEW.email_confirmed_at IS NOT NULL AND status = 'pending_verification' THEN 'verified'
            ELSE status
        END,
        updated_at = timezone('utc', now())
    WHERE LOWER(email) = LOWER(NEW.email) 
    AND user_id IS NULL
    AND email_verified = FALSE;
    
    RETURN NEW;
END;
$$;


--
-- Name: log_admin_activity(text, text, jsonb); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.log_admin_activity(admin_email text, activity_type text, activity_data jsonb DEFAULT '{}'::jsonb) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO demo_interactions (
        session_id,
        event_type,
        event_data,
        created_at
    )
    SELECT 
        ds.id,
        activity_type,
        jsonb_build_object(
            'admin_email', admin_email,
            'admin_role', du.admin_role,
            'timestamp', NOW(),
            'activity_data', activity_data
        ),
        NOW()
    FROM demo_users du
    LEFT JOIN demo_sessions ds ON du.demo_session_id = ds.id
    WHERE du.email = admin_email AND du.user_type = 'admin';
END;
$$;


--
-- Name: log_prompt_usage(character varying, character varying, text, character varying, double precision, text, integer, jsonb); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.log_prompt_usage(p_prompt_id character varying, p_session_id character varying, p_user_message text, p_detected_goal character varying, p_goal_confidence double precision, p_ai_response text, p_response_time_ms integer, p_token_usage jsonb) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO prompt_usage_logs (
        prompt_id, session_id, user_message, detected_goal,
        goal_confidence, ai_response, response_time_ms, token_usage
    ) VALUES (
        p_prompt_id, p_session_id, p_user_message, p_detected_goal,
        p_goal_confidence, p_ai_response, p_response_time_ms, p_token_usage
    );
END;
$$;


--
-- Name: match_documents(public.vector, double precision, integer, jsonb); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.match_documents(query_embedding public.vector, match_threshold double precision DEFAULT 0.7, match_count integer DEFAULT 5, filter jsonb DEFAULT '{}'::jsonb) RETURNS TABLE(id uuid, title character varying, content text, category character varying, similarity double precision)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT
        knowledge_documents.id,
        knowledge_documents.title,
        knowledge_documents.content,
        knowledge_documents.category,
        1 - (knowledge_documents.embedding <=> query_embedding) AS similarity
    FROM knowledge_documents
    WHERE 
        knowledge_documents.is_active = true
        AND (filter = '{}' OR knowledge_documents.metadata @> filter)
        AND 1 - (knowledge_documents.embedding <=> query_embedding) > match_threshold
    ORDER BY knowledge_documents.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;


--
-- Name: normalize_registration_data(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.normalize_registration_data() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- Normalize email to lowercase
    NEW.email = LOWER(TRIM(NEW.email));
    
    -- Trim whitespace from text fields
    NEW.full_name = TRIM(NEW.full_name);
    NEW.company = TRIM(NEW.company);
    NEW.job_title = TRIM(NEW.job_title);
    NEW.phone = TRIM(NEW.phone);
    NEW.use_case = TRIM(NEW.use_case);
    
    -- Clean source URL
    IF NEW.source_url IS NOT NULL THEN
        NEW.source_url = TRIM(NEW.source_url);
        IF NEW.source_url = '' THEN
            NEW.source_url = NULL;
        END IF;
    END IF;
    
    -- For new registrations, set initial status
    IF TG_OP = 'INSERT' THEN
        NEW.status = 'pending_verification';
        NEW.email_verified = FALSE;
    END IF;
    
    RETURN NEW;
END;
$$;


--
-- Name: notify_admin_login(text); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.notify_admin_login(admin_email text) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- Log admin login
    PERFORM log_admin_activity(
        admin_email,
        'admin_login',
        jsonb_build_object(
            'login_time', NOW(),
            'user_agent', current_setting('request.headers', true)::jsonb->>'user-agent'
        )
    );
    
    -- Update last login time
    UPDATE demo_users 
    SET last_login = NOW()
    WHERE email = admin_email AND user_type = 'admin';
END;
$$;


--
-- Name: refresh_daily_session_summary(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.refresh_daily_session_summary() RETURNS void
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY daily_session_summary;
END;
$$;


--
-- Name: search_faqs(text, public.vector, text, integer, double precision); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.search_faqs(query_text text, query_embedding public.vector, category_filter text DEFAULT NULL::text, limit_results integer DEFAULT 3, similarity_threshold double precision DEFAULT 0.75) RETURNS TABLE(id uuid, question text, answer text, category text, similarity_score double precision, helpful_votes integer)
    LANGUAGE sql STABLE
    AS $$
    SELECT
        faq.id,
        faq.question,
        faq.answer,
        faq.category,
        CASE 
            WHEN faq.embedding IS NOT NULL THEN (1 - (faq.embedding <=> query_embedding))::FLOAT
            ELSE 0.5::FLOAT
        END as similarity_score,
        faq.helpful_votes
    FROM faq_items faq
    WHERE 
        faq.status = 'published'
        AND (category_filter IS NULL OR faq.category = category_filter)
        AND (
            faq.embedding IS NULL 
            OR (1 - (faq.embedding <=> query_embedding)) > similarity_threshold
        )
    ORDER BY 
        CASE 
            WHEN faq.embedding IS NOT NULL THEN faq.embedding <=> query_embedding
            ELSE 0.5
        END,
        faq.helpful_votes DESC
    LIMIT limit_results;
$$;


--
-- Name: search_knowledge_base(text, public.vector, text, integer, double precision); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.search_knowledge_base(query_text text, query_embedding public.vector, category_filter text DEFAULT NULL::text, limit_results integer DEFAULT 5, similarity_threshold double precision DEFAULT 0.7) RETURNS TABLE(id uuid, title text, content text, summary text, category text, similarity_score double precision)
    LANGUAGE sql STABLE
    AS $$
    SELECT
        kb.id,
        kb.title,
        kb.content,
        kb.summary,
        kb.category,
        CASE 
            WHEN kb.embedding IS NOT NULL THEN (1 - (kb.embedding <=> query_embedding))::FLOAT
            ELSE 0.5::FLOAT
        END as similarity_score
    FROM knowledge_base kb
    WHERE 
        kb.status = 'published'
        AND (category_filter IS NULL OR kb.category = category_filter)
        AND (
            kb.embedding IS NULL 
            OR (1 - (kb.embedding <=> query_embedding)) > similarity_threshold
        )
    ORDER BY 
        CASE 
            WHEN kb.embedding IS NOT NULL THEN kb.embedding <=> query_embedding
            ELSE 0.5
        END
    LIMIT limit_results;
$$;


--
-- Name: search_knowledge_base_enhanced(text, public.vector, text, jsonb, integer, double precision); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.search_knowledge_base_enhanced(query_text text, query_embedding public.vector DEFAULT NULL::public.vector, category_filter text DEFAULT NULL::text, user_context jsonb DEFAULT '{}'::jsonb, limit_results integer DEFAULT 5, similarity_threshold double precision DEFAULT 0.7) RETURNS TABLE(id uuid, title text, content text, summary text, category text, similarity_score double precision, relevance_boost double precision)
    LANGUAGE sql STABLE
    AS $$
    WITH context_boost AS (
        SELECT 
            kb.id,
            kb.title,
            kb.content,
            kb.summary,
            kb.category,
            CASE 
                WHEN kb.embedding IS NOT NULL AND query_embedding IS NOT NULL 
                THEN (1 - (kb.embedding <=> query_embedding))::FLOAT
                ELSE 0.5::FLOAT
            END as base_similarity,
            -- Boost based on user context
            CASE 
                WHEN user_context->>'complianceInterest' = 'ISO 27001' AND kb.category = 'iso_27001' THEN 0.2
                WHEN user_context->>'complianceInterest' = 'GDPR' AND kb.category = 'gdpr_compliance' THEN 0.2
                WHEN user_context->>'complianceInterest' = 'AI Governance' AND kb.category = 'ai_governance' THEN 0.2
                WHEN user_context->>'jobTitle' IN ('CISO', 'Compliance Manager') AND kb.category IN ('iso_27001', 'gdpr_compliance') THEN 0.1
                ELSE 0.0
            END as context_boost
        FROM knowledge_base kb
        WHERE 
            kb.status = 'published'
            AND (category_filter IS NULL OR kb.category = category_filter)
    )
    SELECT
        cb.id,
        cb.title,
        cb.content,
        cb.summary,
        cb.category,
        cb.base_similarity,
        cb.context_boost
    FROM context_boost cb
    WHERE cb.base_similarity + cb.context_boost > similarity_threshold
    ORDER BY (cb.base_similarity + cb.context_boost) DESC
    LIMIT limit_results;
$$;


--
-- Name: sync_email_verification_status(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.sync_email_verification_status() RETURNS trigger
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
    -- Update registration when auth user email is verified
    IF NEW.email_confirmed_at IS NOT NULL AND (OLD.email_confirmed_at IS NULL OR OLD.email_confirmed_at != NEW.email_confirmed_at) THEN
        UPDATE public.product_registrations 
        SET 
            email_verified = TRUE,
            email_verified_at = NEW.email_confirmed_at,
            status = CASE 
                WHEN status = 'pending_verification' THEN 'verified'
                ELSE status
            END,
            updated_at = timezone('utc', now())
        WHERE user_id = NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$;


--
-- Name: update_contact_tracking(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_contact_tracking() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- If status changed to 'contacted', update tracking fields
    IF OLD.status != 'contacted' AND NEW.status = 'contacted' THEN
        NEW.last_contacted = timezone('utc', now());
        NEW.contact_count = COALESCE(OLD.contact_count, 0) + 1;
    END IF;
    
    RETURN NEW;
END;
$$;


--
-- Name: update_goal_analytics(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_goal_analytics() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO goal_analytics (
        date,
        goal_type,
        detection_count,
        avg_confidence,
        successful_responses,
        avg_response_time_ms
    )
    SELECT
        CURRENT_DATE,
        detected_goal,
        COUNT(*),
        AVG(goal_confidence),
        COUNT(*) FILTER (WHERE error_occurred = false),
        AVG(response_time_ms)
    FROM interaction_logs
    WHERE 
        DATE(timestamp) = CURRENT_DATE
        AND detected_goal IS NOT NULL
    GROUP BY detected_goal
    ON CONFLICT (date, goal_type) DO UPDATE SET
        detection_count = EXCLUDED.detection_count,
        avg_confidence = EXCLUDED.avg_confidence,
        successful_responses = EXCLUDED.successful_responses,
        avg_response_time_ms = EXCLUDED.avg_response_time_ms;
END;
$$;


--
-- Name: update_prompt_performance_metrics(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_prompt_performance_metrics() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO prompt_performance_metrics (
        prompt_id,
        date,
        usage_count,
        avg_response_time_ms,
        avg_token_usage,
        avg_user_rating,
        success_rate
    )
    SELECT
        prompt_id,
        CURRENT_DATE,
        COUNT(*),
        AVG(response_time_ms),
        AVG((token_usage->>'total_tokens')::INTEGER),
        AVG(user_satisfaction_rating),
        COUNT(*) FILTER (WHERE ai_response IS NOT NULL)::FLOAT / COUNT(*) * 100
    FROM prompt_usage_logs
    WHERE 
        DATE(timestamp) = CURRENT_DATE
        AND prompt_id IS NOT NULL
    GROUP BY prompt_id
    ON CONFLICT (prompt_id, date) DO UPDATE SET
        usage_count = EXCLUDED.usage_count,
        avg_response_time_ms = EXCLUDED.avg_response_time_ms,
        avg_token_usage = EXCLUDED.avg_token_usage,
        avg_user_rating = EXCLUDED.avg_user_rating,
        success_rate = EXCLUDED.success_rate;
END;
$$;


--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = timezone('utc', now());
    RETURN NEW;
END;
$$;


--
-- Name: apply_rls(jsonb, integer); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.apply_rls(wal jsonb, max_record_bytes integer DEFAULT (1024 * 1024)) RETURNS SETOF realtime.wal_rls
    LANGUAGE plpgsql
    AS $$
declare
-- Regclass of the table e.g. public.notes
entity_ regclass = (quote_ident(wal ->> 'schema') || '.' || quote_ident(wal ->> 'table'))::regclass;

-- I, U, D, T: insert, update ...
action realtime.action = (
    case wal ->> 'action'
        when 'I' then 'INSERT'
        when 'U' then 'UPDATE'
        when 'D' then 'DELETE'
        else 'ERROR'
    end
);

-- Is row level security enabled for the table
is_rls_enabled bool = relrowsecurity from pg_class where oid = entity_;

subscriptions realtime.subscription[] = array_agg(subs)
    from
        realtime.subscription subs
    where
        subs.entity = entity_;

-- Subscription vars
roles regrole[] = array_agg(distinct us.claims_role::text)
    from
        unnest(subscriptions) us;

working_role regrole;
claimed_role regrole;
claims jsonb;

subscription_id uuid;
subscription_has_access bool;
visible_to_subscription_ids uuid[] = '{}';

-- structured info for wal's columns
columns realtime.wal_column[];
-- previous identity values for update/delete
old_columns realtime.wal_column[];

error_record_exceeds_max_size boolean = octet_length(wal::text) > max_record_bytes;

-- Primary jsonb output for record
output jsonb;

begin
perform set_config('role', null, true);

columns =
    array_agg(
        (
            x->>'name',
            x->>'type',
            x->>'typeoid',
            realtime.cast(
                (x->'value') #>> '{}',
                coalesce(
                    (x->>'typeoid')::regtype, -- null when wal2json version <= 2.4
                    (x->>'type')::regtype
                )
            ),
            (pks ->> 'name') is not null,
            true
        )::realtime.wal_column
    )
    from
        jsonb_array_elements(wal -> 'columns') x
        left join jsonb_array_elements(wal -> 'pk') pks
            on (x ->> 'name') = (pks ->> 'name');

old_columns =
    array_agg(
        (
            x->>'name',
            x->>'type',
            x->>'typeoid',
            realtime.cast(
                (x->'value') #>> '{}',
                coalesce(
                    (x->>'typeoid')::regtype, -- null when wal2json version <= 2.4
                    (x->>'type')::regtype
                )
            ),
            (pks ->> 'name') is not null,
            true
        )::realtime.wal_column
    )
    from
        jsonb_array_elements(wal -> 'identity') x
        left join jsonb_array_elements(wal -> 'pk') pks
            on (x ->> 'name') = (pks ->> 'name');

for working_role in select * from unnest(roles) loop

    -- Update `is_selectable` for columns and old_columns
    columns =
        array_agg(
            (
                c.name,
                c.type_name,
                c.type_oid,
                c.value,
                c.is_pkey,
                pg_catalog.has_column_privilege(working_role, entity_, c.name, 'SELECT')
            )::realtime.wal_column
        )
        from
            unnest(columns) c;

    old_columns =
            array_agg(
                (
                    c.name,
                    c.type_name,
                    c.type_oid,
                    c.value,
                    c.is_pkey,
                    pg_catalog.has_column_privilege(working_role, entity_, c.name, 'SELECT')
                )::realtime.wal_column
            )
            from
                unnest(old_columns) c;

    if action <> 'DELETE' and count(1) = 0 from unnest(columns) c where c.is_pkey then
        return next (
            jsonb_build_object(
                'schema', wal ->> 'schema',
                'table', wal ->> 'table',
                'type', action
            ),
            is_rls_enabled,
            -- subscriptions is already filtered by entity
            (select array_agg(s.subscription_id) from unnest(subscriptions) as s where claims_role = working_role),
            array['Error 400: Bad Request, no primary key']
        )::realtime.wal_rls;

    -- The claims role does not have SELECT permission to the primary key of entity
    elsif action <> 'DELETE' and sum(c.is_selectable::int) <> count(1) from unnest(columns) c where c.is_pkey then
        return next (
            jsonb_build_object(
                'schema', wal ->> 'schema',
                'table', wal ->> 'table',
                'type', action
            ),
            is_rls_enabled,
            (select array_agg(s.subscription_id) from unnest(subscriptions) as s where claims_role = working_role),
            array['Error 401: Unauthorized']
        )::realtime.wal_rls;

    else
        output = jsonb_build_object(
            'schema', wal ->> 'schema',
            'table', wal ->> 'table',
            'type', action,
            'commit_timestamp', to_char(
                ((wal ->> 'timestamp')::timestamptz at time zone 'utc'),
                'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'
            ),
            'columns', (
                select
                    jsonb_agg(
                        jsonb_build_object(
                            'name', pa.attname,
                            'type', pt.typname
                        )
                        order by pa.attnum asc
                    )
                from
                    pg_attribute pa
                    join pg_type pt
                        on pa.atttypid = pt.oid
                where
                    attrelid = entity_
                    and attnum > 0
                    and pg_catalog.has_column_privilege(working_role, entity_, pa.attname, 'SELECT')
            )
        )
        -- Add "record" key for insert and update
        || case
            when action in ('INSERT', 'UPDATE') then
                jsonb_build_object(
                    'record',
                    (
                        select
                            jsonb_object_agg(
                                -- if unchanged toast, get column name and value from old record
                                coalesce((c).name, (oc).name),
                                case
                                    when (c).name is null then (oc).value
                                    else (c).value
                                end
                            )
                        from
                            unnest(columns) c
                            full outer join unnest(old_columns) oc
                                on (c).name = (oc).name
                        where
                            coalesce((c).is_selectable, (oc).is_selectable)
                            and ( not error_record_exceeds_max_size or (octet_length((c).value::text) <= 64))
                    )
                )
            else '{}'::jsonb
        end
        -- Add "old_record" key for update and delete
        || case
            when action = 'UPDATE' then
                jsonb_build_object(
                        'old_record',
                        (
                            select jsonb_object_agg((c).name, (c).value)
                            from unnest(old_columns) c
                            where
                                (c).is_selectable
                                and ( not error_record_exceeds_max_size or (octet_length((c).value::text) <= 64))
                        )
                    )
            when action = 'DELETE' then
                jsonb_build_object(
                    'old_record',
                    (
                        select jsonb_object_agg((c).name, (c).value)
                        from unnest(old_columns) c
                        where
                            (c).is_selectable
                            and ( not error_record_exceeds_max_size or (octet_length((c).value::text) <= 64))
                            and ( not is_rls_enabled or (c).is_pkey ) -- if RLS enabled, we can't secure deletes so filter to pkey
                    )
                )
            else '{}'::jsonb
        end;

        -- Create the prepared statement
        if is_rls_enabled and action <> 'DELETE' then
            if (select 1 from pg_prepared_statements where name = 'walrus_rls_stmt' limit 1) > 0 then
                deallocate walrus_rls_stmt;
            end if;
            execute realtime.build_prepared_statement_sql('walrus_rls_stmt', entity_, columns);
        end if;

        visible_to_subscription_ids = '{}';

        for subscription_id, claims in (
                select
                    subs.subscription_id,
                    subs.claims
                from
                    unnest(subscriptions) subs
                where
                    subs.entity = entity_
                    and subs.claims_role = working_role
                    and (
                        realtime.is_visible_through_filters(columns, subs.filters)
                        or (
                          action = 'DELETE'
                          and realtime.is_visible_through_filters(old_columns, subs.filters)
                        )
                    )
        ) loop

            if not is_rls_enabled or action = 'DELETE' then
                visible_to_subscription_ids = visible_to_subscription_ids || subscription_id;
            else
                -- Check if RLS allows the role to see the record
                perform
                    -- Trim leading and trailing quotes from working_role because set_config
                    -- doesn't recognize the role as valid if they are included
                    set_config('role', trim(both '"' from working_role::text), true),
                    set_config('request.jwt.claims', claims::text, true);

                execute 'execute walrus_rls_stmt' into subscription_has_access;

                if subscription_has_access then
                    visible_to_subscription_ids = visible_to_subscription_ids || subscription_id;
                end if;
            end if;
        end loop;

        perform set_config('role', null, true);

        return next (
            output,
            is_rls_enabled,
            visible_to_subscription_ids,
            case
                when error_record_exceeds_max_size then array['Error 413: Payload Too Large']
                else '{}'
            end
        )::realtime.wal_rls;

    end if;
end loop;

perform set_config('role', null, true);
end;
$$;


--
-- Name: broadcast_changes(text, text, text, text, text, record, record, text); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.broadcast_changes(topic_name text, event_name text, operation text, table_name text, table_schema text, new record, old record, level text DEFAULT 'ROW'::text) RETURNS void
    LANGUAGE plpgsql
    AS $$
DECLARE
    -- Declare a variable to hold the JSONB representation of the row
    row_data jsonb := '{}'::jsonb;
BEGIN
    IF level = 'STATEMENT' THEN
        RAISE EXCEPTION 'function can only be triggered for each row, not for each statement';
    END IF;
    -- Check the operation type and handle accordingly
    IF operation = 'INSERT' OR operation = 'UPDATE' OR operation = 'DELETE' THEN
        row_data := jsonb_build_object('old_record', OLD, 'record', NEW, 'operation', operation, 'table', table_name, 'schema', table_schema);
        PERFORM realtime.send (row_data, event_name, topic_name);
    ELSE
        RAISE EXCEPTION 'Unexpected operation type: %', operation;
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Failed to process the row: %', SQLERRM;
END;

$$;


--
-- Name: build_prepared_statement_sql(text, regclass, realtime.wal_column[]); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.build_prepared_statement_sql(prepared_statement_name text, entity regclass, columns realtime.wal_column[]) RETURNS text
    LANGUAGE sql
    AS $$
      /*
      Builds a sql string that, if executed, creates a prepared statement to
      tests retrive a row from *entity* by its primary key columns.
      Example
          select realtime.build_prepared_statement_sql('public.notes', '{"id"}'::text[], '{"bigint"}'::text[])
      */
          select
      'prepare ' || prepared_statement_name || ' as
          select
              exists(
                  select
                      1
                  from
                      ' || entity || '
                  where
                      ' || string_agg(quote_ident(pkc.name) || '=' || quote_nullable(pkc.value #>> '{}') , ' and ') || '
              )'
          from
              unnest(columns) pkc
          where
              pkc.is_pkey
          group by
              entity
      $$;


--
-- Name: cast(text, regtype); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime."cast"(val text, type_ regtype) RETURNS jsonb
    LANGUAGE plpgsql IMMUTABLE
    AS $$
    declare
      res jsonb;
    begin
      execute format('select to_jsonb(%L::'|| type_::text || ')', val)  into res;
      return res;
    end
    $$;


--
-- Name: check_equality_op(realtime.equality_op, regtype, text, text); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.check_equality_op(op realtime.equality_op, type_ regtype, val_1 text, val_2 text) RETURNS boolean
    LANGUAGE plpgsql IMMUTABLE
    AS $$
      /*
      Casts *val_1* and *val_2* as type *type_* and check the *op* condition for truthiness
      */
      declare
          op_symbol text = (
              case
                  when op = 'eq' then '='
                  when op = 'neq' then '!='
                  when op = 'lt' then '<'
                  when op = 'lte' then '<='
                  when op = 'gt' then '>'
                  when op = 'gte' then '>='
                  when op = 'in' then '= any'
                  else 'UNKNOWN OP'
              end
          );
          res boolean;
      begin
          execute format(
              'select %L::'|| type_::text || ' ' || op_symbol
              || ' ( %L::'
              || (
                  case
                      when op = 'in' then type_::text || '[]'
                      else type_::text end
              )
              || ')', val_1, val_2) into res;
          return res;
      end;
      $$;


--
-- Name: is_visible_through_filters(realtime.wal_column[], realtime.user_defined_filter[]); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.is_visible_through_filters(columns realtime.wal_column[], filters realtime.user_defined_filter[]) RETURNS boolean
    LANGUAGE sql IMMUTABLE
    AS $_$
    /*
    Should the record be visible (true) or filtered out (false) after *filters* are applied
    */
        select
            -- Default to allowed when no filters present
            $2 is null -- no filters. this should not happen because subscriptions has a default
            or array_length($2, 1) is null -- array length of an empty array is null
            or bool_and(
                coalesce(
                    realtime.check_equality_op(
                        op:=f.op,
                        type_:=coalesce(
                            col.type_oid::regtype, -- null when wal2json version <= 2.4
                            col.type_name::regtype
                        ),
                        -- cast jsonb to text
                        val_1:=col.value #>> '{}',
                        val_2:=f.value
                    ),
                    false -- if null, filter does not match
                )
            )
        from
            unnest(filters) f
            join unnest(columns) col
                on f.column_name = col.name;
    $_$;


--
-- Name: list_changes(name, name, integer, integer); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.list_changes(publication name, slot_name name, max_changes integer, max_record_bytes integer) RETURNS SETOF realtime.wal_rls
    LANGUAGE sql
    SET log_min_messages TO 'fatal'
    AS $$
      with pub as (
        select
          concat_ws(
            ',',
            case when bool_or(pubinsert) then 'insert' else null end,
            case when bool_or(pubupdate) then 'update' else null end,
            case when bool_or(pubdelete) then 'delete' else null end
          ) as w2j_actions,
          coalesce(
            string_agg(
              realtime.quote_wal2json(format('%I.%I', schemaname, tablename)::regclass),
              ','
            ) filter (where ppt.tablename is not null and ppt.tablename not like '% %'),
            ''
          ) w2j_add_tables
        from
          pg_publication pp
          left join pg_publication_tables ppt
            on pp.pubname = ppt.pubname
        where
          pp.pubname = publication
        group by
          pp.pubname
        limit 1
      ),
      w2j as (
        select
          x.*, pub.w2j_add_tables
        from
          pub,
          pg_logical_slot_get_changes(
            slot_name, null, max_changes,
            'include-pk', 'true',
            'include-transaction', 'false',
            'include-timestamp', 'true',
            'include-type-oids', 'true',
            'format-version', '2',
            'actions', pub.w2j_actions,
            'add-tables', pub.w2j_add_tables
          ) x
      )
      select
        xyz.wal,
        xyz.is_rls_enabled,
        xyz.subscription_ids,
        xyz.errors
      from
        w2j,
        realtime.apply_rls(
          wal := w2j.data::jsonb,
          max_record_bytes := max_record_bytes
        ) xyz(wal, is_rls_enabled, subscription_ids, errors)
      where
        w2j.w2j_add_tables <> ''
        and xyz.subscription_ids[1] is not null
    $$;


--
-- Name: quote_wal2json(regclass); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.quote_wal2json(entity regclass) RETURNS text
    LANGUAGE sql IMMUTABLE STRICT
    AS $$
      select
        (
          select string_agg('' || ch,'')
          from unnest(string_to_array(nsp.nspname::text, null)) with ordinality x(ch, idx)
          where
            not (x.idx = 1 and x.ch = '"')
            and not (
              x.idx = array_length(string_to_array(nsp.nspname::text, null), 1)
              and x.ch = '"'
            )
        )
        || '.'
        || (
          select string_agg('' || ch,'')
          from unnest(string_to_array(pc.relname::text, null)) with ordinality x(ch, idx)
          where
            not (x.idx = 1 and x.ch = '"')
            and not (
              x.idx = array_length(string_to_array(nsp.nspname::text, null), 1)
              and x.ch = '"'
            )
          )
      from
        pg_class pc
        join pg_namespace nsp
          on pc.relnamespace = nsp.oid
      where
        pc.oid = entity
    $$;


--
-- Name: send(jsonb, text, text, boolean); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.send(payload jsonb, event text, topic text, private boolean DEFAULT true) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  BEGIN
    -- Set the topic configuration
    EXECUTE format('SET LOCAL realtime.topic TO %L', topic);

    -- Attempt to insert the message
    INSERT INTO realtime.messages (payload, event, topic, private, extension)
    VALUES (payload, event, topic, private, 'broadcast');
  EXCEPTION
    WHEN OTHERS THEN
      -- Capture and notify the error
      RAISE WARNING 'ErrorSendingBroadcastMessage: %', SQLERRM;
  END;
END;
$$;


--
-- Name: subscription_check_filters(); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.subscription_check_filters() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
    /*
    Validates that the user defined filters for a subscription:
    - refer to valid columns that the claimed role may access
    - values are coercable to the correct column type
    */
    declare
        col_names text[] = coalesce(
                array_agg(c.column_name order by c.ordinal_position),
                '{}'::text[]
            )
            from
                information_schema.columns c
            where
                format('%I.%I', c.table_schema, c.table_name)::regclass = new.entity
                and pg_catalog.has_column_privilege(
                    (new.claims ->> 'role'),
                    format('%I.%I', c.table_schema, c.table_name)::regclass,
                    c.column_name,
                    'SELECT'
                );
        filter realtime.user_defined_filter;
        col_type regtype;

        in_val jsonb;
    begin
        for filter in select * from unnest(new.filters) loop
            -- Filtered column is valid
            if not filter.column_name = any(col_names) then
                raise exception 'invalid column for filter %', filter.column_name;
            end if;

            -- Type is sanitized and safe for string interpolation
            col_type = (
                select atttypid::regtype
                from pg_catalog.pg_attribute
                where attrelid = new.entity
                      and attname = filter.column_name
            );
            if col_type is null then
                raise exception 'failed to lookup type for column %', filter.column_name;
            end if;

            -- Set maximum number of entries for in filter
            if filter.op = 'in'::realtime.equality_op then
                in_val = realtime.cast(filter.value, (col_type::text || '[]')::regtype);
                if coalesce(jsonb_array_length(in_val), 0) > 100 then
                    raise exception 'too many values for `in` filter. Maximum 100';
                end if;
            else
                -- raises an exception if value is not coercable to type
                perform realtime.cast(filter.value, col_type);
            end if;

        end loop;

        -- Apply consistent order to filters so the unique constraint on
        -- (subscription_id, entity, filters) can't be tricked by a different filter order
        new.filters = coalesce(
            array_agg(f order by f.column_name, f.op, f.value),
            '{}'
        ) from unnest(new.filters) f;

        return new;
    end;
    $$;


--
-- Name: to_regrole(text); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.to_regrole(role_name text) RETURNS regrole
    LANGUAGE sql IMMUTABLE
    AS $$ select role_name::regrole $$;


--
-- Name: topic(); Type: FUNCTION; Schema: realtime; Owner: -
--

CREATE FUNCTION realtime.topic() RETURNS text
    LANGUAGE sql STABLE
    AS $$
select nullif(current_setting('realtime.topic', true), '')::text;
$$;


--
-- Name: add_prefixes(text, text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.add_prefixes(_bucket_id text, _name text) RETURNS void
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
DECLARE
    prefixes text[];
BEGIN
    prefixes := "storage"."get_prefixes"("_name");

    IF array_length(prefixes, 1) > 0 THEN
        INSERT INTO storage.prefixes (name, bucket_id)
        SELECT UNNEST(prefixes) as name, "_bucket_id" ON CONFLICT DO NOTHING;
    END IF;
END;
$$;


--
-- Name: can_insert_object(text, text, uuid, jsonb); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.can_insert_object(bucketid text, name text, owner uuid, metadata jsonb) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
  INSERT INTO "storage"."objects" ("bucket_id", "name", "owner", "metadata") VALUES (bucketid, name, owner, metadata);
  -- hack to rollback the successful insert
  RAISE sqlstate 'PT200' using
  message = 'ROLLBACK',
  detail = 'rollback successful insert';
END
$$;


--
-- Name: delete_prefix(text, text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.delete_prefix(_bucket_id text, _name text) RETURNS boolean
    LANGUAGE plpgsql SECURITY DEFINER
    AS $$
BEGIN
    -- Check if we can delete the prefix
    IF EXISTS(
        SELECT FROM "storage"."prefixes"
        WHERE "prefixes"."bucket_id" = "_bucket_id"
          AND level = "storage"."get_level"("_name") + 1
          AND "prefixes"."name" COLLATE "C" LIKE "_name" || '/%'
        LIMIT 1
    )
    OR EXISTS(
        SELECT FROM "storage"."objects"
        WHERE "objects"."bucket_id" = "_bucket_id"
          AND "storage"."get_level"("objects"."name") = "storage"."get_level"("_name") + 1
          AND "objects"."name" COLLATE "C" LIKE "_name" || '/%'
        LIMIT 1
    ) THEN
    -- There are sub-objects, skip deletion
    RETURN false;
    ELSE
        DELETE FROM "storage"."prefixes"
        WHERE "prefixes"."bucket_id" = "_bucket_id"
          AND level = "storage"."get_level"("_name")
          AND "prefixes"."name" = "_name";
        RETURN true;
    END IF;
END;
$$;


--
-- Name: delete_prefix_hierarchy_trigger(); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.delete_prefix_hierarchy_trigger() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    prefix text;
BEGIN
    prefix := "storage"."get_prefix"(OLD."name");

    IF coalesce(prefix, '') != '' THEN
        PERFORM "storage"."delete_prefix"(OLD."bucket_id", prefix);
    END IF;

    RETURN OLD;
END;
$$;


--
-- Name: enforce_bucket_name_length(); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.enforce_bucket_name_length() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
begin
    if length(new.name) > 100 then
        raise exception 'bucket name "%" is too long (% characters). Max is 100.', new.name, length(new.name);
    end if;
    return new;
end;
$$;


--
-- Name: extension(text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.extension(name text) RETURNS text
    LANGUAGE plpgsql IMMUTABLE
    AS $$
DECLARE
    _parts text[];
    _filename text;
BEGIN
    SELECT string_to_array(name, '/') INTO _parts;
    SELECT _parts[array_length(_parts,1)] INTO _filename;
    RETURN reverse(split_part(reverse(_filename), '.', 1));
END
$$;


--
-- Name: filename(text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.filename(name text) RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
_parts text[];
BEGIN
	select string_to_array(name, '/') into _parts;
	return _parts[array_length(_parts,1)];
END
$$;


--
-- Name: foldername(text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.foldername(name text) RETURNS text[]
    LANGUAGE plpgsql IMMUTABLE
    AS $$
DECLARE
    _parts text[];
BEGIN
    -- Split on "/" to get path segments
    SELECT string_to_array(name, '/') INTO _parts;
    -- Return everything except the last segment
    RETURN _parts[1 : array_length(_parts,1) - 1];
END
$$;


--
-- Name: get_level(text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.get_level(name text) RETURNS integer
    LANGUAGE sql IMMUTABLE STRICT
    AS $$
SELECT array_length(string_to_array("name", '/'), 1);
$$;


--
-- Name: get_prefix(text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.get_prefix(name text) RETURNS text
    LANGUAGE sql IMMUTABLE STRICT
    AS $_$
SELECT
    CASE WHEN strpos("name", '/') > 0 THEN
             regexp_replace("name", '[\/]{1}[^\/]+\/?$', '')
         ELSE
             ''
        END;
$_$;


--
-- Name: get_prefixes(text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.get_prefixes(name text) RETURNS text[]
    LANGUAGE plpgsql IMMUTABLE STRICT
    AS $$
DECLARE
    parts text[];
    prefixes text[];
    prefix text;
BEGIN
    -- Split the name into parts by '/'
    parts := string_to_array("name", '/');
    prefixes := '{}';

    -- Construct the prefixes, stopping one level below the last part
    FOR i IN 1..array_length(parts, 1) - 1 LOOP
            prefix := array_to_string(parts[1:i], '/');
            prefixes := array_append(prefixes, prefix);
    END LOOP;

    RETURN prefixes;
END;
$$;


--
-- Name: get_size_by_bucket(); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.get_size_by_bucket() RETURNS TABLE(size bigint, bucket_id text)
    LANGUAGE plpgsql STABLE
    AS $$
BEGIN
    return query
        select sum((metadata->>'size')::bigint) as size, obj.bucket_id
        from "storage".objects as obj
        group by obj.bucket_id;
END
$$;


--
-- Name: list_multipart_uploads_with_delimiter(text, text, text, integer, text, text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.list_multipart_uploads_with_delimiter(bucket_id text, prefix_param text, delimiter_param text, max_keys integer DEFAULT 100, next_key_token text DEFAULT ''::text, next_upload_token text DEFAULT ''::text) RETURNS TABLE(key text, id text, created_at timestamp with time zone)
    LANGUAGE plpgsql
    AS $_$
BEGIN
    RETURN QUERY EXECUTE
        'SELECT DISTINCT ON(key COLLATE "C") * from (
            SELECT
                CASE
                    WHEN position($2 IN substring(key from length($1) + 1)) > 0 THEN
                        substring(key from 1 for length($1) + position($2 IN substring(key from length($1) + 1)))
                    ELSE
                        key
                END AS key, id, created_at
            FROM
                storage.s3_multipart_uploads
            WHERE
                bucket_id = $5 AND
                key ILIKE $1 || ''%'' AND
                CASE
                    WHEN $4 != '''' AND $6 = '''' THEN
                        CASE
                            WHEN position($2 IN substring(key from length($1) + 1)) > 0 THEN
                                substring(key from 1 for length($1) + position($2 IN substring(key from length($1) + 1))) COLLATE "C" > $4
                            ELSE
                                key COLLATE "C" > $4
                            END
                    ELSE
                        true
                END AND
                CASE
                    WHEN $6 != '''' THEN
                        id COLLATE "C" > $6
                    ELSE
                        true
                    END
            ORDER BY
                key COLLATE "C" ASC, created_at ASC) as e order by key COLLATE "C" LIMIT $3'
        USING prefix_param, delimiter_param, max_keys, next_key_token, bucket_id, next_upload_token;
END;
$_$;


--
-- Name: list_objects_with_delimiter(text, text, text, integer, text, text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.list_objects_with_delimiter(bucket_id text, prefix_param text, delimiter_param text, max_keys integer DEFAULT 100, start_after text DEFAULT ''::text, next_token text DEFAULT ''::text) RETURNS TABLE(name text, id uuid, metadata jsonb, updated_at timestamp with time zone)
    LANGUAGE plpgsql
    AS $_$
BEGIN
    RETURN QUERY EXECUTE
        'SELECT DISTINCT ON(name COLLATE "C") * from (
            SELECT
                CASE
                    WHEN position($2 IN substring(name from length($1) + 1)) > 0 THEN
                        substring(name from 1 for length($1) + position($2 IN substring(name from length($1) + 1)))
                    ELSE
                        name
                END AS name, id, metadata, updated_at
            FROM
                storage.objects
            WHERE
                bucket_id = $5 AND
                name ILIKE $1 || ''%'' AND
                CASE
                    WHEN $6 != '''' THEN
                    name COLLATE "C" > $6
                ELSE true END
                AND CASE
                    WHEN $4 != '''' THEN
                        CASE
                            WHEN position($2 IN substring(name from length($1) + 1)) > 0 THEN
                                substring(name from 1 for length($1) + position($2 IN substring(name from length($1) + 1))) COLLATE "C" > $4
                            ELSE
                                name COLLATE "C" > $4
                            END
                    ELSE
                        true
                END
            ORDER BY
                name COLLATE "C" ASC) as e order by name COLLATE "C" LIMIT $3'
        USING prefix_param, delimiter_param, max_keys, next_token, bucket_id, start_after;
END;
$_$;


--
-- Name: objects_insert_prefix_trigger(); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.objects_insert_prefix_trigger() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    PERFORM "storage"."add_prefixes"(NEW."bucket_id", NEW."name");
    NEW.level := "storage"."get_level"(NEW."name");

    RETURN NEW;
END;
$$;


--
-- Name: objects_update_prefix_trigger(); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.objects_update_prefix_trigger() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
DECLARE
    old_prefixes TEXT[];
BEGIN
    -- Ensure this is an update operation and the name has changed
    IF TG_OP = 'UPDATE' AND (NEW."name" <> OLD."name" OR NEW."bucket_id" <> OLD."bucket_id") THEN
        -- Retrieve old prefixes
        old_prefixes := "storage"."get_prefixes"(OLD."name");

        -- Remove old prefixes that are only used by this object
        WITH all_prefixes as (
            SELECT unnest(old_prefixes) as prefix
        ),
        can_delete_prefixes as (
             SELECT prefix
             FROM all_prefixes
             WHERE NOT EXISTS (
                 SELECT 1 FROM "storage"."objects"
                 WHERE "bucket_id" = OLD."bucket_id"
                   AND "name" <> OLD."name"
                   AND "name" LIKE (prefix || '%')
             )
         )
        DELETE FROM "storage"."prefixes" WHERE name IN (SELECT prefix FROM can_delete_prefixes);

        -- Add new prefixes
        PERFORM "storage"."add_prefixes"(NEW."bucket_id", NEW."name");
    END IF;
    -- Set the new level
    NEW."level" := "storage"."get_level"(NEW."name");

    RETURN NEW;
END;
$$;


--
-- Name: operation(); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.operation() RETURNS text
    LANGUAGE plpgsql STABLE
    AS $$
BEGIN
    RETURN current_setting('storage.operation', true);
END;
$$;


--
-- Name: prefixes_insert_trigger(); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.prefixes_insert_trigger() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    PERFORM "storage"."add_prefixes"(NEW."bucket_id", NEW."name");
    RETURN NEW;
END;
$$;


--
-- Name: search(text, text, integer, integer, integer, text, text, text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.search(prefix text, bucketname text, limits integer DEFAULT 100, levels integer DEFAULT 1, offsets integer DEFAULT 0, search text DEFAULT ''::text, sortcolumn text DEFAULT 'name'::text, sortorder text DEFAULT 'asc'::text) RETURNS TABLE(name text, id uuid, updated_at timestamp with time zone, created_at timestamp with time zone, last_accessed_at timestamp with time zone, metadata jsonb)
    LANGUAGE plpgsql
    AS $$
declare
    can_bypass_rls BOOLEAN;
begin
    SELECT rolbypassrls
    INTO can_bypass_rls
    FROM pg_roles
    WHERE rolname = coalesce(nullif(current_setting('role', true), 'none'), current_user);

    IF can_bypass_rls THEN
        RETURN QUERY SELECT * FROM storage.search_v1_optimised(prefix, bucketname, limits, levels, offsets, search, sortcolumn, sortorder);
    ELSE
        RETURN QUERY SELECT * FROM storage.search_legacy_v1(prefix, bucketname, limits, levels, offsets, search, sortcolumn, sortorder);
    END IF;
end;
$$;


--
-- Name: search_legacy_v1(text, text, integer, integer, integer, text, text, text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.search_legacy_v1(prefix text, bucketname text, limits integer DEFAULT 100, levels integer DEFAULT 1, offsets integer DEFAULT 0, search text DEFAULT ''::text, sortcolumn text DEFAULT 'name'::text, sortorder text DEFAULT 'asc'::text) RETURNS TABLE(name text, id uuid, updated_at timestamp with time zone, created_at timestamp with time zone, last_accessed_at timestamp with time zone, metadata jsonb)
    LANGUAGE plpgsql STABLE
    AS $_$
declare
    v_order_by text;
    v_sort_order text;
begin
    case
        when sortcolumn = 'name' then
            v_order_by = 'name';
        when sortcolumn = 'updated_at' then
            v_order_by = 'updated_at';
        when sortcolumn = 'created_at' then
            v_order_by = 'created_at';
        when sortcolumn = 'last_accessed_at' then
            v_order_by = 'last_accessed_at';
        else
            v_order_by = 'name';
        end case;

    case
        when sortorder = 'asc' then
            v_sort_order = 'asc';
        when sortorder = 'desc' then
            v_sort_order = 'desc';
        else
            v_sort_order = 'asc';
        end case;

    v_order_by = v_order_by || ' ' || v_sort_order;

    return query execute
        'with folders as (
           select path_tokens[$1] as folder
           from storage.objects
             where objects.name ilike $2 || $3 || ''%''
               and bucket_id = $4
               and array_length(objects.path_tokens, 1) <> $1
           group by folder
           order by folder ' || v_sort_order || '
     )
     (select folder as "name",
            null as id,
            null as updated_at,
            null as created_at,
            null as last_accessed_at,
            null as metadata from folders)
     union all
     (select path_tokens[$1] as "name",
            id,
            updated_at,
            created_at,
            last_accessed_at,
            metadata
     from storage.objects
     where objects.name ilike $2 || $3 || ''%''
       and bucket_id = $4
       and array_length(objects.path_tokens, 1) = $1
     order by ' || v_order_by || ')
     limit $5
     offset $6' using levels, prefix, search, bucketname, limits, offsets;
end;
$_$;


--
-- Name: search_v1_optimised(text, text, integer, integer, integer, text, text, text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.search_v1_optimised(prefix text, bucketname text, limits integer DEFAULT 100, levels integer DEFAULT 1, offsets integer DEFAULT 0, search text DEFAULT ''::text, sortcolumn text DEFAULT 'name'::text, sortorder text DEFAULT 'asc'::text) RETURNS TABLE(name text, id uuid, updated_at timestamp with time zone, created_at timestamp with time zone, last_accessed_at timestamp with time zone, metadata jsonb)
    LANGUAGE plpgsql STABLE
    AS $_$
declare
    v_order_by text;
    v_sort_order text;
begin
    case
        when sortcolumn = 'name' then
            v_order_by = 'name';
        when sortcolumn = 'updated_at' then
            v_order_by = 'updated_at';
        when sortcolumn = 'created_at' then
            v_order_by = 'created_at';
        when sortcolumn = 'last_accessed_at' then
            v_order_by = 'last_accessed_at';
        else
            v_order_by = 'name';
        end case;

    case
        when sortorder = 'asc' then
            v_sort_order = 'asc';
        when sortorder = 'desc' then
            v_sort_order = 'desc';
        else
            v_sort_order = 'asc';
        end case;

    v_order_by = v_order_by || ' ' || v_sort_order;

    return query execute
        'with folders as (
           select (string_to_array(name, ''/''))[level] as name
           from storage.prefixes
             where lower(prefixes.name) like lower($2 || $3) || ''%''
               and bucket_id = $4
               and level = $1
           order by name ' || v_sort_order || '
     )
     (select name,
            null as id,
            null as updated_at,
            null as created_at,
            null as last_accessed_at,
            null as metadata from folders)
     union all
     (select path_tokens[level] as "name",
            id,
            updated_at,
            created_at,
            last_accessed_at,
            metadata
     from storage.objects
     where lower(objects.name) like lower($2 || $3) || ''%''
       and bucket_id = $4
       and level = $1
     order by ' || v_order_by || ')
     limit $5
     offset $6' using levels, prefix, search, bucketname, limits, offsets;
end;
$_$;


--
-- Name: search_v2(text, text, integer, integer, text); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.search_v2(prefix text, bucket_name text, limits integer DEFAULT 100, levels integer DEFAULT 1, start_after text DEFAULT ''::text) RETURNS TABLE(key text, name text, id uuid, updated_at timestamp with time zone, created_at timestamp with time zone, metadata jsonb)
    LANGUAGE plpgsql STABLE
    AS $_$
BEGIN
    RETURN query EXECUTE
        $sql$
        SELECT * FROM (
            (
                SELECT
                    split_part(name, '/', $4) AS key,
                    name || '/' AS name,
                    NULL::uuid AS id,
                    NULL::timestamptz AS updated_at,
                    NULL::timestamptz AS created_at,
                    NULL::jsonb AS metadata
                FROM storage.prefixes
                WHERE name COLLATE "C" LIKE $1 || '%'
                AND bucket_id = $2
                AND level = $4
                AND name COLLATE "C" > $5
                ORDER BY prefixes.name COLLATE "C" LIMIT $3
            )
            UNION ALL
            (SELECT split_part(name, '/', $4) AS key,
                name,
                id,
                updated_at,
                created_at,
                metadata
            FROM storage.objects
            WHERE name COLLATE "C" LIKE $1 || '%'
                AND bucket_id = $2
                AND level = $4
                AND name COLLATE "C" > $5
            ORDER BY name COLLATE "C" LIMIT $3)
        ) obj
        ORDER BY name COLLATE "C" LIMIT $3;
        $sql$
        USING prefix, bucket_name, limits, levels, start_after;
END;
$_$;


--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: storage; Owner: -
--

CREATE FUNCTION storage.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW; 
END;
$$;


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: audit_log_entries; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.audit_log_entries (
    instance_id uuid,
    id uuid NOT NULL,
    payload json,
    created_at timestamp with time zone,
    ip_address character varying(64) DEFAULT ''::character varying NOT NULL
);


--
-- Name: TABLE audit_log_entries; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.audit_log_entries IS 'Auth: Audit trail for user actions.';


--
-- Name: flow_state; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.flow_state (
    id uuid NOT NULL,
    user_id uuid,
    auth_code text NOT NULL,
    code_challenge_method auth.code_challenge_method NOT NULL,
    code_challenge text NOT NULL,
    provider_type text NOT NULL,
    provider_access_token text,
    provider_refresh_token text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    authentication_method text NOT NULL,
    auth_code_issued_at timestamp with time zone
);


--
-- Name: TABLE flow_state; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.flow_state IS 'stores metadata for pkce logins';


--
-- Name: identities; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.identities (
    provider_id text NOT NULL,
    user_id uuid NOT NULL,
    identity_data jsonb NOT NULL,
    provider text NOT NULL,
    last_sign_in_at timestamp with time zone,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    email text GENERATED ALWAYS AS (lower((identity_data ->> 'email'::text))) STORED,
    id uuid DEFAULT gen_random_uuid() NOT NULL
);


--
-- Name: TABLE identities; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.identities IS 'Auth: Stores identities associated to a user.';


--
-- Name: COLUMN identities.email; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON COLUMN auth.identities.email IS 'Auth: Email is a generated column that references the optional email property in the identity_data';


--
-- Name: instances; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.instances (
    id uuid NOT NULL,
    uuid uuid,
    raw_base_config text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone
);


--
-- Name: TABLE instances; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.instances IS 'Auth: Manages users across multiple sites.';


--
-- Name: mfa_amr_claims; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.mfa_amr_claims (
    session_id uuid NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    authentication_method text NOT NULL,
    id uuid NOT NULL
);


--
-- Name: TABLE mfa_amr_claims; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.mfa_amr_claims IS 'auth: stores authenticator method reference claims for multi factor authentication';


--
-- Name: mfa_challenges; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.mfa_challenges (
    id uuid NOT NULL,
    factor_id uuid NOT NULL,
    created_at timestamp with time zone NOT NULL,
    verified_at timestamp with time zone,
    ip_address inet NOT NULL,
    otp_code text,
    web_authn_session_data jsonb
);


--
-- Name: TABLE mfa_challenges; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.mfa_challenges IS 'auth: stores metadata about challenge requests made';


--
-- Name: mfa_factors; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.mfa_factors (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    friendly_name text,
    factor_type auth.factor_type NOT NULL,
    status auth.factor_status NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    secret text,
    phone text,
    last_challenged_at timestamp with time zone,
    web_authn_credential jsonb,
    web_authn_aaguid uuid
);


--
-- Name: TABLE mfa_factors; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.mfa_factors IS 'auth: stores metadata about factors';


--
-- Name: one_time_tokens; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.one_time_tokens (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    token_type auth.one_time_token_type NOT NULL,
    token_hash text NOT NULL,
    relates_to text NOT NULL,
    created_at timestamp without time zone DEFAULT now() NOT NULL,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    CONSTRAINT one_time_tokens_token_hash_check CHECK ((char_length(token_hash) > 0))
);


--
-- Name: refresh_tokens; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.refresh_tokens (
    instance_id uuid,
    id bigint NOT NULL,
    token character varying(255),
    user_id character varying(255),
    revoked boolean,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    parent character varying(255),
    session_id uuid
);


--
-- Name: TABLE refresh_tokens; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.refresh_tokens IS 'Auth: Store of tokens used to refresh JWT tokens once they expire.';


--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE; Schema: auth; Owner: -
--

CREATE SEQUENCE auth.refresh_tokens_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: refresh_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: auth; Owner: -
--

ALTER SEQUENCE auth.refresh_tokens_id_seq OWNED BY auth.refresh_tokens.id;


--
-- Name: saml_providers; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.saml_providers (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    entity_id text NOT NULL,
    metadata_xml text NOT NULL,
    metadata_url text,
    attribute_mapping jsonb,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    name_id_format text,
    CONSTRAINT "entity_id not empty" CHECK ((char_length(entity_id) > 0)),
    CONSTRAINT "metadata_url not empty" CHECK (((metadata_url = NULL::text) OR (char_length(metadata_url) > 0))),
    CONSTRAINT "metadata_xml not empty" CHECK ((char_length(metadata_xml) > 0))
);


--
-- Name: TABLE saml_providers; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.saml_providers IS 'Auth: Manages SAML Identity Provider connections.';


--
-- Name: saml_relay_states; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.saml_relay_states (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    request_id text NOT NULL,
    for_email text,
    redirect_to text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    flow_state_id uuid,
    CONSTRAINT "request_id not empty" CHECK ((char_length(request_id) > 0))
);


--
-- Name: TABLE saml_relay_states; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.saml_relay_states IS 'Auth: Contains SAML Relay State information for each Service Provider initiated login.';


--
-- Name: schema_migrations; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.schema_migrations (
    version character varying(255) NOT NULL
);


--
-- Name: TABLE schema_migrations; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.schema_migrations IS 'Auth: Manages updates to the auth system.';


--
-- Name: sessions; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.sessions (
    id uuid NOT NULL,
    user_id uuid NOT NULL,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    factor_id uuid,
    aal auth.aal_level,
    not_after timestamp with time zone,
    refreshed_at timestamp without time zone,
    user_agent text,
    ip inet,
    tag text
);


--
-- Name: TABLE sessions; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.sessions IS 'Auth: Stores session data associated to a user.';


--
-- Name: COLUMN sessions.not_after; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON COLUMN auth.sessions.not_after IS 'Auth: Not after is a nullable column that contains a timestamp after which the session should be regarded as expired.';


--
-- Name: sso_domains; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.sso_domains (
    id uuid NOT NULL,
    sso_provider_id uuid NOT NULL,
    domain text NOT NULL,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    CONSTRAINT "domain not empty" CHECK ((char_length(domain) > 0))
);


--
-- Name: TABLE sso_domains; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.sso_domains IS 'Auth: Manages SSO email address domain mapping to an SSO Identity Provider.';


--
-- Name: sso_providers; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.sso_providers (
    id uuid NOT NULL,
    resource_id text,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    CONSTRAINT "resource_id not empty" CHECK (((resource_id = NULL::text) OR (char_length(resource_id) > 0)))
);


--
-- Name: TABLE sso_providers; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.sso_providers IS 'Auth: Manages SSO identity provider information; see saml_providers for SAML.';


--
-- Name: COLUMN sso_providers.resource_id; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON COLUMN auth.sso_providers.resource_id IS 'Auth: Uniquely identifies a SSO provider according to a user-chosen resource ID (case insensitive), useful in infrastructure as code.';


--
-- Name: users; Type: TABLE; Schema: auth; Owner: -
--

CREATE TABLE auth.users (
    instance_id uuid,
    id uuid NOT NULL,
    aud character varying(255),
    role character varying(255),
    email character varying(255),
    encrypted_password character varying(255),
    email_confirmed_at timestamp with time zone,
    invited_at timestamp with time zone,
    confirmation_token character varying(255),
    confirmation_sent_at timestamp with time zone,
    recovery_token character varying(255),
    recovery_sent_at timestamp with time zone,
    email_change_token_new character varying(255),
    email_change character varying(255),
    email_change_sent_at timestamp with time zone,
    last_sign_in_at timestamp with time zone,
    raw_app_meta_data jsonb,
    raw_user_meta_data jsonb,
    is_super_admin boolean,
    created_at timestamp with time zone,
    updated_at timestamp with time zone,
    phone text DEFAULT NULL::character varying,
    phone_confirmed_at timestamp with time zone,
    phone_change text DEFAULT ''::character varying,
    phone_change_token character varying(255) DEFAULT ''::character varying,
    phone_change_sent_at timestamp with time zone,
    confirmed_at timestamp with time zone GENERATED ALWAYS AS (LEAST(email_confirmed_at, phone_confirmed_at)) STORED,
    email_change_token_current character varying(255) DEFAULT ''::character varying,
    email_change_confirm_status smallint DEFAULT 0,
    banned_until timestamp with time zone,
    reauthentication_token character varying(255) DEFAULT ''::character varying,
    reauthentication_sent_at timestamp with time zone,
    is_sso_user boolean DEFAULT false NOT NULL,
    deleted_at timestamp with time zone,
    is_anonymous boolean DEFAULT false NOT NULL,
    CONSTRAINT users_email_change_confirm_status_check CHECK (((email_change_confirm_status >= 0) AND (email_change_confirm_status <= 2)))
);


--
-- Name: TABLE users; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON TABLE auth.users IS 'Auth: Stores user login data within a secure schema.';


--
-- Name: COLUMN users.is_sso_user; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON COLUMN auth.users.is_sso_user IS 'Auth: Set this column to true when the account comes from SSO. These accounts can have duplicate emails.';


--
-- Name: demo_users; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.demo_users (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    first_name text,
    last_name text,
    full_name text,
    email text NOT NULL,
    company text,
    job_title text,
    company_size text,
    industry text DEFAULT 'Technology'::text,
    compliance_needs text,
    email_verified boolean DEFAULT false,
    verified_at timestamp with time zone,
    verification_token text,
    agreed_to_terms boolean DEFAULT false,
    marketing_consent boolean DEFAULT false,
    registration_source text DEFAULT 'demo'::text,
    user_agent text,
    referrer text,
    demo_session_id uuid,
    lead_score integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    user_type text DEFAULT 'regular'::text,
    admin_role text,
    admin_permissions text[],
    last_login timestamp with time zone,
    temporary_password text,
    CONSTRAINT demo_users_user_type_check CHECK ((user_type = ANY (ARRAY['regular'::text, 'admin'::text])))
);


--
-- Name: admin_dashboard_summary; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.admin_dashboard_summary AS
 SELECT count(*) FILTER (WHERE (user_type = 'regular'::text)) AS regular_users,
    count(*) FILTER (WHERE (user_type = 'admin'::text)) AS admin_users,
    count(*) FILTER (WHERE (email_verified = true)) AS verified_users,
    count(*) FILTER (WHERE (lead_score >= 80)) AS high_value_leads,
    count(*) FILTER (WHERE (created_at >= CURRENT_DATE)) AS users_today,
    count(*) FILTER (WHERE (created_at >= (CURRENT_DATE - '7 days'::interval))) AS users_this_week,
    count(*) FILTER (WHERE ((user_type = 'admin'::text) AND (last_login >= CURRENT_DATE))) AS admin_logins_today
   FROM public.demo_users;


--
-- Name: admin_sessions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.admin_sessions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    token uuid NOT NULL,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()) NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    is_active boolean DEFAULT true NOT NULL
);


--
-- Name: admin_verification_codes; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.admin_verification_codes (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    code text NOT NULL,
    password_hash text NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    used_at timestamp with time zone,
    is_used boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: contextual_prompts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.contextual_prompts (
    id character varying(100) NOT NULL,
    name character varying(200) NOT NULL,
    description text NOT NULL,
    system_prompt text NOT NULL,
    response_guidelines text[],
    tone character varying(50) DEFAULT 'professional'::character varying,
    max_tokens integer DEFAULT 800,
    temperature double precision DEFAULT 0.3,
    forbidden_topics text[],
    required_elements text[],
    format_instructions text,
    example_responses text[],
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    created_by uuid,
    usage_count integer DEFAULT 0,
    avg_response_rating double precision DEFAULT 0.0
);


--
-- Name: product_registrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.product_registrations (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    user_id uuid,
    product_id text DEFAULT 'default'::text NOT NULL,
    program_type text DEFAULT 'pilot'::text NOT NULL,
    source_url text,
    utm_source text,
    utm_campaign text,
    full_name text NOT NULL,
    email text NOT NULL,
    company text NOT NULL,
    job_title text NOT NULL,
    primary_business text NOT NULL,
    company_size text NOT NULL,
    phone text NOT NULL,
    use_case text NOT NULL,
    timeline text,
    email_verified boolean DEFAULT false,
    email_verified_at timestamp with time zone,
    ip_address inet,
    user_agent text,
    submission_source text DEFAULT 'web'::text,
    created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
    status text DEFAULT 'pending_verification'::text,
    priority text DEFAULT 'normal'::text,
    assigned_to uuid,
    notes text,
    last_contacted timestamp with time zone,
    contact_count integer DEFAULT 0,
    CONSTRAINT company_length CHECK (((char_length(company) >= 2) AND (char_length(company) <= 100))),
    CONSTRAINT full_name_length CHECK (((char_length(full_name) >= 2) AND (char_length(full_name) <= 100))),
    CONSTRAINT job_title_length CHECK (((char_length(job_title) >= 2) AND (char_length(job_title) <= 100))),
    CONSTRAINT phone_length CHECK (((char_length(phone) >= 10) AND (char_length(phone) <= 20))),
    CONSTRAINT product_registrations_priority_check CHECK ((priority = ANY (ARRAY['low'::text, 'normal'::text, 'high'::text, 'urgent'::text]))),
    CONSTRAINT product_registrations_status_check CHECK ((status = ANY (ARRAY['pending_verification'::text, 'verified'::text, 'pending'::text, 'approved'::text, 'rejected'::text, 'waitlist'::text, 'contacted'::text, 'onboarded'::text]))),
    CONSTRAINT use_case_length CHECK (((char_length(use_case) >= 20) AND (char_length(use_case) <= 1000))),
    CONSTRAINT valid_company_size CHECK ((company_size = ANY (ARRAY['1-10'::text, '11-50'::text, '51-200'::text, '201-1000'::text, '1001-5000'::text, '5000+'::text]))),
    CONSTRAINT valid_email CHECK ((email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'::text)),
    CONSTRAINT valid_primary_business CHECK ((primary_business = ANY (ARRAY['Technology'::text, 'Financial Services'::text, 'Healthcare'::text, 'Manufacturing'::text, 'Government'::text, 'Education'::text, 'Retail'::text, 'Energy'::text, 'Telecommunications'::text, 'Other'::text]))),
    CONSTRAINT valid_product_id CHECK ((product_id = ANY (ARRAY['arioncomply'::text, 'arionsecure'::text, 'arionanalytics'::text, 'arionplatform'::text, 'default'::text]))),
    CONSTRAINT valid_program_type CHECK ((program_type = ANY (ARRAY['pilot'::text, 'waitlist'::text, 'beta'::text, 'early_access'::text]))),
    CONSTRAINT valid_timeline CHECK (((timeline IS NULL) OR (timeline = ANY (ARRAY['Immediately'::text, '1-3 months'::text, '3-6 months'::text, '6-12 months'::text, '12+ months'::text, 'Just exploring'::text]))))
);


--
-- Name: daily_registration_trends; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.daily_registration_trends AS
 SELECT date(created_at) AS registration_date,
    product_id,
    program_type,
    count(*) AS daily_count,
    count(*) FILTER (WHERE (email_verified = true)) AS verified_count,
    count(DISTINCT ip_address) AS unique_ips,
    count(DISTINCT company) AS unique_companies,
    array_agg(DISTINCT primary_business) AS industries
   FROM public.product_registrations
  WHERE (created_at >= (now() - '90 days'::interval))
  GROUP BY (date(created_at)), product_id, program_type
  ORDER BY (date(created_at)) DESC, product_id, program_type;


--
-- Name: demo_browsing_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.demo_browsing_logs (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    session_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    page_url text NOT NULL,
    page_title text,
    referrer_url text,
    time_on_page integer,
    scroll_depth numeric(5,2),
    clicks integer DEFAULT 0,
    document_id uuid,
    document_section text,
    event_type text,
    metadata jsonb DEFAULT '{}'::jsonb
);


--
-- Name: demo_contacts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.demo_contacts (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    session_id uuid NOT NULL,
    name text NOT NULL,
    email text NOT NULL,
    company text,
    phone text,
    message text,
    created_at timestamp with time zone DEFAULT now(),
    application_type text DEFAULT 'contact'::text,
    application_data jsonb DEFAULT '{}'::jsonb,
    lead_score integer DEFAULT 0,
    lead_qualification text DEFAULT 'suspect'::text
);


--
-- Name: demo_documents; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.demo_documents (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    session_id uuid NOT NULL,
    doc_type text NOT NULL,
    title text NOT NULL,
    content text NOT NULL,
    description text,
    icon text,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: demo_embeddings; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.demo_embeddings (
    id bigint NOT NULL,
    session_id uuid,
    content text NOT NULL,
    embedding public.vector(1536),
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: TABLE demo_embeddings; Type: COMMENT; Schema: public; Owner: -
--

COMMENT ON TABLE public.demo_embeddings IS 'Stores vector embeddings for semantic search and context retrieval';


--
-- Name: demo_embeddings_id_seq; Type: SEQUENCE; Schema: public; Owner: -
--

CREATE SEQUENCE public.demo_embeddings_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


--
-- Name: demo_embeddings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: -
--

ALTER SEQUENCE public.demo_embeddings_id_seq OWNED BY public.demo_embeddings.id;


--
-- Name: demo_intake_data; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.demo_intake_data (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    session_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    goal_type text NOT NULL,
    company_size text,
    industry text,
    current_compliance_level text,
    responses jsonb DEFAULT '{}'::jsonb NOT NULL,
    risk_level text,
    priority_areas jsonb DEFAULT '[]'::jsonb,
    recommended_documents jsonb DEFAULT '[]'::jsonb
);


--
-- Name: demo_interactions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.demo_interactions (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    session_id uuid NOT NULL,
    event_type text NOT NULL,
    event_data jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: demo_sessions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.demo_sessions (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    metadata jsonb DEFAULT '{}'::jsonb,
    status text DEFAULT 'active'::text,
    last_interaction timestamp with time zone DEFAULT now(),
    interaction_count integer DEFAULT 0,
    detected_goal text,
    preview_ready boolean DEFAULT false,
    document_count integer DEFAULT 0,
    intake_data jsonb DEFAULT '{}'::jsonb,
    contact_captured boolean DEFAULT false,
    user_id uuid,
    user_email text,
    registration_required boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    user_type text DEFAULT 'regular'::text,
    admin_role text,
    CONSTRAINT demo_sessions_user_type_check CHECK ((user_type = ANY (ARRAY['regular'::text, 'admin'::text])))
);


--
-- Name: demo_verification_requests; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.demo_verification_requests (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    email text NOT NULL,
    verification_token text NOT NULL,
    user_data jsonb NOT NULL,
    is_used boolean DEFAULT false,
    used_at timestamp with time zone,
    session_id uuid,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    temporary_password text
);


--
-- Name: email_verification_funnel; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.email_verification_funnel AS
 SELECT product_id,
    program_type,
    count(*) AS total_registrations,
    count(*) FILTER (WHERE (user_id IS NOT NULL)) AS auth_users_created,
    count(*) FILTER (WHERE (email_verified = true)) AS email_verified,
    count(*) FILTER (WHERE (status = 'verified'::text)) AS status_verified,
    count(*) FILTER (WHERE (status = ANY (ARRAY['approved'::text, 'contacted'::text, 'onboarded'::text]))) AS progressed_further,
    round((((count(*) FILTER (WHERE (user_id IS NOT NULL)))::numeric / (NULLIF(count(*), 0))::numeric) * (100)::numeric), 2) AS auth_creation_rate,
    round((((count(*) FILTER (WHERE (email_verified = true)))::numeric / (NULLIF(count(*), 0))::numeric) * (100)::numeric), 2) AS verification_completion_rate
   FROM public.product_registrations
  GROUP BY product_id, program_type
  ORDER BY (count(*)) DESC;


--
-- Name: event_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.event_logs (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    event_type character varying(100) NOT NULL,
    session_id character varying(255),
    event_data jsonb DEFAULT '{}'::jsonb,
    "timestamp" timestamp with time zone DEFAULT now(),
    user_id uuid
);


--
-- Name: faq_items; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.faq_items (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    question text NOT NULL,
    answer text NOT NULL,
    category text NOT NULL,
    subcategory text,
    embedding public.vector(1536),
    helpful_votes integer DEFAULT 0,
    unhelpful_votes integer DEFAULT 0,
    status text DEFAULT 'published'::text,
    priority integer DEFAULT 0,
    metadata jsonb DEFAULT '{}'::jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT faq_items_status_check CHECK ((status = ANY (ARRAY['draft'::text, 'published'::text, 'archived'::text])))
);


--
-- Name: goal_analytics; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.goal_analytics (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    date date NOT NULL,
    goal_type character varying(100) NOT NULL,
    detection_count integer DEFAULT 0,
    avg_confidence double precision,
    successful_responses integer DEFAULT 0,
    avg_response_time_ms double precision,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: knowledge_base; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.knowledge_base (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    title text NOT NULL,
    content text NOT NULL,
    summary text,
    category text NOT NULL,
    subcategory text,
    embedding public.vector(1536),
    metadata jsonb DEFAULT '{}'::jsonb,
    status text DEFAULT 'published'::text,
    version integer DEFAULT 1,
    author text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT knowledge_base_status_check CHECK ((status = ANY (ARRAY['draft'::text, 'published'::text, 'archived'::text])))
);


--
-- Name: knowledge_documents; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.knowledge_documents (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    title character varying(500) NOT NULL,
    content text NOT NULL,
    category character varying(100),
    subcategory character varying(100),
    document_type character varying(50),
    source_url text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    is_active boolean DEFAULT true,
    embedding public.vector(128),
    metadata jsonb DEFAULT '{}'::jsonb
);


--
-- Name: persistent_admin_sessions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.persistent_admin_sessions (
    token text NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    expires_at timestamp with time zone NOT NULL
);


--
-- Name: product_registration_stats; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.product_registration_stats AS
 SELECT product_id,
    program_type,
    status,
    count(*) AS total_registrations,
    count(*) FILTER (WHERE (email_verified = true)) AS verified_registrations,
    count(*) FILTER (WHERE (email_verified = false)) AS unverified_registrations,
    count(*) FILTER (WHERE (user_id IS NOT NULL)) AS linked_to_auth,
    count(*) FILTER (WHERE (created_at >= (now() - '24:00:00'::interval))) AS last_24h,
    count(*) FILTER (WHERE ((email_verified = true) AND (created_at >= (now() - '24:00:00'::interval)))) AS verified_last_24h,
    round((((count(*) FILTER (WHERE (email_verified = true)))::numeric / (NULLIF(count(*), 0))::numeric) * (100)::numeric), 2) AS verification_rate_percent,
    avg((EXTRACT(epoch FROM (email_verified_at - created_at)) / (3600)::numeric)) FILTER (WHERE (email_verified = true)) AS avg_verification_time_hours
   FROM public.product_registrations
  GROUP BY product_id, program_type, status
  ORDER BY product_id, program_type, status;


--
-- Name: prompt_performance_metrics; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.prompt_performance_metrics (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    prompt_id character varying(100),
    date date NOT NULL,
    usage_count integer DEFAULT 0,
    avg_response_time_ms double precision,
    avg_token_usage double precision,
    avg_user_rating double precision,
    success_rate double precision,
    goal_accuracy double precision,
    created_at timestamp with time zone DEFAULT now()
);


--
-- Name: prompt_usage_logs; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.prompt_usage_logs (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    prompt_id character varying(100),
    session_id character varying(255),
    user_message text NOT NULL,
    detected_goal character varying(100),
    goal_confidence double precision,
    ai_response text,
    response_time_ms integer,
    token_usage jsonb,
    user_satisfaction_rating integer,
    feedback_text text,
    "timestamp" timestamp with time zone DEFAULT now()
);


--
-- Name: response_templates; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.response_templates (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    intent_name text NOT NULL,
    category_name text NOT NULL,
    template_format text NOT NULL,
    required_variables text[] DEFAULT '{}'::text[],
    followup_suggestions text[] DEFAULT '{}'::text[],
    conditions jsonb DEFAULT '{}'::jsonb,
    priority integer DEFAULT 0,
    status text DEFAULT 'active'::text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT response_templates_status_check CHECK ((status = ANY (ARRAY['active'::text, 'inactive'::text])))
);


--
-- Name: user_custom_prompts; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_custom_prompts (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    user_id uuid,
    name character varying(200) NOT NULL,
    description text,
    system_prompt text NOT NULL,
    tone character varying(50) DEFAULT 'professional'::character varying,
    max_tokens integer DEFAULT 800,
    temperature double precision DEFAULT 0.3,
    is_private boolean DEFAULT true,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    usage_count integer DEFAULT 0
);


--
-- Name: user_queries_log; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.user_queries_log (
    id uuid DEFAULT extensions.uuid_generate_v4() NOT NULL,
    session_id uuid,
    query_text text NOT NULL,
    query_intent text,
    query_category text,
    retrieval_results jsonb DEFAULT '{}'::jsonb,
    response_generated text,
    response_time_ms integer,
    retrieval_confidence numeric(3,2),
    user_satisfaction integer,
    conversation_resolved boolean,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT user_queries_log_user_satisfaction_check CHECK (((user_satisfaction >= 1) AND (user_satisfaction <= 5)))
);


--
-- Name: verification_stats; Type: VIEW; Schema: public; Owner: -
--

CREATE VIEW public.verification_stats AS
 SELECT date(created_at) AS date,
    count(*) AS total_requests,
    count(*) FILTER (WHERE (is_used = true)) AS verified_requests,
    count(*) FILTER (WHERE ((expires_at < now()) AND (is_used = false))) AS expired_requests,
    count(*) FILTER (WHERE ((expires_at >= now()) AND (is_used = false))) AS pending_requests,
    round((((count(*) FILTER (WHERE (is_used = true)))::numeric * 100.0) / (count(*))::numeric), 2) AS verification_rate
   FROM public.demo_verification_requests
  GROUP BY (date(created_at))
  ORDER BY (date(created_at)) DESC;


--
-- Name: messages; Type: TABLE; Schema: realtime; Owner: -
--

CREATE TABLE realtime.messages (
    topic text NOT NULL,
    extension text NOT NULL,
    payload jsonb,
    event text,
    private boolean DEFAULT false,
    updated_at timestamp without time zone DEFAULT now() NOT NULL,
    inserted_at timestamp without time zone DEFAULT now() NOT NULL,
    id uuid DEFAULT gen_random_uuid() NOT NULL
)
PARTITION BY RANGE (inserted_at);


--
-- Name: schema_migrations; Type: TABLE; Schema: realtime; Owner: -
--

CREATE TABLE realtime.schema_migrations (
    version bigint NOT NULL,
    inserted_at timestamp(0) without time zone
);


--
-- Name: subscription; Type: TABLE; Schema: realtime; Owner: -
--

CREATE TABLE realtime.subscription (
    id bigint NOT NULL,
    subscription_id uuid NOT NULL,
    entity regclass NOT NULL,
    filters realtime.user_defined_filter[] DEFAULT '{}'::realtime.user_defined_filter[] NOT NULL,
    claims jsonb NOT NULL,
    claims_role regrole GENERATED ALWAYS AS (realtime.to_regrole((claims ->> 'role'::text))) STORED NOT NULL,
    created_at timestamp without time zone DEFAULT timezone('utc'::text, now()) NOT NULL
);


--
-- Name: subscription_id_seq; Type: SEQUENCE; Schema: realtime; Owner: -
--

ALTER TABLE realtime.subscription ALTER COLUMN id ADD GENERATED ALWAYS AS IDENTITY (
    SEQUENCE NAME realtime.subscription_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);


--
-- Name: buckets; Type: TABLE; Schema: storage; Owner: -
--

CREATE TABLE storage.buckets (
    id text NOT NULL,
    name text NOT NULL,
    owner uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    public boolean DEFAULT false,
    avif_autodetection boolean DEFAULT false,
    file_size_limit bigint,
    allowed_mime_types text[],
    owner_id text,
    type storage.buckettype DEFAULT 'STANDARD'::storage.buckettype NOT NULL
);


--
-- Name: COLUMN buckets.owner; Type: COMMENT; Schema: storage; Owner: -
--

COMMENT ON COLUMN storage.buckets.owner IS 'Field is deprecated, use owner_id instead';


--
-- Name: buckets_analytics; Type: TABLE; Schema: storage; Owner: -
--

CREATE TABLE storage.buckets_analytics (
    id text NOT NULL,
    type storage.buckettype DEFAULT 'ANALYTICS'::storage.buckettype NOT NULL,
    format text DEFAULT 'ICEBERG'::text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: migrations; Type: TABLE; Schema: storage; Owner: -
--

CREATE TABLE storage.migrations (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    hash character varying(40) NOT NULL,
    executed_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


--
-- Name: objects; Type: TABLE; Schema: storage; Owner: -
--

CREATE TABLE storage.objects (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    bucket_id text,
    name text,
    owner uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    last_accessed_at timestamp with time zone DEFAULT now(),
    metadata jsonb,
    path_tokens text[] GENERATED ALWAYS AS (string_to_array(name, '/'::text)) STORED,
    version text,
    owner_id text,
    user_metadata jsonb,
    level integer
);


--
-- Name: COLUMN objects.owner; Type: COMMENT; Schema: storage; Owner: -
--

COMMENT ON COLUMN storage.objects.owner IS 'Field is deprecated, use owner_id instead';


--
-- Name: prefixes; Type: TABLE; Schema: storage; Owner: -
--

CREATE TABLE storage.prefixes (
    bucket_id text NOT NULL,
    name text NOT NULL COLLATE pg_catalog."C",
    level integer GENERATED ALWAYS AS (storage.get_level(name)) STORED NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


--
-- Name: s3_multipart_uploads; Type: TABLE; Schema: storage; Owner: -
--

CREATE TABLE storage.s3_multipart_uploads (
    id text NOT NULL,
    in_progress_size bigint DEFAULT 0 NOT NULL,
    upload_signature text NOT NULL,
    bucket_id text NOT NULL,
    key text NOT NULL COLLATE pg_catalog."C",
    version text NOT NULL,
    owner_id text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    user_metadata jsonb
);


--
-- Name: s3_multipart_uploads_parts; Type: TABLE; Schema: storage; Owner: -
--

CREATE TABLE storage.s3_multipart_uploads_parts (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    upload_id text NOT NULL,
    size bigint DEFAULT 0 NOT NULL,
    part_number integer NOT NULL,
    bucket_id text NOT NULL,
    key text NOT NULL COLLATE pg_catalog."C",
    etag text NOT NULL,
    owner_id text,
    version text NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL
);


--
-- Name: refresh_tokens id; Type: DEFAULT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.refresh_tokens ALTER COLUMN id SET DEFAULT nextval('auth.refresh_tokens_id_seq'::regclass);


--
-- Name: demo_embeddings id; Type: DEFAULT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.demo_embeddings ALTER COLUMN id SET DEFAULT nextval('public.demo_embeddings_id_seq'::regclass);


--
-- Name: mfa_amr_claims amr_id_pk; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT amr_id_pk PRIMARY KEY (id);


--
-- Name: audit_log_entries audit_log_entries_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.audit_log_entries
    ADD CONSTRAINT audit_log_entries_pkey PRIMARY KEY (id);


--
-- Name: flow_state flow_state_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.flow_state
    ADD CONSTRAINT flow_state_pkey PRIMARY KEY (id);


--
-- Name: identities identities_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_pkey PRIMARY KEY (id);


--
-- Name: identities identities_provider_id_provider_unique; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_provider_id_provider_unique UNIQUE (provider_id, provider);


--
-- Name: instances instances_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.instances
    ADD CONSTRAINT instances_pkey PRIMARY KEY (id);


--
-- Name: mfa_amr_claims mfa_amr_claims_session_id_authentication_method_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT mfa_amr_claims_session_id_authentication_method_pkey UNIQUE (session_id, authentication_method);


--
-- Name: mfa_challenges mfa_challenges_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_challenges
    ADD CONSTRAINT mfa_challenges_pkey PRIMARY KEY (id);


--
-- Name: mfa_factors mfa_factors_last_challenged_at_key; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_last_challenged_at_key UNIQUE (last_challenged_at);


--
-- Name: mfa_factors mfa_factors_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_pkey PRIMARY KEY (id);


--
-- Name: one_time_tokens one_time_tokens_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.one_time_tokens
    ADD CONSTRAINT one_time_tokens_pkey PRIMARY KEY (id);


--
-- Name: refresh_tokens refresh_tokens_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_pkey PRIMARY KEY (id);


--
-- Name: refresh_tokens refresh_tokens_token_unique; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_token_unique UNIQUE (token);


--
-- Name: saml_providers saml_providers_entity_id_key; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_entity_id_key UNIQUE (entity_id);


--
-- Name: saml_providers saml_providers_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_pkey PRIMARY KEY (id);


--
-- Name: saml_relay_states saml_relay_states_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: sessions sessions_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.sessions
    ADD CONSTRAINT sessions_pkey PRIMARY KEY (id);


--
-- Name: sso_domains sso_domains_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.sso_domains
    ADD CONSTRAINT sso_domains_pkey PRIMARY KEY (id);


--
-- Name: sso_providers sso_providers_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.sso_providers
    ADD CONSTRAINT sso_providers_pkey PRIMARY KEY (id);


--
-- Name: users users_phone_key; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_phone_key UNIQUE (phone);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: admin_sessions admin_sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.admin_sessions
    ADD CONSTRAINT admin_sessions_pkey PRIMARY KEY (id);


--
-- Name: admin_sessions admin_sessions_token_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.admin_sessions
    ADD CONSTRAINT admin_sessions_token_key UNIQUE (token);


--
-- Name: admin_verification_codes admin_verification_codes_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.admin_verification_codes
    ADD CONSTRAINT admin_verification_codes_pkey PRIMARY KEY (id);


--
-- Name: contextual_prompts contextual_prompts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.contextual_prompts
    ADD CONSTRAINT contextual_prompts_pkey PRIMARY KEY (id);


--
-- Name: demo_browsing_logs demo_browsing_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.demo_browsing_logs
    ADD CONSTRAINT demo_browsing_logs_pkey PRIMARY KEY (id);


--
-- Name: demo_contacts demo_contacts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.demo_contacts
    ADD CONSTRAINT demo_contacts_pkey PRIMARY KEY (id);


--
-- Name: demo_documents demo_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.demo_documents
    ADD CONSTRAINT demo_documents_pkey PRIMARY KEY (id);


--
-- Name: demo_embeddings demo_embeddings_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.demo_embeddings
    ADD CONSTRAINT demo_embeddings_pkey PRIMARY KEY (id);


--
-- Name: demo_intake_data demo_intake_data_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.demo_intake_data
    ADD CONSTRAINT demo_intake_data_pkey PRIMARY KEY (id);


--
-- Name: demo_interactions demo_interactions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.demo_interactions
    ADD CONSTRAINT demo_interactions_pkey PRIMARY KEY (id);


--
-- Name: demo_sessions demo_sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.demo_sessions
    ADD CONSTRAINT demo_sessions_pkey PRIMARY KEY (id);


--
-- Name: demo_users demo_users_email_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.demo_users
    ADD CONSTRAINT demo_users_email_key UNIQUE (email);


--
-- Name: demo_users demo_users_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.demo_users
    ADD CONSTRAINT demo_users_pkey PRIMARY KEY (id);


--
-- Name: demo_verification_requests demo_verification_requests_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.demo_verification_requests
    ADD CONSTRAINT demo_verification_requests_pkey PRIMARY KEY (id);


--
-- Name: demo_verification_requests demo_verification_requests_verification_token_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.demo_verification_requests
    ADD CONSTRAINT demo_verification_requests_verification_token_key UNIQUE (verification_token);


--
-- Name: event_logs event_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.event_logs
    ADD CONSTRAINT event_logs_pkey PRIMARY KEY (id);


--
-- Name: faq_items faq_items_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.faq_items
    ADD CONSTRAINT faq_items_pkey PRIMARY KEY (id);


--
-- Name: goal_analytics goal_analytics_date_goal_type_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.goal_analytics
    ADD CONSTRAINT goal_analytics_date_goal_type_key UNIQUE (date, goal_type);


--
-- Name: goal_analytics goal_analytics_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.goal_analytics
    ADD CONSTRAINT goal_analytics_pkey PRIMARY KEY (id);


--
-- Name: knowledge_base knowledge_base_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.knowledge_base
    ADD CONSTRAINT knowledge_base_pkey PRIMARY KEY (id);


--
-- Name: knowledge_documents knowledge_documents_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.knowledge_documents
    ADD CONSTRAINT knowledge_documents_pkey PRIMARY KEY (id);


--
-- Name: persistent_admin_sessions persistent_admin_sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.persistent_admin_sessions
    ADD CONSTRAINT persistent_admin_sessions_pkey PRIMARY KEY (token);


--
-- Name: product_registrations product_registrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.product_registrations
    ADD CONSTRAINT product_registrations_pkey PRIMARY KEY (id);


--
-- Name: prompt_performance_metrics prompt_performance_metrics_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompt_performance_metrics
    ADD CONSTRAINT prompt_performance_metrics_pkey PRIMARY KEY (id);


--
-- Name: prompt_performance_metrics prompt_performance_metrics_prompt_id_date_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompt_performance_metrics
    ADD CONSTRAINT prompt_performance_metrics_prompt_id_date_key UNIQUE (prompt_id, date);


--
-- Name: prompt_usage_logs prompt_usage_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompt_usage_logs
    ADD CONSTRAINT prompt_usage_logs_pkey PRIMARY KEY (id);


--
-- Name: response_templates response_templates_intent_name_category_name_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.response_templates
    ADD CONSTRAINT response_templates_intent_name_category_name_key UNIQUE (intent_name, category_name);


--
-- Name: response_templates response_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.response_templates
    ADD CONSTRAINT response_templates_pkey PRIMARY KEY (id);


--
-- Name: user_custom_prompts user_custom_prompts_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_custom_prompts
    ADD CONSTRAINT user_custom_prompts_pkey PRIMARY KEY (id);


--
-- Name: user_queries_log user_queries_log_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_queries_log
    ADD CONSTRAINT user_queries_log_pkey PRIMARY KEY (id);


--
-- Name: messages messages_pkey; Type: CONSTRAINT; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.messages
    ADD CONSTRAINT messages_pkey PRIMARY KEY (id, inserted_at);


--
-- Name: subscription pk_subscription; Type: CONSTRAINT; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.subscription
    ADD CONSTRAINT pk_subscription PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: realtime; Owner: -
--

ALTER TABLE ONLY realtime.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: buckets_analytics buckets_analytics_pkey; Type: CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.buckets_analytics
    ADD CONSTRAINT buckets_analytics_pkey PRIMARY KEY (id);


--
-- Name: buckets buckets_pkey; Type: CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.buckets
    ADD CONSTRAINT buckets_pkey PRIMARY KEY (id);


--
-- Name: migrations migrations_name_key; Type: CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.migrations
    ADD CONSTRAINT migrations_name_key UNIQUE (name);


--
-- Name: migrations migrations_pkey; Type: CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.migrations
    ADD CONSTRAINT migrations_pkey PRIMARY KEY (id);


--
-- Name: objects objects_pkey; Type: CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.objects
    ADD CONSTRAINT objects_pkey PRIMARY KEY (id);


--
-- Name: prefixes prefixes_pkey; Type: CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.prefixes
    ADD CONSTRAINT prefixes_pkey PRIMARY KEY (bucket_id, level, name);


--
-- Name: s3_multipart_uploads_parts s3_multipart_uploads_parts_pkey; Type: CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.s3_multipart_uploads_parts
    ADD CONSTRAINT s3_multipart_uploads_parts_pkey PRIMARY KEY (id);


--
-- Name: s3_multipart_uploads s3_multipart_uploads_pkey; Type: CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.s3_multipart_uploads
    ADD CONSTRAINT s3_multipart_uploads_pkey PRIMARY KEY (id);


--
-- Name: audit_logs_instance_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX audit_logs_instance_id_idx ON auth.audit_log_entries USING btree (instance_id);


--
-- Name: confirmation_token_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX confirmation_token_idx ON auth.users USING btree (confirmation_token) WHERE ((confirmation_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: email_change_token_current_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX email_change_token_current_idx ON auth.users USING btree (email_change_token_current) WHERE ((email_change_token_current)::text !~ '^[0-9 ]*$'::text);


--
-- Name: email_change_token_new_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX email_change_token_new_idx ON auth.users USING btree (email_change_token_new) WHERE ((email_change_token_new)::text !~ '^[0-9 ]*$'::text);


--
-- Name: factor_id_created_at_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX factor_id_created_at_idx ON auth.mfa_factors USING btree (user_id, created_at);


--
-- Name: flow_state_created_at_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX flow_state_created_at_idx ON auth.flow_state USING btree (created_at DESC);


--
-- Name: identities_email_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX identities_email_idx ON auth.identities USING btree (email text_pattern_ops);


--
-- Name: INDEX identities_email_idx; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON INDEX auth.identities_email_idx IS 'Auth: Ensures indexed queries on the email column';


--
-- Name: identities_user_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX identities_user_id_idx ON auth.identities USING btree (user_id);


--
-- Name: idx_auth_code; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX idx_auth_code ON auth.flow_state USING btree (auth_code);


--
-- Name: idx_user_id_auth_method; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX idx_user_id_auth_method ON auth.flow_state USING btree (user_id, authentication_method);


--
-- Name: mfa_challenge_created_at_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX mfa_challenge_created_at_idx ON auth.mfa_challenges USING btree (created_at DESC);


--
-- Name: mfa_factors_user_friendly_name_unique; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX mfa_factors_user_friendly_name_unique ON auth.mfa_factors USING btree (friendly_name, user_id) WHERE (TRIM(BOTH FROM friendly_name) <> ''::text);


--
-- Name: mfa_factors_user_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX mfa_factors_user_id_idx ON auth.mfa_factors USING btree (user_id);


--
-- Name: one_time_tokens_relates_to_hash_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX one_time_tokens_relates_to_hash_idx ON auth.one_time_tokens USING hash (relates_to);


--
-- Name: one_time_tokens_token_hash_hash_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX one_time_tokens_token_hash_hash_idx ON auth.one_time_tokens USING hash (token_hash);


--
-- Name: one_time_tokens_user_id_token_type_key; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX one_time_tokens_user_id_token_type_key ON auth.one_time_tokens USING btree (user_id, token_type);


--
-- Name: reauthentication_token_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX reauthentication_token_idx ON auth.users USING btree (reauthentication_token) WHERE ((reauthentication_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: recovery_token_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX recovery_token_idx ON auth.users USING btree (recovery_token) WHERE ((recovery_token)::text !~ '^[0-9 ]*$'::text);


--
-- Name: refresh_tokens_instance_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX refresh_tokens_instance_id_idx ON auth.refresh_tokens USING btree (instance_id);


--
-- Name: refresh_tokens_instance_id_user_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX refresh_tokens_instance_id_user_id_idx ON auth.refresh_tokens USING btree (instance_id, user_id);


--
-- Name: refresh_tokens_parent_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX refresh_tokens_parent_idx ON auth.refresh_tokens USING btree (parent);


--
-- Name: refresh_tokens_session_id_revoked_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX refresh_tokens_session_id_revoked_idx ON auth.refresh_tokens USING btree (session_id, revoked);


--
-- Name: refresh_tokens_updated_at_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX refresh_tokens_updated_at_idx ON auth.refresh_tokens USING btree (updated_at DESC);


--
-- Name: saml_providers_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX saml_providers_sso_provider_id_idx ON auth.saml_providers USING btree (sso_provider_id);


--
-- Name: saml_relay_states_created_at_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX saml_relay_states_created_at_idx ON auth.saml_relay_states USING btree (created_at DESC);


--
-- Name: saml_relay_states_for_email_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX saml_relay_states_for_email_idx ON auth.saml_relay_states USING btree (for_email);


--
-- Name: saml_relay_states_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX saml_relay_states_sso_provider_id_idx ON auth.saml_relay_states USING btree (sso_provider_id);


--
-- Name: sessions_not_after_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX sessions_not_after_idx ON auth.sessions USING btree (not_after DESC);


--
-- Name: sessions_user_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX sessions_user_id_idx ON auth.sessions USING btree (user_id);


--
-- Name: sso_domains_domain_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX sso_domains_domain_idx ON auth.sso_domains USING btree (lower(domain));


--
-- Name: sso_domains_sso_provider_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX sso_domains_sso_provider_id_idx ON auth.sso_domains USING btree (sso_provider_id);


--
-- Name: sso_providers_resource_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX sso_providers_resource_id_idx ON auth.sso_providers USING btree (lower(resource_id));


--
-- Name: unique_phone_factor_per_user; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX unique_phone_factor_per_user ON auth.mfa_factors USING btree (user_id, phone);


--
-- Name: user_id_created_at_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX user_id_created_at_idx ON auth.sessions USING btree (user_id, created_at);


--
-- Name: users_email_partial_key; Type: INDEX; Schema: auth; Owner: -
--

CREATE UNIQUE INDEX users_email_partial_key ON auth.users USING btree (email) WHERE (is_sso_user = false);


--
-- Name: INDEX users_email_partial_key; Type: COMMENT; Schema: auth; Owner: -
--

COMMENT ON INDEX auth.users_email_partial_key IS 'Auth: A partial unique index that applies only when is_sso_user is false';


--
-- Name: users_instance_id_email_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX users_instance_id_email_idx ON auth.users USING btree (instance_id, lower((email)::text));


--
-- Name: users_instance_id_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX users_instance_id_idx ON auth.users USING btree (instance_id);


--
-- Name: users_is_anonymous_idx; Type: INDEX; Schema: auth; Owner: -
--

CREATE INDEX users_is_anonymous_idx ON auth.users USING btree (is_anonymous);


--
-- Name: idx_admin_codes_code_unused; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_admin_codes_code_unused ON public.admin_verification_codes USING btree (code) WHERE (is_used = false);


--
-- Name: idx_admin_codes_expires; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_admin_codes_expires ON public.admin_verification_codes USING btree (expires_at);


--
-- Name: idx_admin_codes_lookup; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_admin_codes_lookup ON public.admin_verification_codes USING btree (code, is_used, expires_at);


--
-- Name: idx_admin_sessions_token; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_admin_sessions_token ON public.admin_sessions USING btree (token);


--
-- Name: idx_contextual_prompts_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_contextual_prompts_active ON public.contextual_prompts USING btree (is_active);


--
-- Name: idx_contextual_prompts_tone; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_contextual_prompts_tone ON public.contextual_prompts USING btree (tone);


--
-- Name: idx_contextual_prompts_usage; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_contextual_prompts_usage ON public.contextual_prompts USING btree (usage_count DESC);


--
-- Name: idx_demo_contacts_created; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_contacts_created ON public.demo_contacts USING btree (created_at);


--
-- Name: idx_demo_contacts_email; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_contacts_email ON public.demo_contacts USING btree (email);


--
-- Name: idx_demo_contacts_qualification; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_contacts_qualification ON public.demo_contacts USING btree (lead_qualification);


--
-- Name: idx_demo_contacts_score; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_contacts_score ON public.demo_contacts USING btree (lead_score DESC);


--
-- Name: idx_demo_contacts_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_contacts_type ON public.demo_contacts USING btree (application_type);


--
-- Name: idx_demo_documents_session; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_documents_session ON public.demo_documents USING btree (session_id, created_at);


--
-- Name: idx_demo_documents_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_documents_type ON public.demo_documents USING btree (doc_type);


--
-- Name: idx_demo_embeddings_created_at; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_embeddings_created_at ON public.demo_embeddings USING btree (created_at);


--
-- Name: idx_demo_embeddings_session_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_embeddings_session_id ON public.demo_embeddings USING btree (session_id);


--
-- Name: idx_demo_interactions_event; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_interactions_event ON public.demo_interactions USING btree (event_type, created_at);


--
-- Name: idx_demo_interactions_session; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_interactions_session ON public.demo_interactions USING btree (session_id, created_at);


--
-- Name: idx_demo_sessions_email; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_sessions_email ON public.demo_sessions USING btree (user_email);


--
-- Name: idx_demo_sessions_goal; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_sessions_goal ON public.demo_sessions USING btree (detected_goal);


--
-- Name: idx_demo_sessions_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_sessions_status ON public.demo_sessions USING btree (status, updated_at);


--
-- Name: idx_demo_sessions_user; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_sessions_user ON public.demo_sessions USING btree (user_id);


--
-- Name: idx_demo_sessions_user_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_sessions_user_type ON public.demo_sessions USING btree (user_type);


--
-- Name: idx_demo_users_admin_role; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_users_admin_role ON public.demo_users USING btree (admin_role) WHERE (user_type = 'admin'::text);


--
-- Name: idx_demo_users_created; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_users_created ON public.demo_users USING btree (created_at);


--
-- Name: idx_demo_users_email; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_users_email ON public.demo_users USING btree (email);


--
-- Name: idx_demo_users_last_login; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_users_last_login ON public.demo_users USING btree (last_login) WHERE (user_type = 'admin'::text);


--
-- Name: idx_demo_users_lead_score; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_users_lead_score ON public.demo_users USING btree (lead_score DESC);


--
-- Name: idx_demo_users_session; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_users_session ON public.demo_users USING btree (demo_session_id);


--
-- Name: idx_demo_users_user_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_users_user_type ON public.demo_users USING btree (user_type);


--
-- Name: idx_demo_users_verification_token; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_users_verification_token ON public.demo_users USING btree (verification_token);


--
-- Name: idx_demo_users_verified; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_demo_users_verified ON public.demo_users USING btree (email_verified, created_at);


--
-- Name: idx_event_logs_event_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_event_logs_event_type ON public.event_logs USING btree (event_type);


--
-- Name: idx_event_logs_session_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_event_logs_session_id ON public.event_logs USING btree (session_id);


--
-- Name: idx_event_logs_timestamp; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_event_logs_timestamp ON public.event_logs USING btree ("timestamp");


--
-- Name: idx_faq_items_category; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_faq_items_category ON public.faq_items USING btree (category, status);


--
-- Name: idx_faq_items_helpful; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_faq_items_helpful ON public.faq_items USING btree (helpful_votes DESC);


--
-- Name: idx_goal_analytics_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_goal_analytics_date ON public.goal_analytics USING btree (date);


--
-- Name: idx_goal_analytics_goal_type; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_goal_analytics_goal_type ON public.goal_analytics USING btree (goal_type);


--
-- Name: idx_knowledge_base_category; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_knowledge_base_category ON public.knowledge_base USING btree (category, status);


--
-- Name: idx_knowledge_base_status; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_knowledge_base_status ON public.knowledge_base USING btree (status, updated_at);


--
-- Name: idx_knowledge_documents_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_knowledge_documents_active ON public.knowledge_documents USING btree (is_active);


--
-- Name: idx_knowledge_documents_category; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_knowledge_documents_category ON public.knowledge_documents USING btree (category);


--
-- Name: idx_knowledge_documents_embedding; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_knowledge_documents_embedding ON public.knowledge_documents USING ivfflat (embedding public.vector_cosine_ops) WITH (lists='100');


--
-- Name: idx_prompt_performance_date; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompt_performance_date ON public.prompt_performance_metrics USING btree (date);


--
-- Name: idx_prompt_performance_prompt; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompt_performance_prompt ON public.prompt_performance_metrics USING btree (prompt_id);


--
-- Name: idx_prompt_usage_logs_prompt_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompt_usage_logs_prompt_id ON public.prompt_usage_logs USING btree (prompt_id);


--
-- Name: idx_prompt_usage_logs_session; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompt_usage_logs_session ON public.prompt_usage_logs USING btree (session_id);


--
-- Name: idx_prompt_usage_logs_timestamp; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_prompt_usage_logs_timestamp ON public.prompt_usage_logs USING btree ("timestamp");


--
-- Name: idx_response_templates_intent; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_response_templates_intent ON public.response_templates USING btree (intent_name, category_name);


--
-- Name: idx_user_custom_prompts_active; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_custom_prompts_active ON public.user_custom_prompts USING btree (is_active);


--
-- Name: idx_user_custom_prompts_user_id; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_custom_prompts_user_id ON public.user_custom_prompts USING btree (user_id);


--
-- Name: idx_user_queries_intent; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_queries_intent ON public.user_queries_log USING btree (query_intent, query_category);


--
-- Name: idx_user_queries_session; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_user_queries_session ON public.user_queries_log USING btree (session_id, created_at);


--
-- Name: idx_verification_created; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_verification_created ON public.demo_verification_requests USING btree (created_at);


--
-- Name: idx_verification_email; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_verification_email ON public.demo_verification_requests USING btree (email, is_used);


--
-- Name: idx_verification_expires; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_verification_expires ON public.demo_verification_requests USING btree (expires_at);


--
-- Name: idx_verification_token; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX idx_verification_token ON public.demo_verification_requests USING btree (verification_token);


--
-- Name: product_registrations_created_at_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_registrations_created_at_idx ON public.product_registrations USING btree (created_at DESC);


--
-- Name: product_registrations_email_verified_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_registrations_email_verified_idx ON public.product_registrations USING btree (email_verified);


--
-- Name: product_registrations_product_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_registrations_product_id_idx ON public.product_registrations USING btree (product_id);


--
-- Name: product_registrations_product_status_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_registrations_product_status_idx ON public.product_registrations USING btree (product_id, status);


--
-- Name: product_registrations_program_status_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_registrations_program_status_idx ON public.product_registrations USING btree (program_type, status);


--
-- Name: product_registrations_program_type_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_registrations_program_type_idx ON public.product_registrations USING btree (program_type);


--
-- Name: product_registrations_search_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_registrations_search_idx ON public.product_registrations USING gin (to_tsvector('english'::regconfig, ((((((((full_name || ' '::text) || company) || ' '::text) || job_title) || ' '::text) || primary_business) || ' '::text) || use_case)));


--
-- Name: product_registrations_status_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_registrations_status_idx ON public.product_registrations USING btree (status);


--
-- Name: product_registrations_unverified_email_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_registrations_unverified_email_idx ON public.product_registrations USING btree (lower(email), email_verified, created_at);


--
-- Name: product_registrations_user_id_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE INDEX product_registrations_user_id_idx ON public.product_registrations USING btree (user_id);


--
-- Name: product_registrations_verified_email_product_program_idx; Type: INDEX; Schema: public; Owner: -
--

CREATE UNIQUE INDEX product_registrations_verified_email_product_program_idx ON public.product_registrations USING btree (lower(email), product_id, program_type) WHERE (email_verified = true);


--
-- Name: ix_realtime_subscription_entity; Type: INDEX; Schema: realtime; Owner: -
--

CREATE INDEX ix_realtime_subscription_entity ON realtime.subscription USING btree (entity);


--
-- Name: subscription_subscription_id_entity_filters_key; Type: INDEX; Schema: realtime; Owner: -
--

CREATE UNIQUE INDEX subscription_subscription_id_entity_filters_key ON realtime.subscription USING btree (subscription_id, entity, filters);


--
-- Name: bname; Type: INDEX; Schema: storage; Owner: -
--

CREATE UNIQUE INDEX bname ON storage.buckets USING btree (name);


--
-- Name: bucketid_objname; Type: INDEX; Schema: storage; Owner: -
--

CREATE UNIQUE INDEX bucketid_objname ON storage.objects USING btree (bucket_id, name);


--
-- Name: idx_multipart_uploads_list; Type: INDEX; Schema: storage; Owner: -
--

CREATE INDEX idx_multipart_uploads_list ON storage.s3_multipart_uploads USING btree (bucket_id, key, created_at);


--
-- Name: idx_name_bucket_level_unique; Type: INDEX; Schema: storage; Owner: -
--

CREATE UNIQUE INDEX idx_name_bucket_level_unique ON storage.objects USING btree (name COLLATE "C", bucket_id, level);


--
-- Name: idx_objects_bucket_id_name; Type: INDEX; Schema: storage; Owner: -
--

CREATE INDEX idx_objects_bucket_id_name ON storage.objects USING btree (bucket_id, name COLLATE "C");


--
-- Name: idx_objects_lower_name; Type: INDEX; Schema: storage; Owner: -
--

CREATE INDEX idx_objects_lower_name ON storage.objects USING btree ((path_tokens[level]), lower(name) text_pattern_ops, bucket_id, level);


--
-- Name: idx_prefixes_lower_name; Type: INDEX; Schema: storage; Owner: -
--

CREATE INDEX idx_prefixes_lower_name ON storage.prefixes USING btree (bucket_id, level, ((string_to_array(name, '/'::text))[level]), lower(name) text_pattern_ops);


--
-- Name: name_prefix_search; Type: INDEX; Schema: storage; Owner: -
--

CREATE INDEX name_prefix_search ON storage.objects USING btree (name text_pattern_ops);


--
-- Name: objects_bucket_id_level_idx; Type: INDEX; Schema: storage; Owner: -
--

CREATE UNIQUE INDEX objects_bucket_id_level_idx ON storage.objects USING btree (bucket_id, level, name COLLATE "C");


--
-- Name: users link_registration_trigger; Type: TRIGGER; Schema: auth; Owner: -
--

CREATE TRIGGER link_registration_trigger AFTER INSERT ON auth.users FOR EACH ROW EXECUTE FUNCTION public.link_registration_with_auth_user();


--
-- Name: users sync_email_verification_trigger; Type: TRIGGER; Schema: auth; Owner: -
--

CREATE TRIGGER sync_email_verification_trigger AFTER UPDATE ON auth.users FOR EACH ROW EXECUTE FUNCTION public.sync_email_verification_status();


--
-- Name: product_registrations normalize_registration_data_trigger; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER normalize_registration_data_trigger BEFORE INSERT OR UPDATE ON public.product_registrations FOR EACH ROW EXECUTE FUNCTION public.normalize_registration_data();


--
-- Name: demo_documents update_demo_documents_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_demo_documents_updated_at BEFORE UPDATE ON public.demo_documents FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: demo_sessions update_demo_sessions_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_demo_sessions_updated_at BEFORE UPDATE ON public.demo_sessions FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: demo_users update_demo_users_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_demo_users_updated_at BEFORE UPDATE ON public.demo_users FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: demo_verification_requests update_demo_verification_requests_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_demo_verification_requests_updated_at BEFORE UPDATE ON public.demo_verification_requests FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: faq_items update_faq_items_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_faq_items_updated_at BEFORE UPDATE ON public.faq_items FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: knowledge_base update_knowledge_base_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_knowledge_base_updated_at BEFORE UPDATE ON public.knowledge_base FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: product_registrations update_product_registrations_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_product_registrations_updated_at BEFORE UPDATE ON public.product_registrations FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: response_templates update_response_templates_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER update_response_templates_updated_at BEFORE UPDATE ON public.response_templates FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: subscription tr_check_filters; Type: TRIGGER; Schema: realtime; Owner: -
--

CREATE TRIGGER tr_check_filters BEFORE INSERT OR UPDATE ON realtime.subscription FOR EACH ROW EXECUTE FUNCTION realtime.subscription_check_filters();


--
-- Name: buckets enforce_bucket_name_length_trigger; Type: TRIGGER; Schema: storage; Owner: -
--

CREATE TRIGGER enforce_bucket_name_length_trigger BEFORE INSERT OR UPDATE OF name ON storage.buckets FOR EACH ROW EXECUTE FUNCTION storage.enforce_bucket_name_length();


--
-- Name: objects objects_delete_delete_prefix; Type: TRIGGER; Schema: storage; Owner: -
--

CREATE TRIGGER objects_delete_delete_prefix AFTER DELETE ON storage.objects FOR EACH ROW EXECUTE FUNCTION storage.delete_prefix_hierarchy_trigger();


--
-- Name: objects objects_insert_create_prefix; Type: TRIGGER; Schema: storage; Owner: -
--

CREATE TRIGGER objects_insert_create_prefix BEFORE INSERT ON storage.objects FOR EACH ROW EXECUTE FUNCTION storage.objects_insert_prefix_trigger();


--
-- Name: objects objects_update_create_prefix; Type: TRIGGER; Schema: storage; Owner: -
--

CREATE TRIGGER objects_update_create_prefix BEFORE UPDATE ON storage.objects FOR EACH ROW WHEN (((new.name <> old.name) OR (new.bucket_id <> old.bucket_id))) EXECUTE FUNCTION storage.objects_update_prefix_trigger();


--
-- Name: prefixes prefixes_create_hierarchy; Type: TRIGGER; Schema: storage; Owner: -
--

CREATE TRIGGER prefixes_create_hierarchy BEFORE INSERT ON storage.prefixes FOR EACH ROW WHEN ((pg_trigger_depth() < 1)) EXECUTE FUNCTION storage.prefixes_insert_trigger();


--
-- Name: prefixes prefixes_delete_hierarchy; Type: TRIGGER; Schema: storage; Owner: -
--

CREATE TRIGGER prefixes_delete_hierarchy AFTER DELETE ON storage.prefixes FOR EACH ROW EXECUTE FUNCTION storage.delete_prefix_hierarchy_trigger();


--
-- Name: objects update_objects_updated_at; Type: TRIGGER; Schema: storage; Owner: -
--

CREATE TRIGGER update_objects_updated_at BEFORE UPDATE ON storage.objects FOR EACH ROW EXECUTE FUNCTION storage.update_updated_at_column();


--
-- Name: identities identities_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.identities
    ADD CONSTRAINT identities_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: mfa_amr_claims mfa_amr_claims_session_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_amr_claims
    ADD CONSTRAINT mfa_amr_claims_session_id_fkey FOREIGN KEY (session_id) REFERENCES auth.sessions(id) ON DELETE CASCADE;


--
-- Name: mfa_challenges mfa_challenges_auth_factor_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_challenges
    ADD CONSTRAINT mfa_challenges_auth_factor_id_fkey FOREIGN KEY (factor_id) REFERENCES auth.mfa_factors(id) ON DELETE CASCADE;


--
-- Name: mfa_factors mfa_factors_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.mfa_factors
    ADD CONSTRAINT mfa_factors_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: one_time_tokens one_time_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.one_time_tokens
    ADD CONSTRAINT one_time_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: refresh_tokens refresh_tokens_session_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.refresh_tokens
    ADD CONSTRAINT refresh_tokens_session_id_fkey FOREIGN KEY (session_id) REFERENCES auth.sessions(id) ON DELETE CASCADE;


--
-- Name: saml_providers saml_providers_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.saml_providers
    ADD CONSTRAINT saml_providers_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: saml_relay_states saml_relay_states_flow_state_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_flow_state_id_fkey FOREIGN KEY (flow_state_id) REFERENCES auth.flow_state(id) ON DELETE CASCADE;


--
-- Name: saml_relay_states saml_relay_states_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.saml_relay_states
    ADD CONSTRAINT saml_relay_states_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: sessions sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.sessions
    ADD CONSTRAINT sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: sso_domains sso_domains_sso_provider_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: -
--

ALTER TABLE ONLY auth.sso_domains
    ADD CONSTRAINT sso_domains_sso_provider_id_fkey FOREIGN KEY (sso_provider_id) REFERENCES auth.sso_providers(id) ON DELETE CASCADE;


--
-- Name: contextual_prompts contextual_prompts_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.contextual_prompts
    ADD CONSTRAINT contextual_prompts_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id);


--
-- Name: demo_contacts demo_contacts_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.demo_contacts
    ADD CONSTRAINT demo_contacts_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.demo_sessions(id) ON DELETE CASCADE;


--
-- Name: demo_documents demo_documents_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.demo_documents
    ADD CONSTRAINT demo_documents_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.demo_sessions(id) ON DELETE CASCADE;


--
-- Name: demo_interactions demo_interactions_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.demo_interactions
    ADD CONSTRAINT demo_interactions_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.demo_sessions(id) ON DELETE CASCADE;


--
-- Name: demo_users demo_users_demo_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.demo_users
    ADD CONSTRAINT demo_users_demo_session_id_fkey FOREIGN KEY (demo_session_id) REFERENCES public.demo_sessions(id) ON DELETE SET NULL;


--
-- Name: demo_verification_requests demo_verification_requests_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.demo_verification_requests
    ADD CONSTRAINT demo_verification_requests_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.demo_sessions(id) ON DELETE SET NULL;


--
-- Name: event_logs event_logs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.event_logs
    ADD CONSTRAINT event_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE SET NULL;


--
-- Name: demo_sessions fk_demo_sessions_user; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.demo_sessions
    ADD CONSTRAINT fk_demo_sessions_user FOREIGN KEY (user_id) REFERENCES public.demo_users(id) ON DELETE SET NULL;


--
-- Name: prompt_performance_metrics prompt_performance_metrics_prompt_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompt_performance_metrics
    ADD CONSTRAINT prompt_performance_metrics_prompt_id_fkey FOREIGN KEY (prompt_id) REFERENCES public.contextual_prompts(id);


--
-- Name: prompt_usage_logs prompt_usage_logs_prompt_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.prompt_usage_logs
    ADD CONSTRAINT prompt_usage_logs_prompt_id_fkey FOREIGN KEY (prompt_id) REFERENCES public.contextual_prompts(id);


--
-- Name: user_custom_prompts user_custom_prompts_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_custom_prompts
    ADD CONSTRAINT user_custom_prompts_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;


--
-- Name: user_queries_log user_queries_log_session_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.user_queries_log
    ADD CONSTRAINT user_queries_log_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.demo_sessions(id) ON DELETE CASCADE;


--
-- Name: objects objects_bucketId_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.objects
    ADD CONSTRAINT "objects_bucketId_fkey" FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: prefixes prefixes_bucketId_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.prefixes
    ADD CONSTRAINT "prefixes_bucketId_fkey" FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: s3_multipart_uploads s3_multipart_uploads_bucket_id_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.s3_multipart_uploads
    ADD CONSTRAINT s3_multipart_uploads_bucket_id_fkey FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: s3_multipart_uploads_parts s3_multipart_uploads_parts_bucket_id_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.s3_multipart_uploads_parts
    ADD CONSTRAINT s3_multipart_uploads_parts_bucket_id_fkey FOREIGN KEY (bucket_id) REFERENCES storage.buckets(id);


--
-- Name: s3_multipart_uploads_parts s3_multipart_uploads_parts_upload_id_fkey; Type: FK CONSTRAINT; Schema: storage; Owner: -
--

ALTER TABLE ONLY storage.s3_multipart_uploads_parts
    ADD CONSTRAINT s3_multipart_uploads_parts_upload_id_fkey FOREIGN KEY (upload_id) REFERENCES storage.s3_multipart_uploads(id) ON DELETE CASCADE;


--
-- Name: audit_log_entries; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.audit_log_entries ENABLE ROW LEVEL SECURITY;

--
-- Name: flow_state; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.flow_state ENABLE ROW LEVEL SECURITY;

--
-- Name: identities; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.identities ENABLE ROW LEVEL SECURITY;

--
-- Name: instances; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.instances ENABLE ROW LEVEL SECURITY;

--
-- Name: mfa_amr_claims; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.mfa_amr_claims ENABLE ROW LEVEL SECURITY;

--
-- Name: mfa_challenges; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.mfa_challenges ENABLE ROW LEVEL SECURITY;

--
-- Name: mfa_factors; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.mfa_factors ENABLE ROW LEVEL SECURITY;

--
-- Name: one_time_tokens; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.one_time_tokens ENABLE ROW LEVEL SECURITY;

--
-- Name: refresh_tokens; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.refresh_tokens ENABLE ROW LEVEL SECURITY;

--
-- Name: saml_providers; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.saml_providers ENABLE ROW LEVEL SECURITY;

--
-- Name: saml_relay_states; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.saml_relay_states ENABLE ROW LEVEL SECURITY;

--
-- Name: schema_migrations; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.schema_migrations ENABLE ROW LEVEL SECURITY;

--
-- Name: sessions; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.sessions ENABLE ROW LEVEL SECURITY;

--
-- Name: sso_domains; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.sso_domains ENABLE ROW LEVEL SECURITY;

--
-- Name: sso_providers; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.sso_providers ENABLE ROW LEVEL SECURITY;

--
-- Name: users; Type: ROW SECURITY; Schema: auth; Owner: -
--

ALTER TABLE auth.users ENABLE ROW LEVEL SECURITY;

--
-- Name: demo_users Admins can access all data; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Admins can access all data" ON public.demo_users USING (((user_type = 'admin'::text) OR (email = ANY (ARRAY['<EMAIL>'::text, '<EMAIL>'::text]))));


--
-- Name: demo_sessions Admins can access all sessions; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Admins can access all sessions" ON public.demo_sessions USING (((user_type = 'admin'::text) OR (user_email = ANY (ARRAY['<EMAIL>'::text, '<EMAIL>'::text]))));


--
-- Name: admin_verification_codes Allow all admin code operations; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow all admin code operations" ON public.admin_verification_codes USING (true);


--
-- Name: demo_contacts Allow all demo_contacts operations; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow all demo_contacts operations" ON public.demo_contacts USING (true);


--
-- Name: demo_documents Allow all demo_documents operations; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow all demo_documents operations" ON public.demo_documents USING (true);


--
-- Name: demo_interactions Allow all demo_interactions operations; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow all demo_interactions operations" ON public.demo_interactions USING (true);


--
-- Name: demo_sessions Allow all demo_sessions operations; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow all demo_sessions operations" ON public.demo_sessions USING (true);


--
-- Name: demo_users Allow all demo_users operations; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow all demo_users operations" ON public.demo_users USING (true);


--
-- Name: user_queries_log Allow all user_queries_log operations; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow all user_queries_log operations" ON public.user_queries_log USING (true);


--
-- Name: faq_items Allow read faq_items; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow read faq_items" ON public.faq_items FOR SELECT USING ((status = 'published'::text));


--
-- Name: knowledge_base Allow read knowledge_base; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow read knowledge_base" ON public.knowledge_base FOR SELECT USING ((status = 'published'::text));


--
-- Name: response_templates Allow read response_templates; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow read response_templates" ON public.response_templates FOR SELECT USING ((status = 'active'::text));


--
-- Name: demo_verification_requests Allow verification operations; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Allow verification operations" ON public.demo_verification_requests USING (true);


--
-- Name: contextual_prompts Authenticated users can read active prompts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Authenticated users can read active prompts" ON public.contextual_prompts FOR SELECT USING (((auth.role() = 'authenticated'::text) AND (is_active = true)));


--
-- Name: goal_analytics Authenticated users can read analytics; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Authenticated users can read analytics" ON public.goal_analytics FOR SELECT USING ((auth.role() = 'authenticated'::text));


--
-- Name: knowledge_documents Authenticated users can read knowledge docs; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Authenticated users can read knowledge docs" ON public.knowledge_documents FOR SELECT USING (((auth.role() = 'authenticated'::text) AND (is_active = true)));


--
-- Name: prompt_performance_metrics Authenticated users can read performance metrics; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Authenticated users can read performance metrics" ON public.prompt_performance_metrics FOR SELECT USING ((auth.role() = 'authenticated'::text));


--
-- Name: goal_analytics Service role can manage analytics; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Service role can manage analytics" ON public.goal_analytics USING ((auth.role() = 'service_role'::text));


--
-- Name: event_logs Service role can manage event logs; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Service role can manage event logs" ON public.event_logs USING ((auth.role() = 'service_role'::text));


--
-- Name: knowledge_documents Service role can manage knowledge docs; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Service role can manage knowledge docs" ON public.knowledge_documents USING ((auth.role() = 'service_role'::text));


--
-- Name: prompt_performance_metrics Service role can manage performance metrics; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Service role can manage performance metrics" ON public.prompt_performance_metrics USING ((auth.role() = 'service_role'::text));


--
-- Name: contextual_prompts Service role can manage prompts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Service role can manage prompts" ON public.contextual_prompts USING ((auth.role() = 'service_role'::text));


--
-- Name: prompt_usage_logs Service role can manage usage logs; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Service role can manage usage logs" ON public.prompt_usage_logs USING ((auth.role() = 'service_role'::text));


--
-- Name: user_custom_prompts Service role can read all custom prompts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Service role can read all custom prompts" ON public.user_custom_prompts FOR SELECT USING ((auth.role() = 'service_role'::text));


--
-- Name: user_custom_prompts Users can manage their own custom prompts; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY "Users can manage their own custom prompts" ON public.user_custom_prompts USING ((auth.uid() = user_id));


--
-- Name: product_registrations admin_dashboard_read_access; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY admin_dashboard_read_access ON public.product_registrations FOR SELECT USING (true);


--
-- Name: product_registrations admin_read_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY admin_read_policy ON public.product_registrations FOR SELECT USING (true);


--
-- Name: admin_verification_codes; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.admin_verification_codes ENABLE ROW LEVEL SECURITY;

--
-- Name: product_registrations block_anonymous_access; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY block_anonymous_access ON public.product_registrations USING ((auth.role() <> 'anon'::text)) WITH CHECK ((auth.role() <> 'anon'::text));


--
-- Name: contextual_prompts; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.contextual_prompts ENABLE ROW LEVEL SECURITY;

--
-- Name: demo_browsing_logs; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.demo_browsing_logs ENABLE ROW LEVEL SECURITY;

--
-- Name: demo_contacts; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.demo_contacts ENABLE ROW LEVEL SECURITY;

--
-- Name: demo_documents; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.demo_documents ENABLE ROW LEVEL SECURITY;

--
-- Name: demo_embeddings; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.demo_embeddings ENABLE ROW LEVEL SECURITY;

--
-- Name: demo_embeddings demo_embeddings_service_role; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY demo_embeddings_service_role ON public.demo_embeddings USING (((auth.jwt() ->> 'role'::text) = 'service_role'::text));


--
-- Name: demo_intake_data; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.demo_intake_data ENABLE ROW LEVEL SECURITY;

--
-- Name: demo_interactions; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.demo_interactions ENABLE ROW LEVEL SECURITY;

--
-- Name: demo_browsing_logs demo_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY demo_policy ON public.demo_browsing_logs USING (true);


--
-- Name: demo_intake_data demo_policy; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY demo_policy ON public.demo_intake_data USING (true);


--
-- Name: demo_sessions; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.demo_sessions ENABLE ROW LEVEL SECURITY;

--
-- Name: demo_users; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.demo_users ENABLE ROW LEVEL SECURITY;

--
-- Name: demo_verification_requests; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.demo_verification_requests ENABLE ROW LEVEL SECURITY;

--
-- Name: event_logs; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.event_logs ENABLE ROW LEVEL SECURITY;

--
-- Name: faq_items; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.faq_items ENABLE ROW LEVEL SECURITY;

--
-- Name: goal_analytics; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.goal_analytics ENABLE ROW LEVEL SECURITY;

--
-- Name: knowledge_base; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.knowledge_base ENABLE ROW LEVEL SECURITY;

--
-- Name: knowledge_documents; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.knowledge_documents ENABLE ROW LEVEL SECURITY;

--
-- Name: product_registrations; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.product_registrations ENABLE ROW LEVEL SECURITY;

--
-- Name: prompt_performance_metrics; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.prompt_performance_metrics ENABLE ROW LEVEL SECURITY;

--
-- Name: prompt_usage_logs; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.prompt_usage_logs ENABLE ROW LEVEL SECURITY;

--
-- Name: response_templates; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.response_templates ENABLE ROW LEVEL SECURITY;

--
-- Name: product_registrations service_role_full_access; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY service_role_full_access ON public.product_registrations USING ((auth.role() = 'service_role'::text)) WITH CHECK ((auth.role() = 'service_role'::text));


--
-- Name: user_custom_prompts; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.user_custom_prompts ENABLE ROW LEVEL SECURITY;

--
-- Name: user_queries_log; Type: ROW SECURITY; Schema: public; Owner: -
--

ALTER TABLE public.user_queries_log ENABLE ROW LEVEL SECURITY;

--
-- Name: product_registrations users_update_own_registrations; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY users_update_own_registrations ON public.product_registrations FOR UPDATE USING ((auth.uid() = user_id)) WITH CHECK ((auth.uid() = user_id));


--
-- Name: product_registrations users_view_own_registrations; Type: POLICY; Schema: public; Owner: -
--

CREATE POLICY users_view_own_registrations ON public.product_registrations FOR SELECT USING ((auth.uid() = user_id));


--
-- Name: messages; Type: ROW SECURITY; Schema: realtime; Owner: -
--

ALTER TABLE realtime.messages ENABLE ROW LEVEL SECURITY;

--
-- Name: buckets; Type: ROW SECURITY; Schema: storage; Owner: -
--

ALTER TABLE storage.buckets ENABLE ROW LEVEL SECURITY;

--
-- Name: buckets_analytics; Type: ROW SECURITY; Schema: storage; Owner: -
--

ALTER TABLE storage.buckets_analytics ENABLE ROW LEVEL SECURITY;

--
-- Name: migrations; Type: ROW SECURITY; Schema: storage; Owner: -
--

ALTER TABLE storage.migrations ENABLE ROW LEVEL SECURITY;

--
-- Name: objects; Type: ROW SECURITY; Schema: storage; Owner: -
--

ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

--
-- Name: prefixes; Type: ROW SECURITY; Schema: storage; Owner: -
--

ALTER TABLE storage.prefixes ENABLE ROW LEVEL SECURITY;

--
-- Name: s3_multipart_uploads; Type: ROW SECURITY; Schema: storage; Owner: -
--

ALTER TABLE storage.s3_multipart_uploads ENABLE ROW LEVEL SECURITY;

--
-- Name: s3_multipart_uploads_parts; Type: ROW SECURITY; Schema: storage; Owner: -
--

ALTER TABLE storage.s3_multipart_uploads_parts ENABLE ROW LEVEL SECURITY;

--
-- Name: supabase_realtime; Type: PUBLICATION; Schema: -; Owner: -
--

CREATE PUBLICATION supabase_realtime WITH (publish = 'insert, update, delete, truncate');


--
-- Name: issue_graphql_placeholder; Type: EVENT TRIGGER; Schema: -; Owner: -
--

CREATE EVENT TRIGGER issue_graphql_placeholder ON sql_drop
         WHEN TAG IN ('DROP EXTENSION')
   EXECUTE FUNCTION extensions.set_graphql_placeholder();


--
-- Name: issue_pg_cron_access; Type: EVENT TRIGGER; Schema: -; Owner: -
--

CREATE EVENT TRIGGER issue_pg_cron_access ON ddl_command_end
         WHEN TAG IN ('CREATE EXTENSION')
   EXECUTE FUNCTION extensions.grant_pg_cron_access();


--
-- Name: issue_pg_graphql_access; Type: EVENT TRIGGER; Schema: -; Owner: -
--

CREATE EVENT TRIGGER issue_pg_graphql_access ON ddl_command_end
         WHEN TAG IN ('CREATE FUNCTION')
   EXECUTE FUNCTION extensions.grant_pg_graphql_access();


--
-- Name: issue_pg_net_access; Type: EVENT TRIGGER; Schema: -; Owner: -
--

CREATE EVENT TRIGGER issue_pg_net_access ON ddl_command_end
         WHEN TAG IN ('CREATE EXTENSION')
   EXECUTE FUNCTION extensions.grant_pg_net_access();


--
-- Name: pgrst_ddl_watch; Type: EVENT TRIGGER; Schema: -; Owner: -
--

CREATE EVENT TRIGGER pgrst_ddl_watch ON ddl_command_end
   EXECUTE FUNCTION extensions.pgrst_ddl_watch();


--
-- Name: pgrst_drop_watch; Type: EVENT TRIGGER; Schema: -; Owner: -
--

CREATE EVENT TRIGGER pgrst_drop_watch ON sql_drop
   EXECUTE FUNCTION extensions.pgrst_drop_watch();


--
-- PostgreSQL database dump complete
--

\unrestrict 4JQuT4Mt1OeWVgCeOwjOQkKpulZQXEeJm6hasWj445k26x4WgAcE28hKLKQVLLM

