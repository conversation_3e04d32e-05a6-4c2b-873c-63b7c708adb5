abstract class FeedbackRepository {
  Future<void> submitFullFeedback({
    required bool isAdmin,
    required String id,
    required String type,
    required String priority,
    required String subject,
    required String message,
    String? additionalNotes,
    String? rating,
    String? contactMethod,
    String adminStatus = 'new',
    String? adminAssignee,
    String? adminNotes,
  });

  Future<void> submitMessageFeedback({
    required String messagePreview,
    required String feedback,
  });

  Future<void> submitQuickMessageReport({
    required String messageContent,
    required String issueType,
    required String description,
  });
}
