import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../features/auth/presentation/blocs/demo_access/demo_access_cubit.dart';

class DemoAccessDialog extends StatefulWidget {
  const DemoAccessDialog({super.key});

  @override
  State<DemoAccessDialog> createState() => _DemoAccessDialogState();
}

class _DemoAccessDialogState extends State<DemoAccessDialog> {
  final _formKey = GlobalKey<FormState>();
  final _firstName = TextEditingController();
  final _lastName = TextEditingController();
  final _workEmail = TextEditingController();
  final _company = TextEditingController();
  String? _jobTitle;
  String? _companySize;
  String? _interest;
  final _password = TextEditingController();
  final _confirmPassword = TextEditingController();
  bool _agreeProcessing = false;
  bool _agreePrivacy = false;
  bool _agreeMarketing = false;
  bool _submitting = false;
  bool _success = false;

  @override
  void dispose() {
    _firstName.dispose();
    _lastName.dispose();
    _workEmail.dispose();
    _company.dispose();
    _password.dispose();
    _confirmPassword.dispose();
    super.dispose();
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate() ||
        !_agreeProcessing ||
        !_agreePrivacy) {
      setState(() {});
      return;
    }
    setState(() => _submitting = true);
    try {
      final data = {
        'firstName': _firstName.text.trim(),
        'lastName': _lastName.text.trim(),
        'workEmail': _workEmail.text.trim(),
        'company': _company.text.trim(),
        'jobTitle': _jobTitle,
        'companySize': _companySize,
        'complianceInterest': _interest,
        'agreedToDataProcessing': _agreeProcessing,
        'agreedToPrivacy': _agreePrivacy,
        'agreedToMarketing': _agreeMarketing,
      };
      final cubit = context.read<DemoAccessCubit>();
      await cubit.submit(data);
      final state = cubit.state;
      if (state.status == DemoAccessStatus.success) {
        setState(() => _success = true);
      } else if (state.status == DemoAccessStatus.failure) {
        throw Exception(state.error ?? 'Submission failed');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to submit request. Please try again.'),
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _submitting = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 720),
        child: AnimatedCrossFade(
          crossFadeState: _success
              ? CrossFadeState.showSecond
              : CrossFadeState.showFirst,
          duration: const Duration(milliseconds: 250),
          firstChild: SizedBox(
            // Cap height and allow scrolling on smaller screens
            height: MediaQuery.of(context).size.height * 0.85,
            child: SingleChildScrollView(
              padding: const EdgeInsets.only(bottom: 12),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Gradient header like screenshot
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 18,
                    ),
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Color(0xFF059669), Color(0xFF047857)],
                      ),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: const [
                        Text(
                          'Get Demo Access',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w700,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(height: 6),
                        Text(
                          'Register to access our AI-powered compliance platform',
                          style: TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 16,
                    ),
                    child: Form(
                      key: _formKey,
                      child: LayoutBuilder(
                        builder: (context, c) {
                          final twoCol = c.maxWidth >= 560;
                          return Column(
                            children: [
                              _Grid(
                                twoCol: twoCol,
                                children: [
                                  _field(_firstName, 'First Name *'),
                                  _field(_lastName, 'Last Name *'),
                                ],
                              ),
                              const SizedBox(height: 12),
                              _field(
                                _workEmail,
                                'Work Email *',
                                validator: (v) {
                                  if (v == null || v.trim().isEmpty)
                                    return 'Required';
                                  final re = RegExp(
                                    r'^[^\s@]+@[^\s@]+\.[^\s@]+$',
                                  );
                                  if (!re.hasMatch(v.trim()))
                                    return 'Invalid email';
                                  return null;
                                },
                              ),
                              const SizedBox(height: 12),
                              _field(_company, 'Company *'),
                              const SizedBox(height: 12),
                              _Grid(
                                twoCol: twoCol,
                                children: [
                                  _dropdown(
                                    'Job Title *',
                                    _jobTitle,
                                    (v) => setState(() => _jobTitle = v),
                                    const [
                                      'CISO',
                                      'Compliance Manager',
                                      'Risk Manager',
                                      'Legal Counsel',
                                      'Privacy Officer',
                                      'CEO/Founder',
                                      'CTO',
                                      'Other',
                                    ],
                                  ),
                                  _dropdown(
                                    'Company Size',
                                    _companySize,
                                    (v) => setState(() => _companySize = v),
                                    const [
                                      '1-10',
                                      '11-50',
                                      '51-250',
                                      '251-1000',
                                      '1000+',
                                    ],
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              _dropdown(
                                'Primary Compliance Interest *',
                                _interest,
                                (v) => setState(() => _interest = v),
                                const [
                                  'ISO 27001',
                                  'GDPR',
                                  'AI Governance',
                                  'SOC 2',
                                  'Multiple Standards',
                                  'General Exploration',
                                ],
                              ),
                              const SizedBox(height: 12),
                              _Grid(
                                twoCol: twoCol,
                                children: [
                                  _field(
                                    _password,
                                    'Create Password *',
                                    obscure: true,
                                    validator: (v) =>
                                        (v == null || v.length < 8)
                                        ? 'Min 8 characters'
                                        : null,
                                  ),
                                  _field(
                                    _confirmPassword,
                                    'Confirm Password *',
                                    obscure: true,
                                    validator: (v) => v != _password.text
                                        ? 'Passwords do not match'
                                        : null,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              _checkbox(
                                'I agree to the processing of my personal data for demo access purposes. *',
                                _agreeProcessing,
                                (v) => setState(
                                  () => _agreeProcessing = v ?? false,
                                ),
                                required: true,
                              ),
                              _checkbox(
                                'I have read and understood the Privacy Policy *',
                                _agreePrivacy,
                                (v) =>
                                    setState(() => _agreePrivacy = v ?? false),
                                required: true,
                              ),
                              _checkbox(
                                'I agree to receive product communications (optional)',
                                _agreeMarketing,
                                (v) => setState(
                                  () => _agreeMarketing = v ?? false,
                                ),
                              ),
                              const SizedBox(height: 10),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  TextButton(
                                    onPressed: _submitting
                                        ? null
                                        : () => Navigator.of(context).pop(),
                                    style: TextButton.styleFrom(
                                      foregroundColor: const Color(0xFF64748B),
                                    ),
                                    child: const Text('Cancel'),
                                  ),
                                  const SizedBox(width: 8),
                                  ElevatedButton(
                                    onPressed: _submitting ? null : _submit,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color(0xFF059669),
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 12,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(10),
                                      ),
                                    ),
                                    child: Text(
                                      _submitting
                                          ? 'Submitting...'
                                          : 'Request Access',
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          secondChild: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: const [
                Icon(Icons.check_circle, color: Color(0xFF10B981), size: 48),
                SizedBox(height: 12),
                Text(
                  'Access Request Submitted!',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700),
                ),
                SizedBox(height: 8),
                Text(
                  'We\'ve sent a verification email to your inbox. Please check your email and click the verification link to access the demo platform.',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  InputDecoration _dec(String label) => InputDecoration(
    labelText: label,
    filled: true,
    fillColor: Colors.white,
    contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 14),
    border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(10),
      borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(10),
      borderSide: const BorderSide(color: Color(0xFF059669), width: 2),
    ),
  );

  Widget _field(
    TextEditingController c,
    String label, {
    bool obscure = false,
    String? Function(String?)? validator,
  }) => TextFormField(
    controller: c,
    obscureText: obscure,
    decoration: _dec(label),
    validator:
        validator ?? (v) => v == null || v.trim().isEmpty ? 'Required' : null,
  );

  Widget _dropdown(
    String label,
    String? value,
    ValueChanged<String?> onChanged,
    List<String> items,
  ) => DropdownButtonFormField<String>(
    value: value,
    decoration: _dec(label),
    items: items
        .map((e) => DropdownMenuItem(value: e, child: Text(e)))
        .toList(),
    onChanged: onChanged,
    validator: (v) =>
        label.contains('*') && (v == null || v.isEmpty) ? 'Required' : null,
  );

  Widget _checkbox(
    String label,
    bool value,
    ValueChanged<bool?> onChanged, {
    bool required = false,
  }) => CheckboxListTile(
    value: value,
    onChanged: onChanged,
    controlAffinity: ListTileControlAffinity.leading,
    title: Text(label, style: const TextStyle(color: Color(0xFF0F172A))),
    subtitle: required && !value
        ? const Text('Required', style: TextStyle(color: Colors.red))
        : null,
  );
}

class _Grid extends StatelessWidget {
  final bool twoCol;
  final List<Widget> children;
  const _Grid({required this.twoCol, required this.children});

  @override
  Widget build(BuildContext context) {
    if (!twoCol) return Column(children: _withSpacing(children));
    return Row(
      children: [
        Expanded(child: children[0]),
        const SizedBox(width: 12),
        Expanded(child: children[1]),
      ],
    );
  }

  List<Widget> _withSpacing(List<Widget> kids) {
    if (kids.length < 2) return kids;
    return [kids[0], const SizedBox(height: 12), kids[1]];
  }
}
