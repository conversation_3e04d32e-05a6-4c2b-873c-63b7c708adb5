/**
 * ArionComply Demo Utility Functions
 * Version: 1.4 - Cleaned Core Utilities Only
 * Purpose: Core utility functions without demo UI code
 */

const ArionUtils = {
  // =============================================================================
  // SESSION MANAGEMENT
  // =============================================================================
  
  session: {
    generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },

    getSessionFromStorage() {
      try {
        return localStorage.getItem('arion-demo-session');
      } catch (error) {
        console.warn('localStorage not available, using memory session');
        return null;
      }
    },

    saveSessionToStorage(sessionId) {
      try {
        localStorage.setItem('arion-demo-session', sessionId);
      } catch (error) {
        console.warn('localStorage not available, session not persisted');
      }
    },

    clearSession() {
      try {
        localStorage.removeItem('arion-demo-session');
      } catch (error) {
        console.warn('localStorage not available');
      }
    }
  },

  // =============================================================================
  // API UTILITIES
  // =============================================================================
  
  api: {
    async call(action, data = {}, sessionId = null) {
      const url = `${ArionConfig.api.baseUrl}${ArionConfig.api.endpoint}`;
      
      const body = {
        action,
        sessionId,
        ...data
      };
      
      const headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-API-Key': 'arion-demo-2025',
        'Authorization': `Bearer ${ArionConfig.supabase.anonKey}`
      };
      
      if (sessionId) {
        headers['X-Session-ID'] = sessionId;
      }

      try {
        const response = await fetch(url, {
          method: 'POST',
          headers,
          body: JSON.stringify(body)
        });
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return await response.json();
      } catch (error) {
        console.error('API call failed:', error);
        throw error;
      }
    },

    async callWithRetry(action, data = {}, sessionId = null, maxRetries = 3) {
      let lastError;
      
      for (let attempt = 0; attempt < maxRetries; attempt++) {
        try {
          return await this.call(action, data, sessionId);
        } catch (error) {
          lastError = error;
          console.warn(`API call attempt ${attempt + 1} failed:`, error);
          
          if (attempt < maxRetries - 1) {
            await ArionUtils.async.delay(1000 * (attempt + 1));
          }
        }
      }
      
      throw lastError;
    },

    async initSession(sessionId, metadata = {}) {
      return await this.call(ArionConfig.api.actions.initSession, {
        userAgent: navigator.userAgent,
        referrer: document.referrer,
        timestamp: new Date().toISOString(),
        ...metadata
      }, sessionId);
    },

    async queryAgent(sessionId, message, context = {}) {
      return await this.call(ArionConfig.api.actions.queryAgent, {
        message,
        timestamp: new Date().toISOString(),
        ...context
      }, sessionId);
    },

    async generateDocuments(sessionId, goalType, intakeData = {}) {
      return await this.call(ArionConfig.api.actions.generateDocuments, {
        goalType,
        intakeData,
        timestamp: new Date().toISOString()
      }, sessionId);
    },

    async getDocuments(sessionId) {
      return await this.call(ArionConfig.api.actions.getDocuments, {}, sessionId);
    },

    async getDocument(sessionId, documentId) {
      return await this.call(ArionConfig.api.actions.getDocument, {
        documentId
      }, sessionId);
    },

    async saveContact(sessionId, contactData) {
      return await this.call(ArionConfig.api.actions.saveContact, {
        ...contactData,
        timestamp: new Date().toISOString()
      }, sessionId);
    },

    async logInteraction(sessionId, eventType, data = {}) {
      return await this.call(ArionConfig.api.actions.logInteraction, {
        eventType,
        data,
        timestamp: new Date().toISOString()
      }, sessionId);
    }
  },

  // =============================================================================
  // DATE/TIME UTILITIES
  // =============================================================================
  
  datetime: {
    formatDate(dateString, options = {}) {
      const defaultOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      };
      
      const finalOptions = { ...defaultOptions, ...options };
      return new Date(dateString).toLocaleDateString('en-US', finalOptions);
    },

    formatDateTime(dateString, options = {}) {
      const defaultOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      };
      
      const finalOptions = { ...defaultOptions, ...options };
      return new Date(dateString).toLocaleString('en-US', finalOptions);
    },

    getRelativeTime(dateString) {
      const now = new Date();
      const date = new Date(dateString);
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMins / 60);
      const diffDays = Math.floor(diffHours / 24);

      if (diffMins < 1) return 'just now';
      if (diffMins < 60) return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
      if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
      if (diffDays < 7) return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
      
      return this.formatDate(dateString);
    },

    getCurrentISOString() {
      return new Date().toISOString();
    }
  },

  // =============================================================================
  // STRING UTILITIES
  // =============================================================================
  
  string: {
    escapeHtml(text) {
      const div = document.createElement('div');
      div.textContent = text;
      return div.innerHTML;
    },

    truncate(text, maxLength = 100) {
      if (text.length <= maxLength) return text;
      return text.substring(0, maxLength - 3) + '...';
    },

    capitalize(text) {
      return text.charAt(0).toUpperCase() + text.slice(1);
    },

    camelToTitle(camelCase) {
      return camelCase
        .replace(/([A-Z])/g, ' $1')
        .replace(/^./, str => str.toUpperCase());
    },

    slugify(text) {
      return text
        .toLowerCase()
        .replace(/[^a-z0-9 -]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-');
    }
  },

  // =============================================================================
  // VALIDATION UTILITIES
  // =============================================================================
  
  validation: {
    isValidEmail(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    },

    isValidURL(url) {
      try {
        new URL(url);
        return true;
      } catch {
        return false;
      }
    },

    isValidUUID(uuid) {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      return uuidRegex.test(uuid);
    },

    sanitizeInput(input, maxLength = 500) {
      if (typeof input !== 'string') return '';
      return input.trim().substring(0, maxLength);
    }
  },

  // =============================================================================
  // DOM UTILITIES
  // =============================================================================
  
  dom: {
    createElement(tag, attributes = {}, children = []) {
      const element = document.createElement(tag);
      
      Object.entries(attributes).forEach(([key, value]) => {
        if (key === 'className') {
          element.className = value;
        } else if (key === 'textContent') {
          element.textContent = value;
        } else {
          element.setAttribute(key, value);
        }
      });

      children.forEach(child => {
        if (typeof child === 'string') {
          element.appendChild(document.createTextNode(child));
        } else if (child instanceof Node) {
          element.appendChild(child);
        }
      });

      return element;
    },

    addEvent(element, event, handler) {
      if (element && typeof handler === 'function') {
        element.addEventListener(event, handler);
      }
    },

    removeEvent(element, event, handler) {
      if (element && typeof handler === 'function') {
        element.removeEventListener(event, handler);
      }
    },

    toggleClass(element, className, force) {
      if (element && element.classList) {
        return element.classList.toggle(className, force);
      }
      return false;
    },

    scrollToElement(element, options = {}) {
      if (element) {
        const defaultOptions = {
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        };
        element.scrollIntoView({ ...defaultOptions, ...options });
      }
    }
  },

  // =============================================================================
  // ASYNC UTILITIES
  // =============================================================================
  
  async: {
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    },

    debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },

    throttle(func, limit) {
      let inThrottle;
      return function(...args) {
        if (!inThrottle) {
          func.apply(this, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      };
    },

    timeout(promise, ms) {
      return Promise.race([
        promise,
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Operation timed out')), ms)
        )
      ]);
    }
  },

  // =============================================================================
  // ANALYTICS/LOGGING UTILITIES
  // =============================================================================
  
  analytics: {
    async track(event, properties = {}, sessionId = null) {
      if (!ArionConfig.analytics.enabled) return;
      
      const data = {
        event,
        properties: {
          ...properties,
          timestamp: ArionUtils.datetime.getCurrentISOString(),
          userAgent: navigator.userAgent,
          referrer: document.referrer,
          url: window.location.href
        }
      };

      try {
        await ArionUtils.api.call(ArionConfig.api.actions.logEvent, data, sessionId);
      } catch (error) {
        console.warn('Analytics tracking failed:', error);
      }
    },

    trackPageView(page = null, sessionId = null) {
      this.track('page_view', {
        page: page || window.location.pathname,
        title: document.title
      }, sessionId);
    },

    trackUserInteraction(element, action, sessionId = null) {
      this.track('user_interaction', {
        element: element.tagName?.toLowerCase() || 'unknown',
        action,
        elementId: element.id || null,
        elementClass: element.className || null
      }, sessionId);
    }
  },

  // =============================================================================
  // DEVICE/BROWSER DETECTION
  // =============================================================================
  
  device: {
    isMobile() {
      return window.innerWidth <= 768;
    },

    isTablet() {
      return window.innerWidth > 768 && window.innerWidth <= 1024;
    },

    isDesktop() {
      return window.innerWidth > 1024;
    },

    hasTouch() {
      return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    },

    getViewport() {
      return {
        width: window.innerWidth,
        height: window.innerHeight
      };
    }
  },

  // =============================================================================
  // GOAL DETECTION AND ROUTING
  // =============================================================================
  
  goals: {
    detectGoal(text) {
      for (const [goal, pattern] of Object.entries(ArionConfig.goals.detectionPatterns)) {
        if (pattern.test(text)) {
          return goal;
        }
      }
      return null;
    },

    getGoalInfo(goalType) {
      return ArionConfig.goals.types[goalType] || null;
    }
  },

  // =============================================================================
  // DOCUMENT PREVIEW FUNCTIONALITY
  // =============================================================================
  
  documents: {
    async showDocumentPreview(sessionId, documentId) {
      try {
        const response = await ArionUtils.api.getDocument(sessionId, documentId);
        
        if (response.success) {
          this.createDocumentModal(response.document);
        } else {
          throw new Error(response.error || 'Failed to load document');
        }
      } catch (error) {
        console.error('Failed to show document preview:', error);
        alert('Failed to load document. Please try again.');
      }
    },

    createDocumentModal(documentData) {
      const existingModal = document.querySelector('#document-preview-modal');
      if (existingModal) {
        existingModal.remove();
      }

      const modal = ArionUtils.dom.createElement('div', {
        id: 'document-preview-modal',
        className: 'document-modal-overlay'
      });

      modal.innerHTML = `
        <div class="document-modal">
          <div class="document-modal-header">
            <div class="document-title">
              <span class="document-icon">${documentData.icon}</span>
              <h3>${documentData.title}</h3>
            </div>
            <button class="document-close" aria-label="Close">&times;</button>
          </div>
          <div class="document-modal-content">
            <div class="document-preview">
              ${this.formatDocumentContent(documentData.content)}
            </div>
          </div>
          <div class="document-modal-footer">
            <button class="btn-secondary" onclick="this.closest('.document-modal-overlay').remove()">Close</button>
            <button class="btn-primary" onclick="ArionUtils.documents.requestConsultation('${documentData.id}')">Schedule Consultation</button>
          </div>
        </div>
      `;

      const style = document.createElement('style');
      style.textContent = `
        .document-modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.7);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10002;
          padding: 1rem;
        }
        
        .document-modal {
          background: white;
          border-radius: 12px;
          max-width: 800px;
          max-height: 90vh;
          width: 100%;
          display: flex;
          flex-direction: column;
          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }
        
        .document-modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1.5rem;
          border-bottom: 1px solid #e2e8f0;
        }
        
        .document-title {
          display: flex;
          align-items: center;
          gap: 0.75rem;
        }
        
        .document-icon {
          font-size: 1.5rem;
        }
        
        .document-title h3 {
          margin: 0;
          color: #334155;
          font-size: 1.25rem;
        }
        
        .document-close {
          background: none;
          border: none;
          font-size: 1.5rem;
          color: #64748b;
          cursor: pointer;
          padding: 0;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: background 0.2s;
        }
        
        .document-close:hover {
          background: #f1f5f9;
        }
        
        .document-modal-content {
          flex: 1;
          overflow-y: auto;
          padding: 1.5rem;
        }
        
        .document-preview {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #334155;
        }
        
        .document-modal-footer {
          display: flex;
          justify-content: flex-end;
          gap: 1rem;
          padding: 1.5rem;
          border-top: 1px solid #e2e8f0;
        }
        
        .btn-primary, .btn-secondary {
          padding: 0.75rem 1.5rem;
          border-radius: 8px;
          font-weight: 600;
          cursor: pointer;
          border: none;
          transition: all 0.2s;
        }
        
        .btn-primary {
          background: #059669;
          color: white;
        }
        
        .btn-primary:hover {
          background: #047857;
        }
        
        .btn-secondary {
          background: #f1f5f9;
          color: #64748b;
        }
        
        .btn-secondary:hover {
          background: #e2e8f0;
        }
      `;

      document.head.appendChild(style);
      document.body.appendChild(modal);

      const closeBtn = modal.querySelector('.document-close');
      closeBtn.addEventListener('click', () => modal.remove());

      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          modal.remove();
        }
      });

      const handleEscape = (e) => {
        if (e.key === 'Escape') {
          modal.remove();
          document.removeEventListener('keydown', handleEscape);
        }
      };
      document.addEventListener('keydown', handleEscape);
    },

    formatDocumentContent(content) {
      let formatted = content;
      
      formatted = formatted.replace(/^### (.*$)/gim, '<h3>$1</h3>');
      formatted = formatted.replace(/^## (.*$)/gim, '<h2>$1</h2>');
      formatted = formatted.replace(/^# (.*$)/gim, '<h1>$1</h1>');
      
      formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
      formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');
      
      formatted = formatted.replace(/^\* (.*$)/gim, '<li>$1</li>');
      formatted = formatted.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>');
      
      formatted = formatted.replace(/\n\n/g, '</p><p>');
      formatted = '<p>' + formatted + '</p>';
      
      formatted = formatted.replace(/<p><\/p>/g, '');
      formatted = formatted.replace(/<p>(<h[1-6]>)/g, '$1');
      formatted = formatted.replace(/(<\/h[1-6]>)<\/p>/g, '$1');
      formatted = formatted.replace(/<p>(<ul>)/g, '$1');
      formatted = formatted.replace(/(<\/ul>)<\/p>/g, '$1');
      
      return formatted;
    },

    async requestConsultation(documentId) {
      alert('Consultation request functionality - would integrate with your CRM/calendar system');
    }
  },

  // =============================================================================
  // ERROR HANDLING
  // =============================================================================
  
  error: {
    handle(error, context = '') {
      console.error(`Error in ${context}:`, error);
      
      ArionUtils.analytics.track('error', {
        message: error.message,
        stack: error.stack,
        context
      });

      return 'An error occurred. Please try again.';
    },

    createErrorElement(message) {
      return ArionUtils.dom.createElement('div', {
        className: 'error-message',
        textContent: message
      });
    }
  }
};

// Make utilities globally available
window.ArionUtils = ArionUtils;

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ArionUtils;
}

console.log('✅ ArionUtils core utilities loaded successfully');
