import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';

class SessionService {
  static const _key = 'arion-demo-session';

  Future<String?> getSessionId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_key);
  }

  Future<void> saveSessionId(String id) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_key, id);
  }

  Future<void> clear() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_key);
  }

  String generateUuid() {
    final random = Random();
    String fourHex() => random.nextInt(0x10000).toRadixString(16).padLeft(4, '0');
    return '${fourHex()}${fourHex()}-${fourHex()}-4${fourHex().substring(1)}-${(8 + random.nextInt(4)).toRadixString(16)}${fourHex().substring(1)}-${fourHex()}${fourHex()}${fourHex()}';
  }
}


