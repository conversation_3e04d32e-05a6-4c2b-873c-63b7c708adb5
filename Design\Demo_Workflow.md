# ArionComply Demo - Workflow Documentation

**Version:** v1.0  
**Date:** 2025-07-14  
**Purpose:** Define complete data flows, API interactions, and system workflows for the ArionComply demo application

---

## 🎯 System Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │  Edge Function  │    │   Database      │
│   (HTML/JS)     │◄──►│     Router      │◄──►│   (PostgreSQL)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐               │
         └─────────────►│  Vector Store   │◄──────────────┘
                        │   (Supabase)    │
                        └─────────────────┘

API Call Flow:
Frontend → Single Edge Function Router → Specific Handler → Database
All CORS handled at router level - no direct database access from frontend
```

---

## 🔄 Complete User Journey Workflow

### Phase 1: Session Initialization

**Trigger:** User opens assistant widget  
**Frontend Action:** Call single API endpoint with action type

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant R as Router Function
    participant H as Handler Function
    participant DB as Database

    U->>F: Clicks assistant launcher
    F->>F: Generate sessionId (UUID)
    F->>R: POST /api {action: "init-session", sessionId, metadata}
    R->>R: Handle CORS, validate request
    R->>H: Route to init-session handler
    H->>DB: INSERT demo_sessions
    H->>R: {success: true, sessionId}
    R->>F: Response with CORS headers
    F->>F: Store sessionId, show welcome
```

**Database Changes:**
```sql
INSERT INTO demo_sessions (id, user_agent, referrer, ip_address_hash)
VALUES (uuid, user_agent, referrer, hash_ip_address(client_ip));
```

---

### Phase 2: Conversation & Goal Detection

**Trigger:** User sends message  
**Frontend Action:** Call router with query-agent action

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant R as Router Function
    participant H as Handler Function
    participant LLM as LLM Service
    participant VDB as Vector DB
    participant DB as Database

    U->>F: Types and sends message
    F->>R: POST /api {action: "query-agent", sessionId, message}
    R->>R: Validate session, handle CORS
    R->>H: Route to query-agent handler
    H->>DB: INSERT demo_interactions (user message)
    H->>VDB: Vector search for context
    H->>LLM: Generate response with context
    H->>H: Detect goal/intent
    H->>DB: UPDATE demo_sessions (goal_detected)
    H->>DB: INSERT demo_interactions (assistant response)
    H->>R: {reply, quickReplies, goalDetected}
    R->>F: Response with CORS headers
    F->>F: Display assistant message
```

**Database Changes:**
```sql
-- Store user message
INSERT INTO demo_interactions (session_id, message_type, content, intent_detected)
VALUES (session_id, 'user', user_message, detected_intent);

-- Update session with detected goal
UPDATE demo_sessions 
SET goal_detected = goal, goal_confidence = confidence, updated_at = NOW()
WHERE id = session_id;

-- Store assistant response
INSERT INTO demo_interactions (session_id, message_type, content, quick_replies, action_triggered)
VALUES (session_id, 'assistant', response, quick_replies_array, action_type);
```

---

### Phase 3: Intake Data Collection

**Trigger:** Goal detected, assistant asks intake questions  
**Process:** Conversational data gathering

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant API as Edge Function
    participant DB as Database

    loop Intake Questions
        API->>F: Ask intake question
        F->>U: Display question + quick replies
        U->>F: Provides answer
        F->>API: POST /query-agent {answer}
        API->>DB: UPSERT demo_intake_data
        API->>DB: UPDATE completion_percentage
    end
    
    API->>API: Check if intake complete
    alt Intake Complete
        API->>F: {actionRequired: 'trigger_document_generation'}
        F->>API: POST /generate-documents
    end
```

**Database Changes:**
```sql
-- Create or update intake record
INSERT INTO demo_intake_data (session_id, goal_type, company_size, industry, ...)
VALUES (session_id, goal, size, industry, ...)
ON CONFLICT (session_id) DO UPDATE SET
  company_size = EXCLUDED.company_size,
  completion_percentage = calculate_completion_percentage(...);

-- Mark intake as completed when threshold reached
UPDATE demo_sessions 
SET intake_completed = TRUE, updated_at = NOW()
WHERE id = session_id AND completion_percentage >= 0.8;
```

---

### Phase 4: Document Generation

**Trigger:** Intake completion or explicit request  
**Process:** Generate personalized compliance documents

```mermaid
sequenceDiagram
    participant F as Frontend
    participant API as Edge Function
    participant LLM as LLM Service
    participant DB as Database

    F->>API: POST /generate-documents {sessionId}
    API->>DB: SELECT intake_data for context
    API->>API: Determine document types needed
    
    loop For Each Document Type
        API->>LLM: Generate document content
        API->>DB: INSERT demo_documents
    end
    
    API->>DB: UPDATE demo_sessions (documents_generated, preview_ready)
    API->>F: {success: true, documentsGenerated}
    F->>F: Show "documents ready" message
```

**Database Changes:**
```sql
-- Generate documents based on goal type
INSERT INTO demo_documents (session_id, document_type, title, content, sections, metadata, generation_method)
VALUES 
  (session_id, 'risk_register', 'Risk Register', content, sections, metadata, 'ai'),
  (session_id, 'soa', 'Statement of Applicability', content, sections, metadata, 'ai'),
  ...;

-- Mark session as ready for preview
UPDATE demo_sessions 
SET documents_generated = TRUE, preview_ready = TRUE, updated_at = NOW()
WHERE id = session_id;
```

---

### Phase 5: Document Preview

**Trigger:** User navigates to `/guide/results`  
**Process:** Display generated documents

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant API as Edge Function
    participant DB as Database

    U->>F: Navigate to /guide/results
    F->>API: GET /get-documents?sessionId
    API->>DB: SELECT demo_documents WHERE session_id
    API->>F: {documents: [...]}
    F->>F: Render document grid
    
    U->>F: Click document card
    F->>API: GET /get-document/{docId}
    API->>DB: SELECT document content
    API->>DB: INSERT demo_browsing_logs
    API->>DB: UPDATE demo_documents (preview_count++)
    API->>F: {document: {...}}
    F->>F: Render document viewer
```

**Database Changes:**
```sql
-- Log document view
INSERT INTO demo_browsing_logs (session_id, event_type, document_id, created_at)
VALUES (session_id, 'document_view', document_id, NOW());

-- Update view count
UPDATE demo_documents 
SET preview_count = preview_count + 1, last_viewed_at = NOW()
WHERE id = document_id;
```

---

### Phase 6: Lead Capture (Optional)

**Trigger:** User views 2+ documents or shows interest  
**Process:** Collect contact information

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant API as Edge Function
    participant DB as Database
    participant CRM as CRM System

    F->>F: Check if lead capture threshold met
    F->>U: Show lead capture modal
    U->>F: Fills contact form
    F->>API: POST /save-contact {contactData}
    API->>DB: INSERT demo_contacts
    API->>DB: UPDATE demo_sessions (lead_captured)
    API->>CRM: Sync lead data (optional)
    API->>F: {success: true}
    F->>F: Show thank you message
```

**Database Changes:**
```sql
-- Save contact information
INSERT INTO demo_contacts (
  session_id, name, email, company, job_title, interest_area, message,
  documents_viewed, engagement_score, follow_up_consented, marketing_consented
)
VALUES (session_id, name, email, company, title, interest, message, docs_array, score, true, consent);

-- Mark session as lead captured
UPDATE demo_sessions 
SET lead_captured = TRUE, updated_at = NOW()
WHERE id = session_id;
```

---

## 🔄 Background Processes

### Auto-Refresh for Document Status

**Frontend Process:** Poll for document generation completion

```javascript
// Frontend auto-refresh logic
setInterval(async () => {
  if (waitingForDocuments) {
    const response = await fetch(`/get-documents?sessionId=${sessionId}`);
    const data = await response.json();
    if (data.documents.length > 0) {
      showDocumentsReady();
      waitingForDocuments = false;
    }
  }
}, 5000); // Check every 5 seconds
```

### Session Cleanup

**Database Process:** Automated cleanup of old sessions

```sql
-- Daily cleanup job
DELETE FROM demo_sessions 
WHERE created_at < NOW() - INTERVAL '30 days' 
  AND lead_captured = FALSE;

-- Archive old sessions with leads
UPDATE demo_sessions 
SET session_tags = array_append(session_tags, 'archived')
WHERE created_at < NOW() - INTERVAL '2 years';
```

---

## 📊 Analytics Data Flow

### Real-time Metrics Collection

**Process:** Track user behavior for optimization

```sql
-- Aggregate session metrics
CREATE MATERIALIZED VIEW session_analytics AS
SELECT 
  DATE(created_at) as date,
  goal_detected,
  COUNT(*) as total_sessions,
  COUNT(*) FILTER (WHERE intake_completed) as completed_intake,
  COUNT(*) FILTER (WHERE documents_generated) as generated_docs,
  COUNT(*) FILTER (WHERE lead_captured) as captured_leads,
  AVG(EXTRACT(EPOCH FROM (updated_at - created_at))) as avg_session_duration
FROM demo_sessions
GROUP BY DATE(created_at), goal_detected;

-- Refresh daily
REFRESH MATERIALIZED VIEW session_analytics;
```

---

## 🔧 API Endpoint Architecture

### Single Router Endpoint

**All frontend requests go through one endpoint:**

| Endpoint | Method | Purpose | Action Parameter |
|----------|--------|---------|-----------------|
| `/api` | POST | All API operations | `action` field determines routing |

**Action Types:**
- `init-session` → Create new session
- `query-agent` → Chat interaction  
- `generate-documents` → Create documents
- `get-documents` → List documents
- `get-document` → View document (requires `documentId`)
- `save-contact` → Capture lead
- `log-interaction` → Track events

### Frontend API Usage

**JavaScript Implementation:**
```javascript
// Single API endpoint for all operations
const API_BASE = 'https://your-project.supabase.co/functions/v1/api';

async function callAPI(action, data = {}) {
    const response = await fetch(API_BASE, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Session-ID': sessionId
        },
        body: JSON.stringify({
            action,
            sessionId,
            ...data
        })
    });
    
    if (!response.ok) {
        throw new Error(`API call failed: ${response.statusText}`);
    }
    
    return await response.json();
}

// Usage examples:
await callAPI('init-session', { userAgent: navigator.userAgent });
await callAPI('query-agent', { message: 'Hello' });
await callAPI('generate-documents');
await callAPI('get-documents');
await callAPI('save-contact', { first_name: 'John', email: '<EMAIL>' });
```

### Router Implementation Benefits

**CORS Control:**
- All CORS headers handled in one place
- No cross-origin issues between different Edge Functions
- Consistent security policies

**Security Benefits:**
- Single point for rate limiting
- Unified authentication/validation
- Request logging and monitoring
- Input sanitization

**Simplified Frontend:**
- Single API endpoint to manage
- Consistent error handling
- Easier debugging and monitoring

---

## 🚨 Error Handling Workflows

### Document Generation Failure

```mermaid
sequenceDiagram
    participant API as Edge Function
    participant DB as Database
    participant F as Frontend

    API->>API: Document generation fails
    API->>DB: INSERT error log
    API->>F: {success: false, error: "generation_failed"}
    F->>F: Show retry option
    F->>F: Offer manual contact form
```

### Session Recovery

```sql
-- Handle orphaned sessions
UPDATE demo_sessions 
SET session_tags = array_append(session_tags, 'recovered')
WHERE updated_at < NOW() - INTERVAL '1 hour' 
  AND documents_generated = FALSE
  AND intake_completed = TRUE;
```

---

## 🎯 Performance Optimization

### Database Indexing Strategy
- Session-based queries (most common)
- Time-range analytics queries
- Document type filtering
- Lead management queries

### Caching Strategy
- Session data cached in frontend for duration of session
- Document content cached after first load
- Vector search results cached per query

### Monitoring Points
- API response times per endpoint
- Document generation success rates
- Session completion funnels
- Database query performance