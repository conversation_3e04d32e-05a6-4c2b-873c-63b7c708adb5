import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'core/di/locator.dart';
import 'presentation/theme/app_theme.dart';
import 'presentation/routes/app_router.dart';
import 'features/auth/presentation/blocs/auth/auth_cubit.dart';
import 'features/auth/presentation/blocs/demo_access/demo_access_cubit.dart';
import 'domain/repositories/auth_repository.dart' as domain_auth;
import 'domain/repositories/demo_access_repository.dart' as domain_demo_access;

class ArionApp extends StatefulWidget {
  const ArionApp({super.key});

  @override
  State<ArionApp> createState() => _ArionAppState();
}

class _ArionAppState extends State<ArionApp> {
  @override
  void initState() {
    super.initState();
    setupLocator();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AuthCubit>(
          create: (_) => AuthCubit(locator<domain_auth.AuthRepository>()),
        ),
        BlocProvider<DemoAccessCubit>(
          create: (_) => DemoAccessCubit(
            locator<domain_demo_access.DemoAccessRepository>(),
          ),
        ),
      ],
      child: MaterialApp.router(
        title: 'ArionComply',
        theme: AppTheme.lightTheme,
        debugShowCheckedModeBanner: false,
        routerConfig: AppRouter.router,
      ),
    );
  }
}
