<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Registration Complete - Arionetworks</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
      line-height: 1.6;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .container {
      max-width: 700px;
      width: 100%;
      background: white;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, var(--primary-color, #667eea) 0%, var(--secondary-color, #764ba2) 100%);
      color: white;
      padding: 40px 30px;
      text-align: center;
    }

    .logo {
      width: 80px;
      height: 80px;
      background: rgba(255,255,255,0.2);
      border-radius: 50%;
      margin: 0 auto 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36px;
    }

    .header h1 {
      font-size: 2.2em;
      margin-bottom: 10px;
      font-weight: 700;
    }

    .header p {
      font-size: 1.1em;
      opacity: 0.9;
    }

    .content {
      padding: 40px 30px;
    }

    .success-message {
      text-align: center;
      margin-bottom: 30px;
    }

    .success-icon {
      width: 80px;
      height: 80px;
      background: #d4edda;
      color: #155724;
      border-radius: 50%;
      margin: 0 auto 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 36px;
    }

    .success-title {
      font-size: 1.8em;
      color: #155724;
      margin-bottom: 10px;
      font-weight: 600;
    }

    .success-description {
      color: #666;
      font-size: 1.1em;
      line-height: 1.6;
    }

    .info-box {
      background: #f8f9fa;
      border-left: 4px solid var(--primary-color, #667eea);
      padding: 25px;
      margin: 30px 0;
      border-radius: 0 8px 8px 0;
    }

    .info-box h3 {
      color: #333;
      margin-bottom: 15px;
      font-size: 18px;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .info-box p {
      color: #666;
      margin-bottom: 10px;
      line-height: 1.6;
    }

    .info-box p:last-child {
      margin-bottom: 0;
    }

    .timeline {
      background: white;
      border: 1px solid #e1e5e9;
      border-radius: 8px;
      padding: 25px;
      margin: 30px 0;
    }

    .timeline h3 {
      color: #333;
      margin-bottom: 20px;
      font-size: 18px;
      text-align: center;
    }

    .timeline-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 20px;
      position: relative;
    }

    .timeline-item::before {
      content: '';
      position: absolute;
      left: 15px;
      top: 35px;
      bottom: -20px;
      width: 2px;
      background: #e1e5e9;
    }

    .timeline-item:last-child::before {
      display: none;
    }

    .timeline-icon {
      width: 30px;
      height: 30px;
      background: var(--primary-color, #667eea);
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: bold;
      margin-right: 15px;
      flex-shrink: 0;
      position: relative;
      z-index: 1;
    }

    .timeline-content h4 {
      color: #333;
      margin-bottom: 5px;
      font-size: 16px;
    }

    .timeline-content p {
      color: #666;
      font-size: 14px;
      line-height: 1.5;
    }

    .action-buttons {
      text-align: center;
      margin-top: 30px;
    }

    .btn {
      display: inline-block;
      background: linear-gradient(135deg, var(--primary-color, #667eea) 0%, var(--secondary-color, #764ba2) 100%);
      color: white;
      padding: 15px 30px;
      text-decoration: none;
      border-radius: 8px;
      font-weight: 600;
      margin: 0 10px 10px 10px;
      transition: all 0.3s ease;
      font-size: 16px;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .btn.secondary {
      background: #6c757d;
    }

    .btn.secondary:hover {
      box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
    }

    .contact-info {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
      margin: 30px 0;
      text-align: center;
    }

    .contact-info h3 {
      color: #333;
      margin-bottom: 10px;
      font-size: 16px;
    }

    .contact-info p {
      color: #666;
      font-size: 14px;
      margin-bottom: 10px;
    }

    .contact-info a {
      color: var(--primary-color, #667eea);
      text-decoration: none;
    }

    .contact-info a:hover {
      text-decoration: underline;
    }

    .footer {
      background: #f8f9fa;
      padding: 20px 30px;
      text-align: center;
      font-size: 14px;
      color: #666;
      border-top: 1px solid #e1e5e9;
    }

    /* Product-specific color schemes */
    .arioncomply {
      --primary-color: #2563eb;
      --secondary-color: #1d4ed8;
    }

    .arionsecure {
      --primary-color: #dc2626;
      --secondary-color: #b91c1c;
    }

    .arionanalytics {
      --primary-color: #059669;
      --secondary-color: #047857;
    }

    .arionplatform {
      --primary-color: #7c3aed;
      --secondary-color: #6d28d9;
    }

    @media (max-width: 768px) {
      body {
        padding: 10px;
      }

      .container {
        margin: 10px 0;
      }

      .header {
        padding: 30px 20px;
      }

      .header h1 {
        font-size: 1.8em;
      }

      .content {
        padding: 30px 20px;
      }

      .btn {
        display: block;
        margin: 10px 0;
        width: 100%;
      }

      .success-title {
        font-size: 1.5em;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo" id="product-logo">✅</div>
      <h1 id="page-title">Registration Complete!</h1>
      <p id="page-subtitle">Thank you for joining our program</p>
    </div>

    <div class="content">
      <div class="success-message">
        <div class="success-icon">🎉</div>
        <h2 class="success-title" id="success-title">Welcome to Arionetworks!</h2>
        <p class="success-description" id="success-description">
          Your registration has been completed successfully. We've sent you a confirmation email with all the details.
        </p>
      </div>

      <div class="info-box">
        <h3>📧 Email Verification Complete</h3>
        <p>Your email address has been verified and your registration is now active in our system.</p>
        <p><strong>Registration Details:</strong></p>
        <p id="registration-details">
          • Product: <span id="product-name">Arionetworks</span><br>
          • Program: <span id="program-name">Program</span><br>
          • Status: <strong>Verified & Active</strong>
        </p>
      </div>

      <div class="timeline">
        <h3>🚀 What Happens Next?</h3>

        <div class="timeline-item">
          <div class="timeline-icon">1</div>
          <div class="timeline-content">
            <h4>Application Review</h4>
            <p>Our team will review your application and requirements within 2-3 business days.</p>
          </div>
        </div>

        <div class="timeline-item">
          <div class="timeline-icon">2</div>
          <div class="timeline-content">
            <h4>Initial Contact</h4>
            <p>We'll reach out to discuss your specific needs and answer any questions about the program.</p>
          </div>
        </div>

        <div class="timeline-item">
          <div class="timeline-icon">3</div>
          <div class="timeline-content">
            <h4>Program Access</h4>
            <p>Upon approval, you'll receive access instructions, onboarding materials, and dedicated support contact information.</p>
          </div>
        </div>
      </div>

      <div class="action-buttons">
        <a href="#" id="back-to-product" class="btn">Back to Product Page</a>
        <a href="mailto:<EMAIL>" class="btn secondary">Contact Support</a>
      </div>

      <div class="contact-info">
        <h3>🤝 Stay Connected</h3>
        <p>Follow our progress and get updates about your program:</p>
        <p>
          Email: <a href="mailto:<EMAIL>"><EMAIL></a> |
          Website: <a href="https://arionetworks.com" target="_blank">arionetworks.com</a>
        </p>
      </div>
    </div>

    <div class="footer">
      <p>
        <strong>Arionetworks</strong> - Secure Business Solutions<br>
        Thank you for choosing us for your business technology needs.
      </p>
    </div>
  </div>

  <script>
    // Configuration
    const CONFIG = {
      PRODUCT_CONFIGS: {
        'arioncomply': {
          name: 'ArionComply',
          logo: '🛡️',
          domain: 'https://iso.arionetworks.com',
          description: 'ISO Compliance Management'
        },
        'arionsecure': {
          name: 'ArionSecure',
          logo: '🔐',
          domain: 'https://security.arionetworks.com',
          description: 'Advanced Security Platform'
        },
        'arionanalytics': {
          name: 'ArionAnalytics',
          logo: '📊',
          domain: 'https://analytics.arionetworks.com',
          description: 'Business Intelligence Suite'
        },
        'arionplatform': {
          name: 'ArionPlatform',
          logo: '🚀',
          domain: 'https://platform.arionetworks.com',
          description: 'Integrated Business Platform'
        },
        'default': {
          name: 'Arionetworks',
          logo: '🌐',
          domain: 'https://arionetworks.com',
          description: 'Business Technology Solutions'
        }
      }
    };

    // Get URL parameters
    function getQueryParam(param) {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get(param);
    }

    // Update UI based on product and program
    function updateUI() {
      const productId = getQueryParam('product') || 'default';
      const programType = getQueryParam('program') || 'pilot';

      const config = CONFIG.PRODUCT_CONFIGS[productId] || CONFIG.PRODUCT_CONFIGS['default'];

      // Apply product colors
      document.body.className = productId;

      // Update logo and product info
      document.getElementById('product-logo').textContent = config.logo;
      document.getElementById('product-name').textContent = config.name;

      // Update program name
      const programNames = {
        'pilot': 'Pilot Program',
        'waitlist': 'Waitlist',
        'beta': 'Beta Program',
        'early_access': 'Early Access Program'
      };
      document.getElementById('program-name').textContent = programNames[programType] || 'Program';

      // Update page title and descriptions
      document.getElementById('page-title').textContent = `${config.name} Registration Complete!`;
      document.getElementById('success-title').textContent = `Welcome to ${config.name}!`;
      document.getElementById('success-description').textContent =
        `Your ${programNames[programType] || 'program'} registration has been completed successfully. We've sent you a confirmation email with all the details.`;

      // Update back button
      const backBtn = document.getElementById('back-to-product');
      backBtn.href = config.domain;
      backBtn.textContent = `Back to ${config.name}`;

      // Update page title
      document.title = `Registration Complete - ${config.name}`;

      // Update registration details
      document.getElementById('registration-details').innerHTML = `
        • Product: <strong>${config.name}</strong><br>
        • Program: <strong>${programNames[programType] || 'Program'}</strong><br>
        • Status: <strong>Verified & Active</strong>
      `;
    }

    // Initialize page
    document.addEventListener('DOMContentLoaded', () => {
      updateUI();

      // Track page view
      if (window.gtag) {
        const productId = getQueryParam('product') || 'default';
        const programType = getQueryParam('program') || 'pilot';

        window.gtag('event', 'registration_complete', {
          event_category: 'conversion',
          event_label: `${productId}_${programType}`,
          product_id: productId,
          program_type: programType
        });
      }

      // Add some celebration animation
      setTimeout(() => {
        const logo = document.getElementById('product-logo');
        logo.style.animation = 'bounce 0.6s ease-in-out';
      }, 500);
    });

    // Add bounce animation
    const style = document.createElement('style');
    style.textContent = `
      @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
      }
    `;
    document.head.appendChild(style);
  </script>
</body>
</html>
