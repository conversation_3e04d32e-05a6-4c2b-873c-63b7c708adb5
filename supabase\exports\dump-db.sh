#!/bin/bash
set -euo pipefail

HOST="db.dxncozbhwppvwpugoqjk.supabase.co"
PORT="6543"
DB="postgres"
USER="postgres"
export PGPASSWORD='Ariondbfriend@2025'

CONN="host=$HOST port=$PORT dbname=$DB user=$USER sslmode=require"
OUTDIR="./db_export_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$OUTDIR"

command -v psql >/dev/null || { echo "psql not found"; exit 1; }
command -v pg_dump >/dev/null || { echo "pg_dump not found"; exit 1; }

psql "$CONN" -c 'SELECT current_database(), current_user, version();' > "$OUTDIR/connection_info.txt"

pg_dump --schema-only --no-owner --no-privileges -h "$HOST" -p "$PORT" -U "$USER" -d "$DB" > "$OUTDIR/schema.sql"
gzip -c "$OUTDIR/schema.sql" > "$OUTDIR/schema.sql.gz"

pg_dumpall --globals-only -h "$HOST" -p "$PORT" -U "$USER" > "$OUTDIR/globals.sql"

{
  echo "Database inspection report"
  echo "Generated: $(date)"
  echo "=================================="

  echo -e "\n[Extensions]\n"
  psql "$CONN" -c '\dx'

  echo -e "\n[Table sizes & row estimates]\n"
  psql "$CONN" -c "
    SELECT n.nspname AS schema, c.relname AS table,
           pg_total_relation_size(c.oid) AS bytes,
           pg_size_pretty(pg_total_relation_size(c.oid)) AS size,
           c.reltuples::bigint AS est_rows
    FROM pg_class c
    JOIN pg_namespace n ON n.oid=c.relnamespace
    WHERE c.relkind='r' AND n.nspname IN ('public','auth','storage')
    ORDER BY bytes DESC;"

  echo -e "\n[RLS flags]\n"
  psql "$CONN" -c "
    SELECT n.nspname AS schema,
           c.relname  AS table,
           c.relrowsecurity       AS rowsecurity,
           c.relforcerowsecurity  AS force_rowsecurity
    FROM pg_class c
    JOIN pg_namespace n ON n.oid = c.relnamespace
    WHERE c.relkind = 'r'
      AND n.nspname IN ('public','auth','storage')
    ORDER BY 1,2;"

  echo -e "\n[RLS policies]\n"
  psql "$CONN" -c "
    SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
    FROM pg_policies
    ORDER BY schemaname, tablename, policyname;"

  echo -e "\n[Triggers]\n"
  psql "$CONN" -c "
    SELECT event_object_schema, event_object_table, trigger_name, action_timing, event_manipulation
    FROM information_schema.triggers
    ORDER BY 1,2,3;"

  echo -e "\n[Indexes]\n"
  psql "$CONN" -c "
    SELECT schemaname, tablename, indexname, indexdef
    FROM pg_indexes
    WHERE schemaname IN ('public','auth','storage')
    ORDER BY schemaname, tablename, indexname;"

  echo -e "\n[Foreign Keys]\n"
  psql "$CONN" -c "
    SELECT tc.table_schema, tc.table_name, kcu.column_name,
           ccu.table_schema AS fk_schema, ccu.table_name AS fk_table, ccu.column_name AS fk_column
    FROM information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kcu USING (constraint_name,table_schema,table_name)
    JOIN information_schema.constraint_column_usage ccu USING (constraint_name,table_schema)
    WHERE tc.constraint_type='FOREIGN KEY'
      AND tc.table_schema IN ('public','auth','storage')
    ORDER BY tc.table_schema, tc.table_name;"
} > "$OUTDIR/inspect_report.txt"

psql "$CONN" -At -F',' -c "
  SELECT n.nspname,c.relname,pg_total_relation_size(c.oid),c.reltuples::bigint
  FROM pg_class c JOIN pg_namespace n ON n.oid=c.relnamespace
  WHERE c.relkind='r' AND n.nspname IN ('public','auth','storage')
  ORDER BY 3 DESC;" > "$OUTDIR/table_sizes.csv"

echo "Done. Outputs in: $OUTDIR"

