<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ArionComply Demo - Enhanced Chat</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f8fafc;
      height: 100vh;
      display: flex;
      flex-direction: column;
    }

    .header {
      background: white;
      padding: 1rem 2rem;
      border-bottom: 1px solid #e2e8f0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .user-greeting {
      flex: 1;
      font-size: 0.9rem;
      color: #059669;
      font-weight: 600;
    }

    .logo {
      flex: 1;
      text-align: center;
      font-size: 1.5rem;
      font-weight: bold;
      color: #059669;
    }

    .demo-info {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 1rem;
      font-size: 0.8rem;
      color: #64748b;
    }

    .demo-badge {
      background: #fef3c7;
      color: #92400e;
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: 600;
    }

    .questions-counter {
      background: #eff6ff;
      color: #1e40af;
      padding: 0.375rem 0.875rem;
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: 600;
      transition: all 0.3s ease;
      white-space: nowrap;
      min-width: 200px;
    }

    .questions-counter.warning {
      background: #fef3c7;
      color: #92400e;
      animation: pulse 2s infinite;
    }

    .questions-counter.danger {
      background: #fee2e2;
      color: #dc2626;
      animation: pulse 1.5s infinite;
    }

    @keyframes pulse {

      0%,
      100% {
        opacity: 1;
      }

      50% {
        opacity: 0.7;
      }
    }

    .chat-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      max-width: 1200px;
      margin: 1rem auto;
      width: 95%;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      position: relative;
    }

    .chat-header {
      background: linear-gradient(135deg, #059669, #047857);
      color: white;
      padding: 1.5rem;
      text-align: center;
      position: relative;
    }

    .demo-info-toggle {
      position: absolute;
      top: 1rem;
      right: 1rem;
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      padding: 0.5rem 0.75rem;
      border-radius: 20px;
      font-size: 0.8rem;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      gap: 0.25rem;
    }

    .demo-info-toggle:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }

    .demo-info-panel {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      padding: 1.5rem;
      margin-top: 1.5rem;
      text-align: left;
      font-size: 0.9rem;
      line-height: 1.5;
      display: none;
      backdrop-filter: blur(10px);
    }

    .demo-info-panel.show {
      display: block;
      animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
      from {
        opacity: 0;
        transform: translateY(-10px);
      }

      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .info-section {
      margin-bottom: 1.5rem;
    }

    .info-section:last-child {
      margin-bottom: 0;
    }

    .info-section h4 {
      color: white;
      font-size: 1rem;
      margin-bottom: 0.75rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .info-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .info-list li {
      padding: 0.25rem 0;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .info-highlight {
      background: rgba(255, 255, 255, 0.15);
      padding: 0.75rem 1rem;
      border-radius: 8px;
      border-left: 3px solid rgba(255, 255, 255, 0.5);
      margin: 0.75rem 0;
    }

    /* Welcome Banner Styles */
    .welcome-banner {
      background: linear-gradient(135deg, #ecfdf5, #d1fae5);
      border: 1px solid #a7f3d0;
      color: #065f46;
      padding: 1rem 1.5rem;
      margin: 0 1.5rem 1rem 1.5rem;
      border-radius: 8px;
      font-size: 0.9rem;
      text-align: center;
      position: relative;
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }

    .welcome-banner.hidden {
      display: none;
    }

    .welcome-icon {
      font-size: 1.5rem;
      flex-shrink: 0;
    }

    .welcome-content {
      flex: 1;
      text-align: left;
    }

    .welcome-title {
      font-weight: 600;
      margin-bottom: 0.25rem;
    }

    .welcome-text {
      line-height: 1.4;
    }

    .banner-close {
      position: absolute;
      top: 0.75rem;
      right: 0.75rem;
      background: none;
      border: none;
      color: #065f46;
      cursor: pointer;
      font-size: 1.2rem;
      line-height: 1;
      padding: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.2s;
    }

    .banner-close:hover {
      background: rgba(6, 95, 70, 0.1);
      color: #047857;
    }

    .demo-limits-warning {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 8px;
      padding: 1rem;
      margin-top: 1rem;
      font-size: 0.85rem;
      text-align: left;
      display: none;
    }

    .demo-limits-warning.show {
      display: block;
    }

    .demo-limit-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.95);
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      backdrop-filter: blur(5px);
    }

    .demo-limit-overlay.show {
      display: flex;
    }

    .limit-reached-card {
      background: white;
      padding: 3rem;
      border-radius: 16px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      text-align: center;
      max-width: 500px;
      margin: 2rem;
      border: 2px solid #059669;
    }

    .limit-icon {
      font-size: 4rem;
      margin-bottom: 1.5rem;
    }

    .limit-title {
      color: #059669;
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }

    .limit-message {
      color: #64748b;
      line-height: 1.6;
      margin-bottom: 2rem;
    }

    .upgrade-btn {
      background: #059669;
      color: white;
      padding: 1rem 2rem;
      border: none;
      border-radius: 8px;
      font-size: 1.1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s;
      margin: 0.5rem;
    }

    .upgrade-btn:hover {
      background: #047857;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
    }

    .continue-btn {
      background: transparent;
      color: #64748b;
      padding: 0.75rem 1.5rem;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 0.9rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      margin: 0.5rem;
    }

    .continue-btn:hover {
      background: #f9fafb;
      color: #374151;
    }

    /* Pre-prompt suggestions section */
    .pre-prompt-suggestions {
      background: #f8fafc;
      border-bottom: 1px solid #e2e8f0;
      padding: 1.25rem 2rem;
      transition: all 0.3s ease;
    }

    .pre-prompt-suggestions.hidden {
      display: none;
    }

    .suggestions-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.75rem;
    }

    .suggestions-title {
      color: #059669;
      font-size: 0.9rem;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .hide-suggestions {
      background: none;
      border: none;
      color: #64748b;
      cursor: pointer;
      font-size: 0.8rem;
      padding: 0.25rem;
      border-radius: 4px;
      transition: color 0.2s;
    }

    .hide-suggestions:hover {
      color: #374151;
    }

    .suggestion-categories {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
    }

    .category-group {
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 0.75rem;
    }

    .category-title {
      font-size: 0.8rem;
      font-weight: 600;
      color: #374151;
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
      gap: 0.25rem;
    }

    .suggestion-chips {
      display: flex;
      flex-wrap: wrap;
      gap: 0.375rem;
    }

    .suggestion-chip {
      background: #f9fafb;
      border: 1px solid #d1d5db;
      color: #374151;
      padding: 0.375rem 0.75rem;
      border-radius: 16px;
      font-size: 0.75rem;
      cursor: pointer;
      transition: all 0.2s;
      line-height: 1.2;
    }

    .suggestion-chip:hover {
      background: #ecfdf5;
      border-color: #059669;
      color: #059669;
      transform: translateY(-1px);
    }

    .show-suggestions-btn {
      background: #f1f5f9;
      border: 1px solid #e2e8f0;
      color: #64748b;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      font-size: 0.8rem;
      cursor: pointer;
      transition: all 0.2s;
      margin-bottom: 1rem;
    }

    .show-suggestions-btn:hover {
      background: #e2e8f0;
      color: #374151;
    }

    /* =========================== */
    /* NEW: DOCUMENT VIEWING STYLES */
    /* =========================== */

    /* Document gallery view */
    .document-gallery {
      display: none;
      flex: 1;
      padding: 2rem;
      background: #f8fafc;
      overflow-y: auto;
    }

    .document-gallery.active {
      display: block;
    }

    .gallery-header {
      text-align: center;
      margin-bottom: 2rem;
    }

    .gallery-title {
      font-size: 1.8rem;
      color: #334155;
      margin-bottom: 0.5rem;
    }

    .gallery-subtitle {
      color: #64748b;
      font-size: 1rem;
    }

    .document-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
      max-width: 1000px;
      margin: 0 auto;
    }

    .document-card {
      background: white;
      border-radius: 12px;
      border: 2px solid #e2e8f0;
      padding: 1.5rem;
      cursor: pointer;
      transition: all 0.2s ease;
      position: relative;
      overflow: hidden;
    }

    .document-card:hover {
      border-color: #059669;
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(5, 150, 105, 0.15);
    }

    .document-card-header {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .document-icon {
      font-size: 2rem;
    }

    .document-info h3 {
      color: #334155;
      font-size: 1.1rem;
      margin-bottom: 0.25rem;
    }

    .document-type {
      color: #64748b;
      font-size: 0.85rem;
      text-transform: uppercase;
      font-weight: 600;
    }

    .document-preview {
      color: #475569;
      font-size: 0.9rem;
      line-height: 1.5;
      margin-bottom: 1rem;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .document-watermark {
      position: absolute;
      top: 0.75rem;
      right: 0.75rem;
      background: rgba(5, 150, 105, 0.1);
      color: #059669;
      padding: 0.25rem 0.75rem;
      border-radius: 12px;
      font-size: 0.7rem;
      font-weight: 600;
      text-transform: uppercase;
    }

    .document-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid #f1f5f9;
    }

    .document-date {
      color: #64748b;
      font-size: 0.8rem;
    }

    .view-btn {
      background: #059669;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      font-size: 0.85rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s;
    }

    .view-btn:hover {
      background: #047857;
    }

    /* Document viewer */
    .document-viewer {
      display: none;
      flex: 1;
      flex-direction: column;
      background: white;
    }

    .document-viewer.active {
      display: flex;
    }

    .viewer-header {
      background: #f8fafc;
      border-bottom: 1px solid #e2e8f0;
      padding: 1.5rem 2rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .viewer-title-area {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .back-btn {
      background: #f1f5f9;
      border: 1px solid #e2e8f0;
      color: #475569;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      font-size: 0.9rem;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .back-btn:hover {
      background: #e2e8f0;
      color: #334155;
    }

    .viewer-document-title {
      font-size: 1.5rem;
      color: #334155;
      font-weight: 600;
    }

    .viewer-actions {
      display: flex;
      gap: 1rem;
    }

    .action-btn {
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s;
      font-size: 0.9rem;
    }

    .action-btn.primary {
      background: #059669;
      color: white;
      border: none;
    }

    .action-btn.primary:hover {
      background: #047857;
      transform: translateY(-1px);
    }

    .action-btn.secondary {
      background: white;
      color: #475569;
      border: 1px solid #d1d5db;
    }

    .action-btn.secondary:hover {
      background: #f9fafb;
      border-color: #9ca3af;
    }

    .viewer-content {
      flex: 1;
      padding: 2rem;
      overflow-y: auto;
      line-height: 1.6;
      max-width: 800px;
      margin: 0 auto;
      width: 100%;
    }

    .viewer-content h1,
    .viewer-content h2,
    .viewer-content h3 {
      color: #334155;
      margin: 1.5rem 0 1rem 0;
    }

    .viewer-content h1 {
      font-size: 1.8rem;
      border-bottom: 2px solid #059669;
      padding-bottom: 0.5rem;
    }

    .viewer-content h2 {
      font-size: 1.4rem;
      color: #059669;
    }

    .viewer-content h3 {
      font-size: 1.2rem;
    }

    .viewer-content p {
      margin: 1rem 0;
      color: #475569;
    }

    .viewer-content ul,
    .viewer-content ol {
      margin: 1rem 0;
      padding-left: 2rem;
    }

    .viewer-content li {
      margin: 0.5rem 0;
      color: #475569;
    }

    .viewer-content strong {
      color: #334155;
    }

    /* Back to chat button */
    .back-to-chat-btn {
      background: #059669;
      color: white;
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s;
      position: fixed;
      top: 1rem;
      left: 1rem;
      z-index: 1000;
      display: none;
      align-items: center;
      gap: 0.5rem;
    }

    .back-to-chat-btn.show {
      display: flex;
    }

    .back-to-chat-btn:hover {
      background: #047857;
      transform: translateY(-1px);
    }

    /* Lead capture modal (triggered after viewing 2+ documents) */
    .lead-capture-modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);
      z-index: 10000;
      align-items: center;
      justify-content: center;
      padding: 1rem;
    }

    .lead-capture-modal.show {
      display: flex;
    }

    .lead-capture-card {
      background: white;
      border-radius: 16px;
      padding: 2rem;
      max-width: 500px;
      width: 100%;
      text-align: center;
    }

    .lead-capture-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    .lead-capture-title {
      font-size: 1.5rem;
      color: #334155;
      margin-bottom: 1rem;
      font-weight: 600;
    }

    .lead-capture-message {
      color: #64748b;
      line-height: 1.5;
      margin-bottom: 2rem;
    }

    .lead-capture-actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
    }

    /* =========================== */
    /* END DOCUMENT VIEWING STYLES */
    /* =========================== */

    .chat-messages {
      flex: 1;
      padding: 1.5rem 2rem;
      overflow-y: auto;
      min-height: 500px;
      background: #fafbfc;
    }

    .message {
      margin-bottom: 1rem;
      display: flex;
      flex-direction: column;
    }

    .message.user {
      align-items: flex-end;
    }

    .message.assistant {
      align-items: flex-start;
    }

    .message-bubble {
      max-width: 70%;
      padding: 1rem 1.25rem;
      border-radius: 18px;
      word-wrap: break-word;
      line-height: 1.5;
    }

    .message.user .message-bubble {
      background: #059669;
      color: white;
    }

    .message.assistant .message-bubble {
      background: white;
      color: #334155;
      border: 1px solid #e2e8f0;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    /* NEW: Compliance rejection styles */
    .message.assistant.compliance-rejection .message-bubble {
      background: linear-gradient(135deg, #fef2f2, #fef7f7);
      border: 2px solid #ef4444;
      max-width: 85%;
    }

    .compliance-rejection-header {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #dc2626;
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }

    .rejection-icon {
      font-size: 1.5rem;
    }

    .compliance-rejection-body p {
      margin-bottom: 0.75rem;
      color: #374151;
      line-height: 1.5;
    }

    .sample-questions {
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 1rem;
      margin-top: 1rem;
    }

    .sample-questions p {
      margin-bottom: 0.5rem !important;
      font-weight: 600;
      color: #059669;
    }

    .sample-buttons {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    .sample-question-btn {
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      padding: 0.5rem 0.75rem;
      border-radius: 6px;
      text-align: left;
      cursor: pointer;
      transition: all 0.2s;
      font-size: 0.9rem;
      color: #475569;
    }

    .sample-question-btn:hover {
      background: #059669;
      color: white;
      border-color: #059669;
      transform: translateX(4px);
    }

    /* END compliance rejection styles */

    .typing-message {
      margin-bottom: 1rem;
      display: flex;
      align-items: flex-start;
    }

    .typing-bubble {
      background: white;
      border: 1px solid #e2e8f0;
      border-radius: 18px;
      padding: 0.75rem 1rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      display: flex;
      align-items: center;
      gap: 0.5rem;
      max-width: 80%;
    }

    .typing-text {
      color: #64748b;
      font-style: italic;
      font-size: 0.9rem;
    }

    .typing-dots {
      display: flex;
      gap: 3px;
    }

    .typing-dot {
      width: 6px;
      height: 6px;
      background: #94a3b8;
      border-radius: 50%;
      animation: typingAnimation 1.4s infinite ease-in-out;
    }

    .typing-dot:nth-child(1) {
      animation-delay: 0s;
    }

    .typing-dot:nth-child(2) {
      animation-delay: 0.2s;
    }

    .typing-dot:nth-child(3) {
      animation-delay: 0.4s;
    }

    @keyframes typingAnimation {

      0%,
      60%,
      100% {
        transform: translateY(0);
        opacity: 0.4;
      }

      30% {
        transform: translateY(-10px);
        opacity: 1;
      }
    }

    .typing-cursor {
      display: inline-block;
      background: #059669;
      width: 2px;
      height: 1.2em;
      margin-left: 1px;
      animation: blink 1s infinite;
    }

    @keyframes blink {

      0%,
      50% {
        opacity: 1;
      }

      51%,
      100% {
        opacity: 0;
      }
    }

    .quick-replies {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin-top: 0.5rem;
      max-width: 80%;
      opacity: 0;
      transform: translateY(10px);
      animation: slideInQuickReplies 0.4s ease-out forwards;
    }

    @keyframes slideInQuickReplies {
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .quick-reply {
      background: #f1f5f9;
      border: 1px solid #e2e8f0;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      cursor: pointer;
      font-size: 0.875rem;
      transition: all 0.2s;
    }

    .quick-reply:hover {
      background: #ecfdf5;
      color: #059669;
      border-color: #059669;
      transform: translateY(-1px);
    }

    .chat-input {
      padding: 1.5rem 2rem;
      border-top: 1px solid #e2e8f0;
      background: white;
    }

    .input-row {
      display: flex;
      gap: 0.5rem;
      align-items: flex-end;
    }

    .message-input {
      flex: 1;
      padding: 0.75rem;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      font-size: 1rem;
      font-family: inherit;
      resize: none;
      min-height: 44px;
      max-height: 120px;
      transition: border-color 0.2s;
    }

    .message-input:focus {
      outline: none;
      border-color: #059669;
    }

    .message-input:disabled {
      background: #f1f5f9;
      color: #64748b;
      cursor: not-allowed;
      border-color: #d1d5db;
    }

    .send-btn {
      padding: 0.75rem 1.5rem;
      background: #059669;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      transition: all 0.2s;
    }

    .send-btn:hover:not(:disabled) {
      background: #047857;
      transform: translateY(-1px);
    }

    .send-btn:disabled {
      background: #94a3b8;
      cursor: not-allowed;
      transform: none;
    }

    .status {
      padding: 0.5rem 1rem;
      margin: 0.5rem;
      border-radius: 4px;
      font-size: 0.875rem;
      transition: all 0.3s ease;
    }

    .status.info {
      background: #eff6ff;
      color: #1e40af;
      border: 1px solid #dbeafe;
    }

    .status.error {
      background: #fef2f2;
      color: #dc2626;
      border: 1px solid #fecaca;
    }

    .status.success {
      background: #f0fdf4;
      color: #166534;
      border: 1px solid #bbf7d0;
    }

    /* Message formatting styles */
    .message-bubble h1,
    .message-bubble h2,
    .message-bubble h3,
    .message-bubble h4 {
      color: #059669;
      margin: 0.5rem 0;
    }

    .message-bubble ul,
    .message-bubble ol {
      margin: 0.5rem 0;
      padding-left: 1.5rem;
    }

    .message-bubble li {
      margin: 0.25rem 0;
      line-height: 1.4;
    }

    .message-bubble strong {
      color: #1e293b;
    }

    .message-bubble em {
      color: #475569;
    }

    .message-bubble code {
      background: #f1f5f9;
      padding: 0.125rem 0.25rem;
      border-radius: 3px;
      font-family: 'Monaco', 'Consolas', monospace;
      font-size: 0.85em;
    }

    .message-bubble p {
      margin: 0.5rem 0;
      line-height: 1.5;
    }

    .message-bubble p:first-child {
      margin-top: 0;
    }

    .message-bubble p:last-child {
      margin-bottom: 0;
    }

    /* Responsive design */
    @media (max-width: 1024px) {
      .chat-container {
        margin: 0.5rem;
        width: calc(100% - 1rem);
        max-width: none;
      }

      .chat-messages {
        padding: 1rem 1.5rem;
        min-height: 400px;
      }

      .chat-input {
        padding: 1rem 1.5rem;
      }

      .pre-prompt-suggestions {
        padding: 1rem 1.5rem;
      }

      .message-bubble {
        max-width: 85%;
        padding: 0.875rem 1.125rem;
      }

      .document-gallery {
        padding: 1.5rem;
      }

      .viewer-content {
        padding: 1.5rem;
      }
    }

    @media (max-width: 768px) {
      .chat-container {
        margin: 0.25rem;
        width: calc(100% - 0.5rem);
        height: calc(100vh - 0.5rem);
      }

      .header {
        padding: 1rem;
        flex-wrap: wrap;
        gap: 0.5rem;
      }

      .user-greeting {
        order: 1;
        flex-basis: 100%;
        text-align: center;
      }

      .logo {
        order: 2;
        flex-basis: 50%;
        text-align: left;
        font-size: 1.25rem;
      }

      .demo-info {
        order: 3;
        flex-basis: 50%;
        justify-content: flex-end;
      }

      .chat-messages {
        padding: 1rem;
        min-height: 350px;
      }

      .chat-input {
        padding: 1rem;
      }

      .pre-prompt-suggestions {
        padding: 1rem;
      }

      .message-bubble {
        max-width: 90%;
        padding: 0.75rem 1rem;
      }

      .quick-replies {
        max-width: 90%;
      }

      .suggestion-categories {
        grid-template-columns: 1fr;
      }

      .welcome-banner {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
      }

      .welcome-content {
        text-align: center;
      }

      /* Mobile responsive compliance rejection */
      .sample-buttons {
        gap: 0.25rem;
      }

      .sample-question-btn {
        font-size: 0.85rem;
        padding: 0.4rem 0.6rem;
      }

      .document-gallery {
        padding: 1rem;
      }

      .document-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .viewer-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
      }

      .viewer-actions {
        width: 100%;
        justify-content: space-between;
      }

      .viewer-content {
        padding: 1rem;
      }

      .back-to-chat-btn {
        position: fixed;
        bottom: 1rem;
        right: 1rem;
        top: auto;
        left: auto;
      }
    }
  </style>
</head>

<body>
  <!-- Back to chat button (shows when viewing documents) -->
  <button class="back-to-chat-btn" id="backToChatBtn" onclick="showChatView()">
    ← Back to Chat
  </button>

  <div class="header">
    <div class="user-greeting" id="userGreeting">
      <!-- This is where the greeting will appear -->
    </div>
    <div class="logo">ArionComply Demo</div>
    <div class="demo-info">
      <div class="questions-counter" id="questionsCounter">Loading...</div>
    </div>
  </div>

  <div class="chat-container">
    <div class="chat-header">
      <button class="demo-info-toggle" onclick="toggleDemoInfo()" id="demoInfoBtn">
        ℹ️ Demo Info
      </button>

      <h2>🤖 ArionComply Assistant</h2>
      <p>Your AI-powered compliance automation guide</p>

      <div class="demo-info-panel" id="demoInfoPanel">
        <div class="info-section">
          <h4>🎯 What This Demo Includes</h4>
          <ul class="info-list">
            <li>• AI-powered guidance on ISO 27001, GDPR, AI governance & security</li>
            <li>• Smart question suggestions to get you started</li>
            <li>• Contextual follow-up questions based on your interests</li>
            <li>• Expert knowledge without the need for consultation calls</li>
          </ul>
        </div>

        <div class="info-section">
          <h4>📊 Demo Limitations</h4>
          <ul class="info-list">
            <li>• <strong>12 questions per session</strong> - Start fresh anytime</li>
            <li>• <strong>20 questions per day</strong> - Resets at midnight</li>
            <li>• <strong>200 questions per month</strong> - Resets monthly</li>
            <li>• <strong>Compliance topics only</strong> - Focused expertise</li>
          </ul>
          <div class="info-highlight">
            <strong>💡 Tip:</strong> Make your questions specific! Instead of "Tell me about ISO 27001",
            ask "What documents do I need for ISO 27001 certification?" for better results.
          </div>
        </div>

        <div class="info-section">
          <h4>🚀 Full Version Benefits</h4>
          <ul class="info-list">
            <li>• Unlimited questions and conversations</li>
            <li>• Generate actual compliance documents</li>
            <li>• Save and continue sessions</li>
            <li>• Schedule expert consultations</li>
            <li>• Custom compliance roadmaps</li>
          </ul>
        </div>
      </div>

      <div class="demo-limits-warning" id="demoWarning">
        <strong>⚠️ Demo Limitations:</strong><br>
        • Session: 12 questions per session<br>
        • Daily: 20 questions per day<br>
        • Monthly: 200 questions per month<br>
        • Full features available with paid access
      </div>
    </div>

    <!-- Demo Limit Overlay -->
    <div class="demo-limit-overlay" id="limitOverlay">
      <div class="limit-reached-card">
        <div class="limit-icon">🎯</div>
        <h3 class="limit-title">Demo Limit Reached!</h3>
        <div class="limit-message">
          <p>You've used all <strong>12 demo questions</strong>. Ready to unlock the full ArionComply experience?</p>
          <p><strong>With full access you get:</strong></p>
          <ul style="text-align: left; margin: 1rem 0; color: #374151;">
            <li>✅ Unlimited questions & conversations</li>
            <li>📄 Generate actual compliance documents</li>
            <li>🎯 Advanced goal-specific workflows</li>
            <li>👨‍💼 Expert consultation scheduling</li>
            <li>💾 Save & continue sessions</li>
          </ul>
        </div>
        <div>
          <button class="upgrade-btn" onclick="requestFullAccess()">Get Full Access</button>
          <br>
          <button class="continue-btn" onclick="closeLimitOverlay()" id="continueDemoBtn">Maybe Later</button>
        </div>
      </div>
    </div>

    <!-- ======================== -->
    <!-- CHAT VIEW (DEFAULT) -->
    <!-- ======================== -->

    <!-- Welcome Banner -->
    <div class="welcome-banner" id="welcomeBanner">
      <div class="welcome-icon">🎉</div>
      <div class="welcome-content">
        <div class="welcome-title">Welcome to ArionComply Demo!</div>
        <div class="welcome-text">
          You have <strong>12 questions this session</strong>, <strong>20 today</strong>, <strong>200 this
            month</strong>.
          Click "<strong>ℹ️ Demo Info</strong>" above to learn more, or dive right in with the suggestions below!
        </div>
      </div>
      <button class="banner-close" onclick="closeWelcomeBanner()" title="Close">×</button>
    </div>

    <!-- Pre-prompt suggestions section -->
    <div class="pre-prompt-suggestions" id="prePromptSuggestions">
      <div class="suggestions-header">
        <div class="suggestions-title">
          💡 Need help getting started? Try these questions:
        </div>
        <button class="hide-suggestions" onclick="hideSuggestions()">
          Hide suggestions
        </button>
      </div>

      <div class="suggestion-categories">
        <div class="category-group">
          <div class="category-title">🏆 ISO 27001</div>
          <div class="suggestion-chips">
            <div class="suggestion-chip" onclick="useSuggestion(this)">How to get ISO 27001 certified?</div>
            <div class="suggestion-chip" onclick="useSuggestion(this)">What is a risk register?</div>
            <div class="suggestion-chip" onclick="useSuggestion(this)">ISO 27001 implementation timeline</div>
            <div class="suggestion-chip" onclick="useSuggestion(this)">Statement of Applicability guide</div>
          </div>
        </div>

        <div class="category-group">
          <div class="category-title">🛡️ GDPR</div>
          <div class="suggestion-chips">
            <div class="suggestion-chip" onclick="useSuggestion(this)">What is GDPR compliance?</div>
            <div class="suggestion-chip" onclick="useSuggestion(this)">How to create ROPA document?</div>
            <div class="suggestion-chip" onclick="useSuggestion(this)">GDPR data mapping requirements</div>
            <div class="suggestion-chip" onclick="useSuggestion(this)">Privacy impact assessment</div>
          </div>
        </div>

        <div class="category-group">
          <div class="category-title">🤖 AI Governance</div>
          <div class="suggestion-chips">
            <div class="suggestion-chip" onclick="useSuggestion(this)">EU AI Act requirements</div>
            <div class="suggestion-chip" onclick="useSuggestion(this)">AI risk management framework</div>
            <div class="suggestion-chip" onclick="useSuggestion(this)">Ethical AI guidelines</div>
            <div class="suggestion-chip" onclick="useSuggestion(this)">AI bias testing methods</div>
          </div>
        </div>
      </div>
    </div>

    <div class="chat-messages" id="messages">
      <!-- Show suggestions button when hidden -->
      <div id="showSuggestionsContainer" style="display: none;">
        <button class="show-suggestions-btn" onclick="showSuggestions()">
          💡 Show example questions
        </button>
      </div>

      <div class="message assistant">
        <div class="message-bubble">
          Hi! I'm your ArionComply Guide. I can help you with ISO 27001 certification, GDPR compliance, AI governance,
          and security best practices. What would you like to explore today?
        </div>
        <div class="quick-replies">
          <button class="quick-reply" onclick="sendQuickReply('Get ISO 27001 certified')">Get ISO 27001
            certified</button>
          <button class="quick-reply" onclick="sendQuickReply('GDPR compliance help')">GDPR compliance help</button>
          <button class="quick-reply" onclick="sendQuickReply('AI governance guidance')">AI governance guidance</button>
        </div>
      </div>
    </div>

    <div class="status info" id="status">Initializing session...</div>

    <div class="chat-input">
      <div class="input-row">
        <textarea id="messageInput" class="message-input" placeholder="Ask me about compliance requirements..."
          rows="1"></textarea>
        <button id="sendBtn" class="send-btn" disabled>Send</button>
      </div>
    </div>

    <!-- ======================== -->
    <!-- DOCUMENT GALLERY VIEW -->
    <!-- ======================== -->
    <div class="document-gallery" id="documentGallery">
      <div class="gallery-header">
        <h1 class="gallery-title">📄 Your Generated Documents</h1>
        <p class="gallery-subtitle">AI-generated compliance documents tailored for your organization</p>
      </div>

      <div class="document-grid" id="documentGrid">
        <!-- Documents will be populated here -->
      </div>
    </div>

    <!-- ======================== -->
    <!-- DOCUMENT VIEWER -->
    <!-- ======================== -->
    <div class="document-viewer" id="documentViewer">
      <div class="viewer-header">
        <div class="viewer-title-area">
          <button class="back-btn" onclick="showDocumentGallery()">
            ← Back to Documents
          </button>
          <h1 class="viewer-document-title" id="viewerTitle">Document Title</h1>
        </div>

        <div class="viewer-actions">
          <button class="action-btn secondary" onclick="showChatView()">Back to Chat</button>
          <button class="action-btn primary" onclick="showLeadCaptureModal()">Request Full Access</button>
        </div>
      </div>

      <div class="viewer-content" id="viewerContent">
        <!-- Document content will be displayed here -->
      </div>
    </div>
  </div>

  <!-- ======================== -->
  <!-- LEAD CAPTURE MODAL -->
  <!-- ======================== -->
  <div class="lead-capture-modal" id="leadCaptureModal">
    <div class="lead-capture-card">
      <div class="lead-capture-icon">🚀</div>
      <h3 class="lead-capture-title">Ready for Full Access?</h3>
      <div class="lead-capture-message">
        <p>You've viewed multiple documents and experienced our AI-powered compliance automation!</p>
        <p>Get unlimited access to generate, download, and customize all compliance documents.</p>
      </div>
      <div class="lead-capture-actions">
        <button class="action-btn secondary" onclick="hideLeadCaptureModal()">Maybe Later</button>
        <button class="action-btn primary" onclick="requestFullAccess()">Get Full Access</button>
      </div>
    </div>
  </div>

  <script>
    let isWaiting = false;
    let suggestionsVisible = true;

    // NEW: View management variables
    let currentView = 'chat'; // 'chat', 'documents', 'viewer'
    let documentsViewed = 0; // Track for lead capture
    let currentDocuments = []; // Store fetched documents
    let currentDocument = null; // Currently viewed document

    // Demo limitation variables with daily and monthly tracking
    let questionCount = 0;
    let dailyQuestionCount = 0;
    let monthlyQuestionCount = 0;

    // Limits configuration
    const LIMITS = {
      daily: 20,
      monthly: 200,
      session: 12
    };

    let isAdmin = false;
    let limitReached = false;
    let limitType = null;

    // DOM Elements
    const messagesDiv = document.getElementById('messages');
    const messageInput = document.getElementById('messageInput');
    const sendBtn = document.getElementById('sendBtn');
    const statusDiv = document.getElementById('status');
    const prePromptDiv = document.getElementById('prePromptSuggestions');
    const showSuggestionsContainer = document.getElementById('showSuggestionsContainer');
    const questionsCounter = document.getElementById('questionsCounter');
    const demoWarning = document.getElementById('demoWarning');
    const limitOverlay = document.getElementById('limitOverlay');
    const welcomeBanner = document.getElementById('welcomeBanner');
    const demoInfoPanel = document.getElementById('demoInfoPanel');
    const demoInfoBtn = document.getElementById('demoInfoBtn');

    // NEW: Document view elements
    const documentGallery = document.getElementById('documentGallery');
    const documentViewer = document.getElementById('documentViewer');
    const backToChatBtn = document.getElementById('backToChatBtn');
    const leadCaptureModal = document.getElementById('leadCaptureModal');

    // Initialize
    document.addEventListener('DOMContentLoaded', init);

    async function init() {
      console.log('🚀 Initializing demo...');

      // Get session from URL - ADD THIS
      const urlParams = new URLSearchParams(window.location.search);
      sessionId = urlParams.get('session'); // This uses sessionId from utils.js

      if (!sessionId) {
        alert('No session found. Redirecting to login...');
        window.location.href = 'index.html';
        return;
      }

      // Setup event listeners
      messageInput.addEventListener('input', handleInput);
      messageInput.addEventListener('keydown', handleKeydown);
      sendBtn.addEventListener('click', sendMessage);

      // Auto-hide suggestions after user starts typing
      messageInput.addEventListener('focus', () => {
        if (suggestionsVisible && messageInput.value.trim()) {
          hideSuggestions();
        }
      });

      // Check for admin status
      checkAdminStatus();

      // Initialize welcome banner
      initializeWelcomeBanner();

      // Initialize question counter
      loadQuestionCount();
      updateQuestionCounter();

      // Check suggestions preference
      checkSuggestionsPreference();

      // Show success status
      showStatus('Demo ready! Ask me anything about compliance.', 'success');
      setTimeout(() => hideStatus(), 3000);
    }

    // ======================== //
    // NEW: VIEW MANAGEMENT     //
    // ======================== //

    function showChatView() {
      currentView = 'chat';

      // Hide document views
      documentGallery.classList.remove('active');
      documentViewer.classList.remove('active');

      // Show chat elements
      document.getElementById('welcomeBanner').style.display = 'flex';
      document.getElementById('prePromptSuggestions').style.display = 'block';
      document.getElementById('messages').style.display = 'block';
      document.getElementById('status').style.display = 'block';
      document.getElementById('messageInput').parentElement.parentElement.style.display = 'block';

      // Hide back button
      backToChatBtn.classList.remove('show');

      console.log('📱 Switched to chat view');
    }

    function showDocumentGallery() {
      currentView = 'documents';

      // Hide chat elements
      document.getElementById('welcomeBanner').style.display = 'none';
      document.getElementById('prePromptSuggestions').style.display = 'none';
      document.getElementById('messages').style.display = 'none';
      document.getElementById('status').style.display = 'none';
      document.getElementById('messageInput').parentElement.parentElement.style.display = 'none';

      // Hide document viewer
      documentViewer.classList.remove('active');

      // Show document gallery
      documentGallery.classList.add('active');

      // Show back button
      backToChatBtn.classList.add('show');

      // Load documents
      loadDocuments();

      console.log('📄 Switched to document gallery');
    }

    function showDocumentViewer(document) {
      currentView = 'viewer';
      currentDocument = document;

      // Hide gallery
      documentGallery.classList.remove('active');

      // Show viewer
      documentViewer.classList.add('active');

      // Update viewer content
      document.getElementById('viewerTitle').textContent = document.title;
      document.getElementById('viewerContent').innerHTML = formatDocumentContent(document.content);

      // Track document view for lead capture
      documentsViewed++;
      console.log(`📖 Viewing document: ${document.title} (${documentsViewed} viewed)`);

      // Show lead capture after viewing 2+ documents
      if (documentsViewed >= 2) {
        setTimeout(() => showLeadCaptureModal(), 3000);
      }
    }

    // ======================== //
    // NEW: DOCUMENT FUNCTIONS  //
    // ======================== //

    async function loadDocuments() {
      try {
        console.log('📄 Loading documents for session:', sessionId);

        const response = await ArionUtils.api.call('get-documents', {}, sessionId);

        if (response.success && response.documents) {
          currentDocuments = response.documents;
          renderDocuments(response.documents);
          console.log(`✅ Loaded ${response.documents.length} documents`);
        } else {
          console.log('❌ No documents found or error:', response.error);
          renderNoDocuments();
        }
      } catch (error) {
        console.error('❌ Failed to load documents:', error);
        renderNoDocuments();
      }
    }

    function renderDocuments(documents) {
      const grid = document.getElementById('documentGrid');

      if (!documents || documents.length === 0) {
        renderNoDocuments();
        return;
      }

      grid.innerHTML = documents.map(doc => `
        <div class="document-card" onclick="viewDocument('${doc.id}')">
          <div class="document-watermark">PREVIEW ONLY</div>
          <div class="document-card-header">
            <div class="document-icon">${doc.icon}</div>
            <div class="document-info">
              <h3>${doc.title}</h3>
              <div class="document-type">${doc.type.replace('_', ' ')}</div>
            </div>
          </div>
          <div class="document-preview">
            ${doc.description}
          </div>
          <div class="document-footer">
            <div class="document-date">Generated ${new Date(doc.createdAt).toLocaleDateString()}</div>
            <button class="view-btn" onclick="event.stopPropagation(); viewDocument('${doc.id}')">
              View Document
            </button>
          </div>
        </div>
      `).join('');
    }

    function renderNoDocuments() {
      const grid = document.getElementById('documentGrid');
      grid.innerHTML = `
        <div style="grid-column: 1 / -1; text-align: center; padding: 3rem; color: #64748b;">
          <div style="font-size: 3rem; margin-bottom: 1rem;">📄</div>
          <h3 style="margin-bottom: 0.5rem; color: #374151;">No Documents Yet</h3>
          <p>Start a conversation with our AI to generate your compliance documents!</p>
          <button class="action-btn primary" onclick="showChatView()" style="margin-top: 1rem;">
            Start Chat
          </button>
        </div>
      `;
    }

    async function viewDocument(documentId) {
      try {
        console.log('📖 Fetching document:', documentId);

        const response = await ArionUtils.api.call('get-document', { documentId }, sessionId);

        if (response.success && response.document) {
          showDocumentViewer(response.document);
        } else {
          console.error('❌ Failed to fetch document:', response.error);
          alert('Failed to load document. Please try again.');
        }
      } catch (error) {
        console.error('❌ Error fetching document:', error);
        alert('Failed to load document. Please try again.');
      }
    }

    function formatDocumentContent(content) {
      // Convert markdown-like content to HTML
      return content
        .replace(/^# (.*$)/gim, '<h1>$1</h1>')
        .replace(/^## (.*$)/gim, '<h2>$1</h2>')
        .replace(/^### (.*$)/gim, '<h3>$1</h3>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/^\* (.*$)/gim, '<li>$1</li>')
        .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')
        .replace(/<\/ul>\s*<ul>/g, '')
        .replace(/\n\n/g, '</p><p>')
        .replace(/^(.)/gm, '<p>$1')
        .replace(/(.)$/gm, '$1</p>')
        .replace(/<p><h/g, '<h')
        .replace(/<\/h([1-6])><\/p>/g, '</h$1>')
        .replace(/<p><ul>/g, '<ul>')
        .replace(/<\/ul><\/p>/g, '</ul>');
    }

    // ======================== //
    // NEW: LEAD CAPTURE        //
    // ======================== //

    function showLeadCaptureModal() {
      leadCaptureModal.classList.add('show');
      console.log('🚀 Showing lead capture modal');
    }

    function hideLeadCaptureModal() {
      leadCaptureModal.classList.remove('show');
      console.log('🚀 Hiding lead capture modal');
    }

    function requestFullAccess() {
      // This would integrate with your actual signup/pricing flow
      alert('This would redirect to your signup/pricing page');
      window.location.href = 'index.html#signup';
    }

    // ======================== //
    // NEW: ENHANCED MESSAGING  //
    // ======================== //

    async function processMessage(text) {
      let typingIndicator = null;

      try {
        isWaiting = true;
        typingIndicator = showTypingIndicator();

        console.log('🤖 Processing with intelligent cache + Claude...');

        // *** NEW: Check if it's a document generation request ***
        if (window.DocumentGenerator && DocumentGenerator.isDocumentRequest(text)) {
          console.log('📄 Document request detected');

          if (typingIndicator) {
            removeTypingIndicator(typingIndicator);
          }

          // Handle document generation with limits
          const documentResponse = await DocumentGenerator.handleDocumentRequest(text, sessionId);

          // Display response using your existing typeMessage function
          await typeMessage(documentResponse.response, documentResponse.quickReplies || []);

          return;
        }

        // *** EXISTING: Your regular chat processing ***
        const response = await ArionUtils.api.call('query-agent', {
          message: text,
          timestamp: new Date().toISOString()
        }, sessionId);

        if (typingIndicator) {
          removeTypingIndicator(typingIndicator);
        }

        if (response.success && response.response) {
          // Check if documents were generated (existing logic)
          if (response.suggestedActions && response.suggestedActions.includes('show_documents')) {
            // Add "View Documents" button to quick replies
            const quickReplies = response.quickReplies || [];
            quickReplies.push('📄 View My Documents');
          }

          await typeMessage(response.response, response.quickReplies || []);

          // Check if AI mentioned document generation (existing logic)
          if (response.response.toLowerCase().includes('generated') &&
            response.response.toLowerCase().includes('document')) {
            console.log('🎯 AI mentioned document generation - documents might be ready');
          }

        } else {
          throw new Error('Invalid response format');
        }

      } catch (error) {
        console.error('⌛ Critical error:', error);

        if (typingIndicator) {
          removeTypingIndicator(typingIndicator);
        }

        await typeMessage(
          'I apologize, but I\'m experiencing technical difficulties. Please try again.',
          ['Try again', 'Contact support']
        );

      } finally {
        isWaiting = false;
        handleInput(); // This is the correct function name in your demo.html (not sendBtn.disabled)
      }
    }

    // STEP 3: Add quick reply handler for "View My Documents"
    // Add this to your existing sendQuickReply function or create it:

    function sendQuickReply(text) {
      // Check for special document-related quick replies
      if (text === '📄 View My Documents' || text === 'View My Documents') {
        // Call the document generator to show documents
        if (window.DocumentGenerator) {
          DocumentGenerator.viewDocuments();
        } else {
          console.error('DocumentGenerator not loaded');
          alert('Document generator not available. Please refresh the page.');
        }
        return;
      }

      if (text === '🚀 Request Full Access' || text === 'Request full access') {
        // Use your existing lead capture modal
        if (typeof showLeadCaptureModal === 'function') {
          showLeadCaptureModal();
        } else {
          // Fallback to your existing upgrade flow
          alert('Request full access to unlock unlimited documents!');
          window.location.href = 'index.html#signup';
        }
        return;
      }

      // Check limit before sending quick reply (your existing logic)
      if (!checkQuestionLimit()) {
        return;
      }

      // Set the input value and send message
      messageInput.value = text;
      sendMessage(); // This is the correct function name in your demo.html
    }

    // ======================== //
    // EXISTING DEMO FUNCTIONS  //
    // (keeping all unchanged)  //
    // ======================== //

    // Demo Info Panel Toggle Function
    function toggleDemoInfo() {
      const isVisible = demoInfoPanel.classList.contains('show');

      if (isVisible) {
        demoInfoPanel.classList.remove('show');
        demoInfoBtn.textContent = 'ℹ️ Demo Info';
        demoInfoBtn.style.background = 'rgba(255, 255, 255, 0.2)';
      } else {
        demoInfoPanel.classList.add('show');
        demoInfoBtn.textContent = '✕ Close Info';
        demoInfoBtn.style.background = 'rgba(255, 255, 255, 0.3)';
      }
    }

    // Welcome Banner Functions
    function initializeWelcomeBanner() {
      // Check if welcome banner was previously closed
      const bannerClosed = localStorage.getItem('arion-welcome-banner-closed');

      if (bannerClosed === 'true') {
        welcomeBanner.classList.add('hidden');
      } else {
        welcomeBanner.classList.remove('hidden');
      }
    }

    function closeWelcomeBanner() {
      welcomeBanner.classList.add('hidden');
      localStorage.setItem('arion-welcome-banner-closed', 'true');
    }

    function checkWelcomeBannerPreference() {
      initializeWelcomeBanner();
    }

    // Admin status check
    function checkAdminStatus() {
      const urlParams = new URLSearchParams(window.location.search);
      const adminInfo = localStorage.getItem('arion-admin-info');

      if (urlParams.get('admin') === 'true' || adminInfo) {
        isAdmin = true;
        console.log('👑 Admin mode detected - unlimited questions');
      }
    }

    // Question counter functions
    function updateQuestionCounter() {
      if (isAdmin) {
        questionsCounter.innerHTML = '<span style="color: #059669;">👑 Admin Mode</span>';
        questionsCounter.className = 'questions-counter';
        return;
      }

      const dailyRemaining = LIMITS.daily - dailyQuestionCount;
      const monthlyRemaining = LIMITS.monthly - monthlyQuestionCount;
      const sessionRemaining = LIMITS.session - questionCount;

      const minRemaining = Math.min(dailyRemaining, monthlyRemaining, sessionRemaining);

      questionsCounter.innerHTML = `
        <span>Session: ${questionCount}/${LIMITS.session}</span> | 
        <span>Daily: ${dailyQuestionCount}/${LIMITS.daily}</span> | 
        <span>Monthly: ${monthlyQuestionCount}/${LIMITS.monthly}</span>
      `;

      if (minRemaining <= 0) {
        questionsCounter.className = 'questions-counter danger';
      } else if (minRemaining <= 3) {
        questionsCounter.className = 'questions-counter danger';
      } else if (minRemaining <= 5) {
        questionsCounter.className = 'questions-counter warning';
      } else {
        questionsCounter.className = 'questions-counter';
      }

      if (minRemaining <= 5 && minRemaining > 0) {
        showDemoWarning();
      }
    }

    function incrementQuestionCount() {
      if (!isAdmin) {
        questionCount++;
        dailyQuestionCount++;
        monthlyQuestionCount++;

        updateQuestionCounter();
        saveLimitData();

        // Show helpful info when limits are getting low
        const dailyRemaining = LIMITS.daily - dailyQuestionCount;
        const monthlyRemaining = LIMITS.monthly - monthlyQuestionCount;
        const sessionRemaining = LIMITS.session - questionCount;

        if (Math.min(dailyRemaining, monthlyRemaining, sessionRemaining) <= 3) {
          const panel = document.getElementById('demoInfoPanel');
          const button = document.querySelector('.demo-info-toggle');

          if (panel && button && !panel.classList.contains('show')) {
            toggleDemoInfo();
          }
        }
      }
    }

    function checkQuestionLimit() {
      if (isAdmin) return true;

      const dailyRemaining = LIMITS.daily - dailyQuestionCount;
      const monthlyRemaining = LIMITS.monthly - monthlyQuestionCount;
      const sessionRemaining = LIMITS.session - questionCount;

      if (sessionRemaining <= 0) {
        limitType = 'session';
        showLimitOverlay();
        return false;
      } else if (dailyRemaining <= 0) {
        limitType = 'daily';
        showLimitOverlay();
        return false;
      } else if (monthlyRemaining <= 0) {
        limitType = 'monthly';
        showLimitOverlay();
        return false;
      }

      return true;
    }

    function showLimitOverlay() {
      const limitIcon = document.querySelector('.limit-icon');
      const limitTitle = document.querySelector('.limit-title');
      const limitMessage = document.querySelector('.limit-message');

      switch (limitType) {
        case 'daily':
          limitIcon.textContent = '📅';
          limitTitle.textContent = 'Daily Limit Reached!';
          break;
        case 'monthly':
          limitIcon.textContent = '📈';
          limitTitle.textContent = 'Monthly Limit Reached!';
          break;
        default:
          limitIcon.textContent = '🎯';
          limitTitle.textContent = 'Session Limit Reached!';
      }

      limitOverlay.classList.add('show');
      limitReached = true;

      messageInput.disabled = true;
      sendBtn.disabled = true;
      messageInput.placeholder = `${limitType.charAt(0).toUpperCase() + limitType.slice(1)} limit reached - upgrade for unlimited access`;
    }

    function loadQuestionCount() {
      try {
        const stored = localStorage.getItem('arion-demo-limits');
        if (!stored) {
          questionCount = 0;
          dailyQuestionCount = 0;
          monthlyQuestionCount = 0;
          return;
        }

        const data = JSON.parse(stored);
        const today = new Date().toDateString();
        const thisMonth = getMonthKey();

        questionCount = data.session || 0;

        if (data.daily && data.daily.date === today) {
          dailyQuestionCount = data.daily.count || 0;
        } else {
          dailyQuestionCount = 0;
        }

        if (data.monthly && data.monthly.month === thisMonth) {
          monthlyQuestionCount = data.monthly.count || 0;
        } else {
          monthlyQuestionCount = 0;
        }

        console.log('📊 Loaded limits:', {
          session: questionCount,
          daily: dailyQuestionCount,
          monthly: monthlyQuestionCount
        });

      } catch (error) {
        console.error('Failed to load limit data:', error);
        questionCount = 0;
        dailyQuestionCount = 0;
        monthlyQuestionCount = 0;
      }
    }

    function saveLimitData() {
      const today = new Date().toDateString();
      const thisMonth = getMonthKey();

      const data = {
        session: questionCount,
        daily: {
          count: dailyQuestionCount,
          date: today
        },
        monthly: {
          count: monthlyQuestionCount,
          month: thisMonth
        },
        lastUpdated: new Date().toISOString()
      };

      localStorage.setItem('arion-demo-limits', JSON.stringify(data));
    }

    function getMonthKey() {
      const now = new Date();
      return `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}`;
    }

    function showDemoWarning() {
      const dailyRemaining = LIMITS.daily - dailyQuestionCount;
      const monthlyRemaining = LIMITS.monthly - monthlyQuestionCount;
      const sessionRemaining = LIMITS.session - questionCount;

      let warningText = '<strong>⚠️ Demo Limitations:</strong><br>';

      if (sessionRemaining <= 5) {
        warningText += `• Only ${sessionRemaining} questions left in this session<br>`;
      }
      if (dailyRemaining <= 5) {
        warningText += `• Only ${dailyRemaining} questions left today<br>`;
      }
      if (monthlyRemaining <= 20) {
        warningText += `• Only ${monthlyRemaining} questions left this month<br>`;
      }

      warningText += '• Full features available with paid access';

      demoWarning.innerHTML = warningText;
      demoWarning.classList.add('show');
    }

    // Suggestion management functions
    function hideSuggestions() {
      prePromptDiv.classList.add('hidden');
      showSuggestionsContainer.style.display = 'block';
      suggestionsVisible = false;
      localStorage.setItem('arion-suggestions-hidden', 'true');
    }

    function showSuggestions() {
      prePromptDiv.classList.remove('hidden');
      showSuggestionsContainer.style.display = 'none';
      suggestionsVisible = true;
      localStorage.removeItem('arion-suggestions-hidden');
    }

    function useSuggestion(element) {
      const suggestionText = element.textContent;
      messageInput.value = suggestionText;
      messageInput.focus();
      handleInput();

      setTimeout(() => {
        hideSuggestions();
        sendMessage();
      }, 500);
    }

    function checkSuggestionsPreference() {
      const hidden = localStorage.getItem('arion-suggestions-hidden');
      if (hidden === 'true') {
        hideSuggestions();
      }
    }

    // Message handling functions
    function handleInput() {
      const hasText = messageInput.value.trim().length > 0;
      sendBtn.disabled = !hasText || isWaiting || limitReached;

      messageInput.style.height = 'auto';
      messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';

      if (hasText && suggestionsVisible) {
        hideSuggestions();
      }
    }

    function handleKeydown(e) {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        if (!sendBtn.disabled) {
          sendMessage();
        }
      }
    }

    // MODIFIED: sendMessage function with compliance validation
    async function sendMessage() {
      
      if (window.markUserSessionInitiated) {
        window.markUserSessionInitiated();
      } 
      const text = messageInput.value.trim();
      if (!text || isWaiting || limitReached) return;

      // === NEW: COMPLIANCE VALIDATION ===
      console.log('🔍 Validating compliance topic...');
      const validation = ArionComply.validateQuery(text);

      if (!validation.isCompliant) {
        console.log('❌ Topic validation failed:', validation);

        // Display user message first
        addMessage(text, 'user');

        // Show compliance rejection message (doesn't count against limit)
        displayComplianceRejection(validation.rejectionMessage);

        // Clear input and return
        messageInput.value = '';
        handleInput();
        return;
      }

      console.log('✅ Topic validation passed:', validation.category);
      // === END NEW COMPLIANCE CODE ===

      // Check question limit before sending
      if (!checkQuestionLimit()) {
        return;
      }

      console.log('📤 Sending message:', text);

      // Increment question count
      incrementQuestionCount();

      messageInput.value = '';
      handleInput();
      addMessage(text, 'user');

      // Simulate assistant response
      await processMessage(text);
    }

    // NEW: Compliance rejection display function
    function displayComplianceRejection(rejectionData) {
      const messageDiv = document.createElement('div');
      messageDiv.className = 'message assistant compliance-rejection';

      const bubble = document.createElement('div');
      bubble.className = 'message-bubble';

      bubble.innerHTML = `
        <div class="compliance-rejection-header">
          <span class="rejection-icon">🛡️</span>
          <strong>${rejectionData.title}</strong>
        </div>
        
        <div class="compliance-rejection-body">
          <p>${rejectionData.message}</p>
          <p><strong>Suggestion:</strong> ${rejectionData.suggestion}</p>
          
          <div class="sample-questions">
            <p><strong>Try asking about:</strong></p>
            <div class="sample-buttons">
              ${rejectionData.allowedTopics.map(topic =>
        `<button class="sample-question-btn" onclick="askSampleQuestion('${topic.replace(/'/g, '\\\'')}')">${topic}</button>`
      ).join('')}
            </div>
          </div>
        </div>
      `;

      messageDiv.appendChild(bubble);
      messagesDiv.appendChild(messageDiv);
      scrollToBottom();
    }

    // NEW: Sample question handler
    function askSampleQuestion(question) {
      messageInput.value = question;
      messageInput.focus();

      // Auto-send the sample question
      setTimeout(() => {
        sendMessage();
      }, 500);
    }

    function generateMockResponse(userMessage) {
      const message = userMessage.toLowerCase();

      if (message.includes('iso') || message.includes('27001')) {
        return {
          message: "Great question about ISO 27001! This international standard helps organizations establish an Information Security Management System (ISMS). Key requirements include risk assessments, security policies, and regular audits. The certification process typically takes 6-12 months depending on your organization size.",
          quickReplies: ['Implementation timeline', 'Required documents', 'Cost breakdown', 'Risk assessment guide']
        };
      } else if (message.includes('gdpr')) {
        return {
          message: "GDPR (General Data Protection Regulation) is the EU's comprehensive data protection law. It requires organizations to protect personal data, implement privacy by design, and ensure data subject rights. Key documents include Records of Processing Activities (ROPA) and Data Protection Impact Assessments (DPIA).",
          quickReplies: ['GDPR assessment', 'Create ROPA document', 'Data subject rights', 'Privacy policies']
        };
      } else if (message.includes('ai')) {
        return {
          message: "AI governance is becoming crucial with the EU AI Act coming into force. Organizations need to assess AI system risks, implement bias testing, ensure transparency, and maintain human oversight. High-risk AI systems require conformity assessments and CE marking.",
          quickReplies: ['AI risk assessment', 'Bias testing methods', 'EU AI Act compliance', 'Ethics framework']
        };
      } else {
        return {
          message: "I'm here to help with all your compliance questions! I specialize in ISO 27001, GDPR, AI governance, and security best practices. What specific area would you like to explore?",
          quickReplies: ['ISO 27001 certification', 'GDPR compliance', 'AI governance', 'Security policies']
        };
      }
    }

    async function typeMessage(text, quickReplies) {
      const messageDiv = document.createElement('div');
      messageDiv.className = 'message assistant';

      const bubble = document.createElement('div');
      bubble.className = 'message-bubble';
      messageDiv.appendChild(bubble);

      messagesDiv.appendChild(messageDiv);

      let displayText = '';
      for (let i = 0; i < text.length; i++) {
        displayText += text[i];
        bubble.innerHTML = formatMessage(displayText) + '<span class="typing-cursor"></span>';
        scrollToBottom();
        await new Promise(resolve => setTimeout(resolve, 30));
      }

      bubble.innerHTML = formatMessage(text);

      if (quickReplies.length > 0) {
        removeAllPreviousQuickReplies();

        const repliesDiv = document.createElement('div');
        repliesDiv.className = 'quick-replies';

        quickReplies.forEach(reply => {
          const btn = document.createElement('button');
          btn.className = 'quick-reply';
          btn.textContent = reply;
          btn.onclick = () => sendQuickReply(reply);
          repliesDiv.appendChild(btn);
        });

        messageDiv.appendChild(repliesDiv);
      }

      scrollToBottom();
    }

    function addMessage(text, sender, quickReplies = []) {
      const messageDiv = document.createElement('div');
      messageDiv.className = `message ${sender}`;

      const bubble = document.createElement('div');
      bubble.className = 'message-bubble';
      bubble.innerHTML = formatMessage(text);
      messageDiv.appendChild(bubble);

      messagesDiv.appendChild(messageDiv);
      scrollToBottom();
    }

    function removeAllPreviousQuickReplies() {
      const existingReplies = document.querySelectorAll('.quick-replies');
      existingReplies.forEach(replies => {
        replies.style.opacity = '0.6';
        replies.style.pointerEvents = 'none';

        const buttons = replies.querySelectorAll('.quick-reply');
        buttons.forEach(btn => {
          btn.style.background = '#f1f5f9';
          btn.style.color = '#9ca3af';
          btn.style.cursor = 'not-allowed';
          btn.onclick = null;
        });
      });
    }

    function showTypingIndicator() {
      const typingDiv = document.createElement('div');
      typingDiv.className = 'typing-message';

      const typingBubble = document.createElement('div');
      typingBubble.className = 'typing-bubble';

      typingBubble.innerHTML = `
        <span class="typing-text">Assistant is thinking</span>
        <div class="typing-dots">
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
          <div class="typing-dot"></div>
        </div>
      `;

      typingDiv.appendChild(typingBubble);
      messagesDiv.appendChild(typingDiv);
      scrollToBottom();

      return typingDiv;
    }

    function removeTypingIndicator(typingIndicator) {
      if (typingIndicator && typingIndicator.parentNode) {
        typingIndicator.parentNode.removeChild(typingIndicator);
      }
    }

    function formatMessage(text) {
      return text
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
        .replace(/\n/g, '<br>');
    }

    function showStatus(message, type) {
      statusDiv.textContent = message;
      statusDiv.className = `status ${type}`;
      statusDiv.style.display = 'block';
    }

    function hideStatus() {
      statusDiv.style.display = 'none';
    }

    function scrollToBottom() {
      messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }

    // Limit overlay functions
    function closeLimitOverlay() {
      limitOverlay.classList.remove('show');

      // If it's just a session limit, allow continuing
      if (limitType === 'session') {
        questionCount = 0;
        limitReached = false;
        messageInput.disabled = false;
        sendBtn.disabled = false;
        messageInput.placeholder = "Ask me about compliance requirements...";
        updateQuestionCounter();
        saveLimitData();
      }
    }

    // Debug functions for testing
    window.toggleDemoInfo = toggleDemoInfo;
    window.testDemoInfo = function () {
      toggleDemoInfo();
    };

    window.testWelcomeBanner = function () {
      welcomeBanner.classList.remove('hidden');
      localStorage.removeItem('arion-welcome-banner-closed');
    };

    window.debugInfo = function () {
      console.log('Demo Info Panel Visible:', demoInfoPanel.classList.contains('show'));
      console.log('Welcome Banner Visible:', !welcomeBanner.classList.contains('hidden'));
      console.log('Suggestions Visible:', suggestionsVisible);
      console.log('Session questions:', questionCount + '/' + LIMITS.session);
      console.log('Daily questions:', dailyQuestionCount + '/' + LIMITS.daily);
      console.log('Monthly questions:', monthlyQuestionCount + '/' + LIMITS.monthly);
      console.log('Is admin:', isAdmin);
      console.log('Limit reached:', limitReached);
      console.log('Current view:', currentView);
      console.log('Documents viewed:', documentsViewed);
    };

    window.resetDemo = function () {
      if (confirm('Reset all demo limits?')) {
        localStorage.removeItem('arion-demo-limits');
        questionCount = 0;
        dailyQuestionCount = 0;
        monthlyQuestionCount = 0;
        limitReached = false;
        limitType = null;
        messageInput.disabled = false;
        messageInput.placeholder = "Ask me about compliance requirements...";
        updateQuestionCounter();
        closeLimitOverlay();
        handleInput();
        console.log('✅ All demo limits reset');
      }
    };

    window.testLimit = function () {
      questionCount = LIMITS.session - 1;
      updateQuestionCounter();
      console.log('⚠️ Set to 1 question remaining');
    };

    // NEW: Debug functions for testing document features
    window.testDocumentView = function () {
      showDocumentGallery();
    };

    window.testLeadCapture = function () {
      documentsViewed = 2;
      showLeadCaptureModal();
    };

    // NEW: Debug functions for testing compliance filter
    window.testComplianceFilter = function () {
      const testQueries = [
        "What's the weather?", // Should block
        "How do I implement GDPR?", // Should allow
        "Tell me about movies", // Should block
        "ISO 27001 certification process", // Should allow
      ];

      console.log('🧪 Testing Compliance Filter:');

      testQueries.forEach(query => {
        const result = ArionComply.validateQuery(query);
        console.log(`Query: "${query}"`);
        console.log(`✓ Compliant: ${result.isCompliant}`);
        console.log(`📂 Category: ${result.category || 'blocked'}`);
        console.log('---');
      });
    };

    window.showFilterStats = function () {
      const stats = ArionComply.getFilterStats();
      console.log('📊 Filter Statistics:', stats);
    };
  </script>
  <script src="src/config.js"></script>
  <script src="src/utils.js"></script>
  <script src="src/cache.js"></script>
  <script src="src/demo-enhancements.js"></script>
  <script src="src/feedback-enhancements.js"></script>
  <script src="src/compliance-filter.js"></script>
  <script src="src/generate-docs.js"></script>
</body>

</html>
