part of 'demo_access_cubit.dart';

enum DemoAccessStatus { initial, loading, success, failure }

class DemoAccessState extends Equatable {
  final DemoAccessStatus status;
  final String? error;

  const DemoAccessState({required this.status, this.error});
  const DemoAccessState.initial() : this(status: DemoAccessStatus.initial);

  DemoAccessState copyWith({DemoAccessStatus? status, String? error}) =>
      DemoAccessState(status: status ?? this.status, error: error);

  @override
  List<Object?> get props => [status, error];
}
