# ArionComply Backend Architecture – Complete Breakdown

## 🏗️ System Overview
ArionComply is a compliance automation platform built on Supabase (PostgreSQL) with Deno Edge Functions. The system helps organizations achieve compliance with standards like ISO 27001, GDPR, and AI governance through AI-powered document generation and guided workflows.

**Tech Stack:**
- **Backend**: Deno Edge Functions (TypeScript)
- **Database**: Supabase (PostgreSQL)
- **AI Integration**: Anthropic Claude API
- **Email Service**: Resend API
- **Embeddings**: OpenAI API (for RAG system)

**What This Architecture Breakdown Covers:**

- **10 Database Tables** with clear purposes and relationships
- **20+ API Endpoints** with step-by-step processes and data flows
- **6 Core Services** handling business logic and external integrations
- **5 Major Request Flows** with detailed diagrams and process steps
- **Goal Detection System** for automated compliance workflow routing
- **Security & Admin Features** including MFA and session management
- **RAG System Architecture** for knowledge-enhanced AI responses

---

## 📊 Database Tables Structure

### Core User Management Tables

#### 1. `demo_users` - User Accounts
**What it stores**: Verified user accounts and profiles
**Key fields**:
- User identity: `email`, `first_name`, `last_name`, `company`
- Professional info: `job_title`, `company_size`, `industry`
- System data: `email_verified`, `temporary_password`, `lead_score`
- Admin fields: `user_type`, `admin_role`, `admin_permissions`

**Used for**: Authentication, user profiles, lead scoring, admin access

#### 2. `demo_sessions` - User Sessions
**What it stores**: Active user sessions and interaction tracking
**Key fields**:
- Session tracking: `user_id`, `user_email`, `status`
- Goal detection: `detected_goal` (iso_certification, gdpr_audit, etc.)
- Progress tracking: `document_count`, `interaction_count`, `contact_captured`
- Data collection: `intake_data` (JSONB with user responses)

**Used for**: Session management, goal tracking, progress monitoring

#### 3. `demo_verification_requests` - Email Verification
**What it stores**: Pending email verification requests
**Key fields**:
- Verification: `email`, `verification_token`, `expires_at`
- User data: `user_data` (JSONB with registration form data)
- Password: `temporary_password` (generated during registration)

**Used for**: Email verification workflow, temporary password management

### Interaction & Content Tables

#### 4. `demo_interactions` - Event Logging
**What it stores**: All user interactions and system events
**Key fields**:
- Tracking: `session_id`, `event_type`, `event_data` (JSONB)
- Events: `session_started`, `message_sent`, `documents_generated`, `lead_captured`

**Used for**: Analytics, conversation history, user behavior tracking

#### 5. `demo_documents` - Generated Documents
**What it stores**: AI-generated compliance documents
**Key fields**:
- Document info: `doc_type`, `title`, `content`, `description`
- Metadata: `session_id`, `icon`, `metadata` (JSONB)
- Types: `risk_register`, `soa`, `policy`, `ropa`, `dpia`, etc.

**Used for**: Document storage, retrieval, user downloads

#### 6. `demo_contacts` - Lead Capture
**What it stores**: Contact form submissions and lead information
**Key fields**:
- Contact info: `name`, `email`, `company`, `phone`
- Context: `session_id`, `message`

**Used for**: Sales leads, contact management, CRM integration

### Admin & Security Tables

#### 7. `admin_verification_codes` - Admin MFA
**What it stores**: Multi-factor authentication codes for admin access
**Key fields**:
- Security: `code` (6-digit), `password_hash`, `expires_at`
- Usage: `is_used`, `used_at`

**Used for**: Admin two-factor authentication

#### 8. `admin_sessions` - Admin Sessions
**What it stores**: Active admin session tokens
**Key fields**:
- Session: `token` (UUID), `is_active`, `expires_at`

**Used for**: Secure admin session management

### Knowledge Base Tables (Referenced)

#### 9. `faq_items` - FAQ Content
**What it stores**: FAQ content for RAG system
**Used for**: Answering common user questions automatically

#### 10. `user_queries_log` - Query Analytics
**What it stores**: User queries and AI responses for improvement
**Used for**: System optimization, response quality tracking

---

## 🔧 API Action Handlers

### Authentication & User Management

#### `authenticate-user`
**Purpose**: User login with email and temporary password
**Process**:
1. Check if user is admin (special handling)
2. Validate credentials against `demo_users` table
3. Create or retrieve session from `demo_sessions`
4. Return session ID and user info

**Returns**: Session ID, user type, demo URL

#### `request-demo-access`
**Purpose**: New user registration
**Process**:
1. Generate verification token and temporary password
2. Store request in `demo_verification_requests`
3. Send verification email via `EmailService`
4. Calculate initial lead score

**Returns**: Success status, verification info

#### `verify-email`
**Purpose**: Complete email verification
**Process**:
1. Validate verification token
2. Call `complete_email_verification` RPC function
3. Create user in `demo_users` table
4. Create demo session in `demo_sessions`

**Returns**: Session ID, redirect URL

### Core Demo Functionality

#### `init-session`
**Purpose**: Initialize new demo session
**Process**:
1. Create session in `demo_sessions` via `SessionManager`
2. Log session start via `InteractionLogger`

**Returns**: Session ID

#### `query-agent`
**Purpose**: Main AI chat interface
**Process**:
1. Get session and user context
2. Detect compliance goal from message
3. Send to Claude API via `LLMService`
4. Analyze response for suggested actions
5. Update session progress
6. Log interaction

**Returns**: AI response, quick replies, detected goals, suggested actions

#### `generate-documents`
**Purpose**: Create compliance documents
**Process**:
1. Use `DocumentGenerator` to create documents
2. Store in `demo_documents` table
3. Update session status to 'documents_generated'
4. Log document generation event

**Returns**: List of generated documents with metadata

#### `intelligent-document-generation`
**Purpose**: Generate documents from conversation context
**Process**:
1. Extract intake data from conversation history using Claude
2. Generate documents based on detected goal
3. Update session status

**Returns**: Documents list, extracted intake data

### Lead Management

#### `save-contact`
**Purpose**: Capture lead contact information
**Process**:
1. Store contact in `demo_contacts` table
2. Update session status to 'lead_captured'
3. Log lead capture event

**Returns**: Contact ID, success message

### Admin Functions

#### `admin-request-code`
**Purpose**: Request admin MFA code
**Process**:
1. Validate admin password
2. Generate 6-digit code
3. Store in `admin_verification_codes`
4. Send via `EmailService`

**Returns**: Success status, code ID

#### `admin-verify-code`
**Purpose**: Verify admin MFA code
**Process**:
1. Validate code against database
2. Mark code as used
3. Create admin session token
4. Store in `admin_sessions`

**Returns**: Admin session token

#### `get-leads`
**Purpose**: Retrieve all leads for admin dashboard
**Process**:
1. Query `demo_contacts`, `demo_users`, `demo_verification_requests`
2. Combine and format lead data
3. Calculate lead qualification scores

**Returns**: Lead list with scores and summary statistics

#### `admin-manage-user`
**Purpose**: Admin user management actions
**Process**:
1. Verify admin session token
2. Perform action (activate, suspend, delete)
3. Update appropriate tables

**Returns**: Action confirmation

### RAG (Retrieval-Augmented Generation) System

#### `query-rag`
**Purpose**: Smart query processing with knowledge retrieval
**Process**:
1. Classify query intent via `RAGService`
2. Generate embeddings via OpenAI API
3. Search knowledge base and FAQs using vector similarity
4. Generate controlled response using retrieved content
5. Log query for improvement

**Returns**: AI response with knowledge context, quick replies

---

## 🏗️ Core Services & Classes

### SessionManager
**Purpose**: Centralized session management
**Methods**:
- `createSession(metadata)`: Creates new sessions with metadata
- `updateSession(sessionId, updates)`: Updates session progress and status
- `getSession(sessionId)`: Retrieves session data

**Usage**: All session-related operations across the platform

### InteractionLogger  
**Purpose**: Event tracking and analytics
**Methods**:
- `logInteraction(sessionId, eventType, eventData)`: Logs all user events

**Usage**: Tracks user behavior, conversation history, system events

### DocumentGenerator
**Purpose**: AI-powered document creation
**Methods**:
- `generateDocument(docType, intakeData, goalType)`: Creates compliance documents
- `generateContent(docType, companyName, industry, date, intakeData)`: Generates document content

**Document Types**: risk_register, soa, policy, ropa, dpia, assessment, implementation_plan

### LLMService (Claude Integration)
**Purpose**: AI conversation and intelligence
**Methods**:
- `queryAssistantLive(sessionId, message, context)`: Live Claude API integration
- `buildDynamicSystemPrompt(context, conversationHistory)`: Context-aware prompts
- `determineConversationStage(conversationHistory, context)`: Conversation flow management

**Conversation Stages**: initial, goal_detection, intake_process, document_generation, document_review, lead_capture

### RAGService (Retrieval-Augmented Generation)
**Purpose**: Knowledge-enhanced AI responses
**Methods**:
- `generateEmbedding(text)`: Creates vector embeddings
- `searchKnowledgeBase(query, category, limit)`: Vector similarity search
- `classifyQuery(query)`: Intent classification
- `generateControlledResponse()`: Knowledge-enhanced responses

**Categories**: platform_features, iso_27001, gdpr_compliance, ai_governance

### EmailService
**Purpose**: Email communications
**Methods**:
- `sendVerificationEmail()`: Email verification workflow
- `sendAdminNotification()`: Lead alerts to sales team  
- `sendAdminMFACode()`: Admin authentication codes

**Powered by**: Resend API

---

## 🔄 Key Request Flows

### 1. New User Registration Flow
```
User Registration → request-demo-access
    ↓
Generate token + temp password
    ↓
Store in demo_verification_requests
    ↓
Send verification email
    ↓
User clicks link → verify-email
    ↓
Create user in demo_users
    ↓
Create session in demo_sessions
    ↓
Redirect to demo platform
```

### 2. Demo Session Flow
```
Session Start → init-session
    ↓
User Message → query-agent
    ↓
Goal Detection (ISO 27001, GDPR, etc.)
    ↓
AI Response via Claude API
    ↓
Progress Tracking → SessionManager.updateSession
    ↓
Document Generation → generate-documents
    ↓
Lead Capture → save-contact
```

### 3. Document Generation Flow
```
Conversation Analysis
    ↓
Extract intake data via Claude
    ↓
DocumentGenerator.generateDocument()
    ↓
Store in demo_documents
    ↓
Update session status
    ↓
Present to user
```

### 4. Admin Authentication Flow
```
Admin Login → admin-request-code
    ↓
Generate MFA code
    ↓
Store in admin_verification_codes
    ↓
Send via EmailService
    ↓
Code Verification → admin-verify-code
    ↓
Create admin session token
    ↓
Access admin dashboard
```

### 5. RAG Query Flow
```
User Query → query-rag
    ↓
Query Classification → RAGService
    ↓
Generate Embeddings → OpenAI API
    ↓
Vector Search → search_knowledge_base RPC
    ↓
Retrieve FAQs → search_faqs RPC  
    ↓
Generate Response → Claude API
    ↓
Return enhanced answer
```

---

## 🎯 Goal Detection System

### Available Goals
- `iso_certification`: ISO 27001 implementation
- `gdpr_audit`: GDPR compliance assessment  
- `ai_risk_management`: AI governance frameworks
- `soa_generation`: Statement of Applicability creation
- `capa_flow`: Corrective and Preventive Actions

### Detection Process
1. Message analysis via regex patterns
2. Goal storage in `demo_sessions.detected_goal`
3. Context-specific questions and document types
4. Progress tracking through conversation stages

---

## 🔒 Security Features

### Multi-Factor Authentication
- Admin password + 6-digit email code
- Time-based expiration (5 minutes)
- Single-use codes

### Session Management
- UUID-based session tokens
- Time-based expiration
- Session validation on protected endpoints

### Data Protection
- CORS headers for browser security
- Input validation and sanitization
- SQL injection protection via parameterized queries

---

## 📈 Analytics & Monitoring

### Event Tracking
- All user interactions logged via `InteractionLogger`
- Conversation history stored for Claude context
- Document generation metrics
- Lead qualification scoring

### Performance Monitoring  
- Query response times tracked
- RAG system effectiveness measured
- User engagement metrics
- Conversion funnel analysis

---

## 🛠️ Environment Configuration

### Required Environment Variables
- `SUPABASE_URL`: Database connection
- `SUPABASE_SERVICE_ROLE_KEY`: Database access
- `ANTHROPIC_API_KEY`: Claude AI integration
- `OPENAI_API_KEY`: Embeddings for RAG
- `RESEND_API_KEY`: Email service
- `FROM_EMAIL`: Sender email address
- `SITE_URL`: Application base URL
- `ADMIN_PASSWORD`: Admin access password

### Admin Configuration
Hard-coded admin users with roles and permissions:
- Super Admin: Full platform access
- Technical Admin: Limited admin functions

---

This architecture provides a comprehensive compliance automation platform with AI-powered conversations, document generation, lead management, and administrative oversight. The modular design allows for easy extension and maintenance while maintaining security and performance.
