/**
 * ArionComply Demo Enhancements
 * Version: 1.0
 * Purpose: External enhancements for demo.html without modifying core file
 */

class DemoEnhancements {
  constructor() {
    this.userInfo = null;
    this.feedbackData = [];
    this.init();
  }

  init() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.initialize());
    } else {
      this.initialize();
    }
  }

  initialize() {
    console.log('🚀 Demo Enhancements loading...');
    
    // Initialize all enhancements
    this.setupPersonalizedGreeting();
    this.setupFeedbackSystem();
    this.enhanceLimitationHandling();
    this.setupKeyboardShortcuts();
    
    console.log('✅ Demo Enhancements loaded successfully');
  }

  // =============================================================================
  // PERSONALIZED GREETING
  // =============================================================================
  
  async setupPersonalizedGreeting() {
    const greetingDiv = document.getElementById('userGreeting');
    if (!greetingDiv) return;

    // Show loading briefly while we fetch user data
    greetingDiv.innerHTML = '<span style="opacity: 0.6; font-size: 0.8rem;">Loading...</span>';

    // Get user info using the working API
    this.userInfo = await this.getUserInfo();
    
    if (this.userInfo) {
      const greeting = this.generatePersonalizedGreeting();
      greetingDiv.innerHTML = greeting;
      
      // Add subtle animation
      greetingDiv.style.opacity = '0';
      greetingDiv.style.transform = 'translateY(-10px)';
      greetingDiv.style.transition = 'all 0.5s ease';
      
      setTimeout(() => {
        greetingDiv.style.opacity = '1';
        greetingDiv.style.transform = 'translateY(0)';
      }, 300);
    } else {
      greetingDiv.innerHTML = '';
    }
  }

  async getUserInfo() {
    // Check for admin info first
    try {
      const adminInfo = localStorage.getItem('arion-admin-info');
      if (adminInfo) {
        const parsed = JSON.parse(adminInfo);
        return {
          name: parsed.email.split('@')[0],
          email: parsed.email,
          role: parsed.role,
          isAdmin: true
        };
      }
    } catch (error) {
      console.warn('Could not parse admin info:', error);
    }

    // Check URL parameters for session info
    const urlParams = new URLSearchParams(window.location.search);
    const sessionId = urlParams.get('session');
    
    if (sessionId) {
      try {
        // Use the working get-user-profile endpoint
        const response = await ArionUtils.api.call('get-user-profile', {}, sessionId);
        
        if (response && response.first_name) {
          return {
            name: response.first_name,
            email: response.email,
            sessionId: sessionId,
            isAdmin: false
          };
        }
        
      } catch (error) {
        console.warn('⚠️ get-user-profile failed:', error);
      }
      
      return {
        name: 'Friend',
        sessionId: sessionId,
        isAdmin: false
      };
    }

    return null;
  }

  generatePersonalizedGreeting() {
    if (!this.userInfo) return '';

    const currentHour = new Date().getHours();
    let timeGreeting = 'Hello';
    
    if (currentHour < 12) timeGreeting = 'Good morning';
    else if (currentHour < 17) timeGreeting = 'Good afternoon';
    else timeGreeting = 'Good evening';

    const name = this.capitalizeFirst(this.userInfo.name);
    
    if (this.userInfo.isAdmin) {
      return `
        <div style="display: flex; align-items: center; gap: 0.5rem;">
          <span>👑 ${timeGreeting}, ${name}</span>
          <span style="font-size: 0.7rem; background: rgba(5, 150, 105, 0.1); color: #059669; padding: 0.2rem 0.5rem; border-radius: 10px;">Admin</span>
        </div>
      `;
    } else {
      return `<span>👋 ${timeGreeting}, ${name}!</span>`;
    }
  }

  capitalizeFirst(str) {
    if (!str) return '';
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  }

  // =============================================================================
  // FEEDBACK SYSTEM
  // =============================================================================
  
  setupFeedbackSystem() {
    this.createFeedbackButton();
    this.setupMessageFeedback();
  }

  createFeedbackButton() {
    // Create floating feedback button
    const feedbackBtn = document.createElement('button');
    feedbackBtn.innerHTML = '💬 Feedback';
    feedbackBtn.className = 'floating-feedback-btn';
    feedbackBtn.onclick = () => this.showFeedbackModal();
    
    // Add styles
    const style = document.createElement('style');
    style.textContent = `
      .floating-feedback-btn {
        position: fixed;
        bottom: 2rem;
        left: 2rem;
        background: #059669;
        color: white;
        border: none;
        padding: 0.75rem 1rem;
        border-radius: 25px;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 600;
        box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
        transition: all 0.3s ease;
        z-index: 1000;
      }
      
      .floating-feedback-btn:hover {
        background: #047857;
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(5, 150, 105, 0.4);
      }
      
      .feedback-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        padding: 1rem;
      }
      
      .feedback-content {
        background: white;
        padding: 2.5rem;
        border-radius: 16px;
        max-width: 600px;
        width: 100%;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
      }
      
      .feedback-header {
        margin-bottom: 1.5rem;
      }
      
      .feedback-header h3 {
        color: #059669;
        margin-bottom: 0.5rem;
      }
      
      .feedback-form {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
      }
      
      .feedback-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }
      
      .feedback-form label {
        font-weight: 600;
        color: #374151;
        font-size: 0.95rem;
      }
      
      .feedback-form input,
      .feedback-form textarea,
      .feedback-form select {
        padding: 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        font-family: inherit;
        font-size: 0.95rem;
        transition: border-color 0.2s ease;
        background: white;
      }
      
      .feedback-form input:focus,
      .feedback-form textarea:focus,
      .feedback-form select:focus {
        outline: none;
        border-color: #059669;
        box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
      }
      
      .feedback-form textarea {
        min-height: 120px;
        resize: vertical;
        line-height: 1.5;
      }
      
      .feedback-form select {
        cursor: pointer;
        appearance: none;
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 1rem center;
        background-size: 1rem;
        padding-right: 3rem;
      }
      
      .feedback-buttons {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e2e8f0;
      }
      
      .btn-feedback {
        padding: 0.875rem 2rem;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 600;
        font-size: 0.95rem;
        transition: all 0.2s ease;
        min-width: 120px;
      }
      
      .btn-primary {
        background: #059669;
        color: white;
      }
      
      .btn-primary:hover {
        background: #047857;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
      }
      
      .btn-secondary {
        background: #f8fafc;
        color: #64748b;
        border: 1px solid #e2e8f0;
      }
      
      .btn-secondary:hover {
        background: #f1f5f9;
        color: #475569;
      }
      
      @media (max-width: 768px) {
        .floating-feedback-btn {
          bottom: 1rem;
          left: 1rem;
          padding: 0.5rem 0.75rem;
          font-size: 0.8rem;
        }
      }
    `;
    
    document.head.appendChild(style);
    document.body.appendChild(feedbackBtn);
  }

  showFeedbackModal() {
    const modal = document.createElement('div');
    modal.className = 'feedback-modal';
    modal.innerHTML = `
      <div class="feedback-content">
        <div class="feedback-header">
          <h3>💬 Send Feedback</h3>
          <p>Help us improve your ArionComply experience</p>
        </div>
        <form class="feedback-form" id="feedbackForm">
          <div class="feedback-group">
            <label for="feedbackType">Type of Feedback</label>
            <select id="feedbackType" name="feedbackType" required>
              <option value="">Select type...</option>
              <option value="bug">🐛 Bug Report</option>
              <option value="feature">💡 Feature Request</option>
              <option value="improvement">⚡ Improvement Suggestion</option>
              <option value="general">💭 General Feedback</option>
            </select>
          </div>
          
          <div class="feedback-group">
            <label for="feedbackSubject">Subject</label>
            <input type="text" id="feedbackSubject" name="feedbackSubject" placeholder="Brief description..." required>
          </div>
          
          <div class="feedback-group">
            <label for="feedbackMessage">Message</label>
            <textarea id="feedbackMessage" name="feedbackMessage" placeholder="Please provide details about your feedback. Be as specific as possible to help us improve..." required></textarea>
          </div>
          
          <div class="feedback-group">
            <label for="feedbackRating">Overall Experience (1-5)</label>
            <select id="feedbackRating" name="feedbackRating">
              <option value="">How would you rate your experience?</option>
              <option value="5">⭐⭐⭐⭐⭐ Excellent (5/5)</option>
              <option value="4">⭐⭐⭐⭐ Good (4/5)</option>
              <option value="3">⭐⭐⭐ Average (3/5)</option>
              <option value="2">⭐⭐ Poor (2/5)</option>
              <option value="1">⭐ Very Poor (1/5)</option>
            </select>
          </div>
        </form>
        
        <div class="feedback-buttons">
          <button type="button" class="btn-feedback btn-secondary" onclick="this.closest('.feedback-modal').remove()">Cancel</button>
          <button type="button" class="btn-feedback btn-primary" onclick="demoEnhancements.submitFeedback()">Send Feedback</button>
        </div>
      </div>
    `;
    
    // Close on backdrop click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });
    
    document.body.appendChild(modal);
    
    // Focus first input
    setTimeout(() => {
      modal.querySelector('#feedbackType').focus();
    }, 100);
  }

  async submitFeedback() {
    const form = document.getElementById('feedbackForm');
    const formData = new FormData(form);
    
    const feedback = {
      type: formData.get('feedbackType'),
      subject: formData.get('feedbackSubject'),
      message: formData.get('feedbackMessage'),
      rating: formData.get('feedbackRating'),
      timestamp: new Date().toISOString(),
      sessionId: sessionId || null,
      userAgent: navigator.userAgent,
      url: window.location.href
    };
    
    // Basic validation
    if (!feedback.type || !feedback.subject || !feedback.message) {
      alert('Please fill in all required fields');
      return;
    }
    
    try {
      // Store locally for now (you can enhance this to send to server)
      this.feedbackData.push(feedback);
      localStorage.setItem('arion-feedback', JSON.stringify(this.feedbackData));
      
      // TODO: Send to server
      // await ArionUtils.api.call('submit-feedback', feedback, sessionId);
      
      // Success message
      const modal = document.querySelector('.feedback-modal');
      modal.innerHTML = `
        <div class="feedback-content" style="text-align: center;">
          <div style="font-size: 3rem; margin-bottom: 1rem;">✅</div>
          <h3 style="color: #059669;">Thank You!</h3>
          <p>Your feedback has been submitted successfully.</p>
          <button class="btn-feedback btn-primary" onclick="this.closest('.feedback-modal').remove()">Close</button>
        </div>
      `;
      
      // Auto close after 3 seconds
      setTimeout(() => {
        modal.remove();
      }, 3000);
      
    } catch (error) {
      console.error('Failed to submit feedback:', error);
      alert('Failed to submit feedback. Please try again.');
    }
  }

  setupMessageFeedback() {
    // Add thumbs up/down to assistant messages
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE && node.classList && 
              node.classList.contains('message') && node.classList.contains('assistant')) {
            this.addMessageFeedback(node);
          }
        });
      });
    });
    
    const messagesDiv = document.getElementById('messages');
    if (messagesDiv) {
      observer.observe(messagesDiv, { childList: true });
    }
  }

  addMessageFeedback(messageElement) {
    // Don't add feedback to messages that already have it
    if (messageElement.querySelector('.message-feedback')) return;
    
    const feedbackDiv = document.createElement('div');
    feedbackDiv.className = 'message-feedback';
    feedbackDiv.innerHTML = `
      <div style="display: flex; gap: 0.5rem; margin-top: 0.5rem; opacity: 0.6;">
        <button class="feedback-btn" data-feedback="helpful" title="Helpful">👍</button>
        <button class="feedback-btn" data-feedback="not-helpful" title="Not helpful">👎</button>
      </div>
    `;
    
    // Add styles for feedback buttons
    if (!document.querySelector('#message-feedback-styles')) {
      const style = document.createElement('style');
      style.id = 'message-feedback-styles';
      style.textContent = `
        .feedback-btn {
          background: none;
          border: none;
          font-size: 1rem;
          cursor: pointer;
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          transition: all 0.2s;
        }
        
        .feedback-btn:hover {
          background: #f1f5f9;
          transform: scale(1.1);
        }
        
        .feedback-btn.selected {
          background: #ecfdf5;
          color: #059669;
        }
      `;
      document.head.appendChild(style);
    }
    
    // Add click handlers
    feedbackDiv.addEventListener('click', (e) => {
      if (e.target.classList.contains('feedback-btn')) {
        // Remove selection from other buttons
        feedbackDiv.querySelectorAll('.feedback-btn').forEach(btn => 
          btn.classList.remove('selected'));
        
        // Select clicked button
        e.target.classList.add('selected');
        
        // Log feedback
        const messageText = messageElement.querySelector('.message-bubble').textContent;
        const feedback = {
          message: messageText.substring(0, 100) + '...',
          feedback: e.target.dataset.feedback,
          timestamp: new Date().toISOString()
        };
        
        console.log('Message feedback:', feedback);
        // You can send this to your analytics or feedback system
      }
    });
    
    messageElement.appendChild(feedbackDiv);
  }

  // =============================================================================
  // ENHANCED LIMITATION HANDLING
  // =============================================================================
  
  enhanceLimitationHandling() {
    // Override the global closeLimitOverlay function
    window.originalCloseLimitOverlay = window.closeLimitOverlay;
    window.closeLimitOverlay = this.enhancedCloseLimitOverlay.bind(this);
  }

  enhancedCloseLimitOverlay() {
    const limitOverlay = document.getElementById('limitOverlay');
    const limitType = window.limitType; // Access global limitType
    
    if (limitType === 'session') {
      // For session limits, redirect to main page instead of resetting
      console.log('🔄 Session limit reached - redirecting to main page');
      
      // Show a brief message before redirect
      const limitCard = document.querySelector('.limit-reached-card');
      if (limitCard) {
        limitCard.innerHTML = `
          <div class="limit-icon">👋</div>
          <h3 class="limit-title">Thanks for trying ArionComply!</h3>
          <div class="limit-message">
            <p>You've completed your demo session. Ready to get full access?</p>
          </div>
          <div>
            <button class="upgrade-btn" onclick="window.location.href='index.html'">Get Full Access</button>
          </div>
        `;
      }
      
      // Redirect after 3 seconds
      setTimeout(() => {
        window.location.href = 'index.html';
      }, 3000);
      
    } else {
      // For daily/monthly limits, also redirect to main page
      console.log(`🔄 ${limitType} limit reached - redirecting to main page`);
      window.location.href = 'index.html';
    }
  }

  // =============================================================================
  // KEYBOARD SHORTCUTS
  // =============================================================================
  
  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Ctrl/Cmd + K to focus message input
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const messageInput = document.getElementById('messageInput');
        if (messageInput) {
          messageInput.focus();
        }
      }
      
      // Ctrl/Cmd + / to show feedback modal
      if ((e.ctrlKey || e.metaKey) && e.key === '/') {
        e.preventDefault();
        this.showFeedbackModal();
      }
      
      // Escape to close any open modals
      if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.feedback-modal');
        modals.forEach(modal => modal.remove());
      }
    });
  }

  // =============================================================================
  // UTILITY METHODS
  // =============================================================================
  
  // Method to get all feedback data
  getFeedbackData() {
    try {
      const stored = localStorage.getItem('arion-feedback');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to get feedback data:', error);
      return [];
    }
  }

  // Method to export feedback data
  exportFeedbackData() {
    const data = this.getFeedbackData();
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `arion-feedback-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }

  // Method to clear all feedback data
  clearFeedbackData() {
    if (confirm('Are you sure you want to clear all feedback data?')) {
      localStorage.removeItem('arion-feedback');
      this.feedbackData = [];
      console.log('✅ Feedback data cleared');
    }
  }
}

// Initialize the enhancements when script loads
const demoEnhancements = new DemoEnhancements();

// Make it globally available for console debugging
window.demoEnhancements = demoEnhancements;

// Debug methods for testing
window.testFeedback = () => demoEnhancements.showFeedbackModal();
window.exportFeedback = () => demoEnhancements.exportFeedbackData();
window.clearFeedback = () => demoEnhancements.clearFeedbackData();

console.log('🎯 Demo Enhancements ready! Try: testFeedback(), exportFeedback(), clearFeedback()');
