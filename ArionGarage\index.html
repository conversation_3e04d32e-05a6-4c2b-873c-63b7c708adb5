<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-LLM Chat Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .chat-container {
            width: 95%;
            max-width: 1200px;
            height: 85vh;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            padding: 25px;
            text-align: center;
            color: white;
        }

        .chat-header h1 {
            font-size: 2.2rem;
            margin-bottom: 20px;
            font-weight: 700;
        }

        .model-selector {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 10px;
        }

        .model-btn {
            padding: 15px 30px;
            border: 3px solid rgba(255, 255, 255, 0.4);
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1.1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .model-btn:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .model-btn.active {
            background: rgba(255, 255, 255, 0.35);
            border-color: white;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            animation: slideIn 0.3s ease;
        }

        .message.user {
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 0.9rem;
        }

        .user .message-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            line-height: 1.5;
            word-wrap: break-word;
        }

        .user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .assistant .message-content {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #e9ecef;
            border-bottom-left-radius: 5px;
        }

        .chat-input-area {
            padding: 20px;
            border-top: 1px solid #e9ecef;
            background: #f8f9fa;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 1rem;
            resize: none;
            outline: none;
            transition: all 0.3s ease;
            max-height: 120px;
            min-height: 50px;
        }

        .chat-input:focus {
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .send-btn {
            padding: 15px 25px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .send-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px var(--hover-shadow, rgba(79, 172, 254, 0.4));
        }

        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .typing-indicator {
            display: none;
            align-items: center;
            gap: 10px;
            color: #666;
            font-style: italic;
            padding: 10px 0;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #4facfe;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes typing {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .error-message {
            background: #ff6b6b !important;
            color: white !important;
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 98%;
                height: 90vh;
            }

            .message-content {
                max-width: 85%;
            }

            .model-selector {
                gap: 8px;
            }

            .model-btn {
                padding: 12px 20px;
                font-size: 0.95rem;
            }

            .chat-header h1 {
                font-size: 1.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>Multi-LLM Chat Interface</h1>
            <div class="model-selector">
                <button class="model-btn active" data-model="smollm3">SmolLM3</button>
                <button class="model-btn" data-model="mistral">Mistral</button>
            </div>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-avatar" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">SM</div>
                <div class="message-content">
                    Hello! I'm ready to help you with SmolLM3 and Mistral models. Select a model above and start chatting!
                </div>
            </div>
        </div>

        <div class="typing-indicator" id="typingIndicator">
            <div class="message-avatar">AI</div>
            <span>AI is thinking</span>
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        </div>

        <div class="chat-input-area">
            <div class="input-container">
                <textarea
                    id="chatInput"
                    class="chat-input"
                    placeholder="Type your message here..."
                    rows="1"
                ></textarea>
                <button id="sendBtn" class="send-btn">Send</button>
            </div>
        </div>
    </div>

    <script>
        class MultiLLMChat {
            constructor() {
                this.currentModel = 'smollm3';
                this.isLoading = false;
                this.supabaseUrl = 'https://dxncozbhwppvwpugoqjk.supabase.co'; // Replace with your Supabase URL
                this.supabaseFunctionUrl = `${this.supabaseUrl}/functions/v1/edgetogarage`;

                this.initializeElements();
                this.attachEventListeners();
            }

            initializeElements() {
                this.chatMessages = document.getElementById('chatMessages');
                this.chatInput = document.getElementById('chatInput');
                this.sendBtn = document.getElementById('sendBtn');
                this.typingIndicator = document.getElementById('typingIndicator');
                this.modelBtns = document.querySelectorAll('.model-btn');
            }

            attachEventListeners() {
                // Model selection
                this.modelBtns.forEach(btn => {
                    btn.addEventListener('click', () => {
                        this.selectModel(btn.dataset.model);
                        this.updateActiveModel(btn);
                    });
                });

                // Send button
                this.sendBtn.addEventListener('click', () => this.sendMessage());

                // Enter key to send (Shift+Enter for new line)
                this.chatInput.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });

                // Auto-resize textarea
                this.chatInput.addEventListener('input', () => {
                    this.chatInput.style.height = 'auto';
                    this.chatInput.style.height = this.chatInput.scrollHeight + 'px';
                });
            }

            selectModel(model) {
                this.currentModel = model;
                this.updateTheme(); // Update theme colors when switching models
                this.addSystemMessage(`Switched to ${this.getModelFullName()}`);
            }

            updateActiveModel(activeBtn) {
                this.modelBtns.forEach(btn => btn.classList.remove('active'));
                activeBtn.classList.add('active');
            }

            async sendMessage() {
                const message = this.chatInput.value.trim();
                if (!message || this.isLoading) return;

                // Add user message to chat
                this.addMessage(message, 'user');
                this.chatInput.value = '';
                this.chatInput.style.height = 'auto';

                // Show loading state
                this.setLoading(true);

                // Add empty AI message for progressive display
                const aiMessageContent = this.addMessage('', 'assistant');

                try {
                    await this.callLLMAPIWithStreaming(message, aiMessageContent);
                } catch (error) {
                    console.error('Error:', error);
                    aiMessageContent.innerHTML = 'Sorry, there was an error processing your request. Please try again.';
                    aiMessageContent.classList.add('error-message');
                } finally {
                    this.setLoading(false);
                }
            }

            async callLLMAPIWithStreaming(message, messageElement) {
                const response = await fetch(this.supabaseFunctionUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR4bmNvemJod3BwdndwdWdvcWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMxOTgxMDQsImV4cCI6MjA2ODc3NDEwNH0.zx_wwG5nnNyxUwpUqJldQfNzSSfxgud4C4x0Bvx-r90`, // Replace with your anon key
                    },
                    body: JSON.stringify({
                        model: this.currentModel,
                        message: message,
                        conversation_id: this.getConversationId()
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.error) {
                    throw new Error(data.error);
                }

                // Simulate progressive display of the response
                await this.typewriterEffect(data.response, messageElement);

                return data.response;
            }

            async typewriterEffect(text, element) {
                element.innerHTML = '';
                const words = text.split(' ');
                let currentText = '';

                for (let i = 0; i < words.length; i++) {
                    currentText += words[i] + ' ';

                    // Convert newlines to HTML breaks
                    const formattedText = currentText.replace(/\n/g, '<br>');
                    element.innerHTML = formattedText;

                    this.scrollToBottom();

                    // Adjust speed based on word length and content
                    let delay = 50; // Base delay

                    // Slower for punctuation
                    if (words[i].includes('.') || words[i].includes('!') || words[i].includes('?')) {
                        delay = 150;
                    }
                    // Faster for short words
                    else if (words[i].length < 3) {
                        delay = 30;
                    }

                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }

            addMessage(content, sender, isError = false, modelName = null) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;

                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';

                if (sender === 'user') {
                    avatar.textContent = 'You';
                    avatar.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                } else {
                    // Use model name and color for AI messages
                    avatar.textContent = modelName || this.getModelDisplayName();
                    avatar.style.background = this.getModelColor();
                }

                const messageContent = document.createElement('div');
                messageContent.className = `message-content ${isError ? 'error-message' : ''}`;

                // Convert newlines to HTML line breaks for proper display
                const formattedContent = content.replace(/\n/g, '<br>');
                messageContent.innerHTML = formattedContent;

                messageDiv.appendChild(avatar);
                messageDiv.appendChild(messageContent);

                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();

                return messageContent; // Return content element for progressive updates
            }

            getModelDisplayName() {
                const modelNames = {
                    'smollm3': 'SM',
                    'mistral': 'MI'
                };
                return modelNames[this.currentModel] || 'AI';
            }

            getModelFullName() {
                const modelNames = {
                    'smollm3': 'SmolLM3',
                    'mistral': 'Mistral'
                };
                return modelNames[this.currentModel] || 'AI';
            }

            getModelColor() {
                const modelColors = {
                    'smollm3': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', // Blue gradient
                    'mistral': 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)'  // Red/Orange gradient
                };
                return modelColors[this.currentModel] || 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)';
            }

            getModelThemeColor() {
                const themeColors = {
                    'smollm3': {
                        header: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                        button: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                        buttonHover: 'rgba(79, 172, 254, 0.4)'
                    },
                    'mistral': {
                        header: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
                        button: 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)',
                        buttonHover: 'rgba(255, 107, 107, 0.4)'
                    }
                };
                return themeColors[this.currentModel] || themeColors['smollm3'];
            }

            updateTheme() {
                const theme = this.getModelThemeColor();

                // Update header background
                const header = document.querySelector('.chat-header');
                header.style.background = theme.header;

                // Update send button
                const sendBtn = document.getElementById('sendBtn');
                sendBtn.style.background = theme.button;
                sendBtn.setAttribute('data-hover-shadow', theme.buttonHover);
            }

            addSystemMessage(content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message assistant';
                messageDiv.style.opacity = '0.7';

                const avatar = document.createElement('div');
                avatar.className = 'message-avatar';
                avatar.textContent = '⚙️';
                avatar.style.background = 'linear-gradient(135deg, #95e1d3 0%, #fce38a 100%)'; // System message color

                const messageContent = document.createElement('div');
                messageContent.className = 'message-content';
                messageContent.style.fontStyle = 'italic';
                messageContent.textContent = content;

                messageDiv.appendChild(avatar);
                messageDiv.appendChild(messageContent);

                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();
            }

            setLoading(loading) {
                this.isLoading = loading;
                this.sendBtn.disabled = loading;
                this.chatInput.disabled = loading;

                if (loading) {
                    this.typingIndicator.style.display = 'flex';
                    this.sendBtn.textContent = 'Sending...';
                } else {
                    this.typingIndicator.style.display = 'none';
                    this.sendBtn.textContent = 'Send';
                }

                this.scrollToBottom();
            }

            scrollToBottom() {
                setTimeout(() => {
                    this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                }, 100);
            }

            getConversationId() {
                // Generate or retrieve conversation ID for context
                if (!this.conversationId) {
                    this.conversationId = 'conv_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                }
                return this.conversationId;
            }
        }

        // Initialize the chat when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            const chat = new MultiLLMChat();

            // Set initial theme
            chat.updateTheme();

            // Add CSS custom property support for dynamic hover effects
            const style = document.createElement('style');
            style.textContent = `
                .send-btn:hover:not(:disabled) {
                    box-shadow: 0 5px 15px var(--hover-shadow, rgba(79, 172, 254, 0.4));
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
