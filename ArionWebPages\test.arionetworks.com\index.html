<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arion Networks - Innovation • Performance • Partnership</title>
    <style>
        /* (Existing CSS remains unchanged, adding new styles below) */
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #334155;
            line-height: 1.7;
            overflow-x: hidden;
        }
        
        /* Animated background particles */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            opacity: 0.4;
        }
        
        .particle {
            position: absolute;
            background: linear-gradient(45deg, #1e40af, #3b82f6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.3; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 0.8; }
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }
        
        nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 100;
            transition: all 0.3s ease;
        }
        
        nav.scrolled {
            padding: 15px 0;
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 6px 30px rgba(0,0,0,0.12);
        }
        
        nav .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .nav-brand {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .logo-placeholder {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 900;
            font-size: 18px;
            letter-spacing: -1px;
            transition: all 0.3s ease;
        }
        
        .logo-placeholder:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(30, 64, 175, 0.3);
        }
        
        nav .logo {
            font-weight: 800;
            font-size: 1.4em;
            color: #1e293b;
            letter-spacing: -0.5px;
            transition: all 0.3s ease;
        }
        
        nav .logo:hover {
            transform: scale(1.05);
            color: #1e40af;
        }
        
        nav .tagline {
            color: #64748b;
            font-size: 0.8em;
            font-weight: 500;
            letter-spacing: 1px;
            margin-left: 16px;
            opacity: 1;
            transition: opacity 0.3s ease;
        }
        
        nav.scrolled .tagline {
            opacity: 0;
        }
        
        nav a {
            text-decoration: none;
            color: #475569;
            margin: 0 24px;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
        }
        
        nav a:hover {
            color: #1e40af;
            transform: translateY(-1px);
        }
        
        nav a::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background: #1e40af;
            transition: width 0.3s ease;
        }
        
        nav a:hover::after {
            width: 100%;
        }
        
        .hero {
            text-align: center;
            margin: 120px 0 80px;
            padding: 60px 0;
            position: relative;
        }
        
        .hero-bg {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 150%;
            height: 150%;
            background: radial-gradient(circle, rgba(30, 64, 175, 0.05) 0%, transparent 70%);
            animation: pulse 4s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.3; }
            50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.1; }
        }
        
        .hero h1 {
            font-size: 2.8em;
            font-weight: 800;
            margin: 0 0 24px;
            color: #0f172a;
            letter-spacing: -1px;
            line-height: 1.2;
            animation: slideUp 1s ease-out;
            position: relative;
        }
        
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .hero .subtitle {
            font-size: 1.4em;
            color: #64748b;
            max-width: 900px;
            margin: 0 auto 40px;
            font-weight: 400;
            line-height: 1.6;
            animation: slideUp 1s ease-out 0.2s both;
        }
        
        .hero-scroll-indicator {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            margin-top: 50px;
            animation: slideUp 1s ease-out 0.4s both;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .hero-scroll-indicator:hover {
            transform: translateY(-2px);
        }
        
        .scroll-arrow {
            font-size: 2em;
            color: #1e40af;
            animation: bounce 2s ease-in-out infinite;
        }
        
        .hero-scroll-indicator span {
            font-size: 0.95em;
            color: #64748b;
            font-weight: 500;
            letter-spacing: 0.5px;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-8px); }
            60% { transform: translateY(-4px); }
        }
        
        .section {
            margin: 100px 0;
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }
        
        .section.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .section h2 {
            text-align: center;
            font-size: 2.8em;
            font-weight: 800;
            margin-bottom: 24px;
            color: #0f172a;
            letter-spacing: -1px;
            position: relative;
        }
        
        .section h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #1e40af, #3b82f6);
            border-radius: 2px;
        }
        
        .section .intro {
            text-align: center;
            font-size: 1.3em;
            color: #64748b;
            max-width: 900px;
            margin: 0 auto 60px;
            line-height: 1.6;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
            gap: 30px;
            margin: 60px 0;
        }
        
        .card {
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 4px 25px rgba(0,0,0,0.06);
            border: 1px solid rgba(226, 232, 240, 0.8);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            opacity: 0;
            transform: translateY(20px);
        }
        
        .card.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #1e40af, #3b82f6);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .card::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(30, 64, 175, 0.02) 50%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.12);
        }
        
        .card:hover::before {
            opacity: 1;
        }
        
        .card:hover::after {
            opacity: 1;
        }
        
        .card h3 {
            color: #1e293b;
            margin-bottom: 20px;
            font-size: 1.5em;
            font-weight: 700;
            letter-spacing: -0.5px;
            position: relative;
        }
        
        .card p {
            color: #64748b;
            text-align: left;
            line-height: 1.7;
            font-size: 1.05em;
        }
        
        .interactive-demo {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 60px 40px;
            border-radius: 20px;
            margin: 60px 0;
            position: relative;
            overflow: hidden;
        }
        
        .demo-terminal {
            background: #1e293b;
            border-radius: 12px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            color: #cbd5e1;
            position: relative;
            overflow: hidden;
            margin-bottom: 40px; /* Added margin-bottom */
        }
        
        .terminal-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #475569;
        }
        
        .terminal-dots {
            display: flex;
            gap: 8px;
        }
        
        .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        
        .dot.red { background: #ef4444; }
        .dot.yellow { background: #f59e0b; }
        .dot.green { background: #10b981; }
        
        .terminal-title {
            margin-left: 20px;
            color: #94a3b8;
            font-size: 0.9em;
        }
        
        .terminal-content {
            line-height: 1.6;
        }
        
        .typing-effect {
            border-right: 2px solid #3b82f6;
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { border-color: #3b82f6; }
            51%, 100% { border-color: transparent; }
        }
        
        .trust-section {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 80px 40px;
            border-radius: 24px;
            margin: 80px 0;
            text-align: center;
            position: relative;
            box-shadow: 0 8px 40px rgba(0,0,0,0.06);
        }
        
        .trust-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-top: 40px;
        }
        
        .trust-item {
            padding: 30px 20px;
            transition: transform 0.3s ease;
            background: rgba(255, 255, 255, 0.6);
            border-radius: 16px;
            backdrop-filter: blur(10px);
        }
        
        .trust-item:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.8);
        }
        
        .trust-item h4 {
            font-size: 1.3em;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 16px;
        }
        
        .trust-item p {
            color: #64748b;
            font-size: 1.05em;
            line-height: 1.6;
        }

        /* Technology Carousel Styles (Original, some might be overridden or repurposed) */
        .tech-carousel-container {
            margin-top: 60px;
        }

        .tech-category h3 { /* This will be for the tab content */
            color: #1e293b;
            font-size: 1.4em;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
        }

        .tech-carousel {
            position: relative;
            height: 160px; /* Increased height for text */
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            padding: 30px 20px;
            margin: 30px 0;
            background: transparent; /* Transparent background to blend with page */
        }

        .tech-carousel-wrapper {
            position: relative;
            width: 100%;
            max-width: 450px; /* Slightly wider for text */
            height: 120px; /* Increased height */
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .tech-card {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            padding: 20px 25px;
            font-weight: 600;
            color: #475569;
            white-space: normal; /* Allow text wrapping */
            box-shadow: 0 6px 25px rgba(0,0,0,0.12);
            opacity: 0;
            transition: all 1.2s ease-in-out;
            z-index: 1;
            min-width: 280px;
            max-width: 320px;
            text-align: center;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .tech-card-title {
            font-size: 1.3em;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .tech-card-description {
            font-size: 0.85em;
            font-weight: 400;
            color: #64748b;
            line-height: 1.4;
            opacity: 0.9;
        }

        .tech-card.active {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1.02);
            border-color: #3b82f6;
            z-index: 2;
        }

        .tech-card.active .tech-card-title {
            color: #3b82f6;
        }

        .tech-card.active .tech-card-description {
            color: #475569;
        }

        .tech-card.active {
            box-shadow: 0 8px 35px rgba(59, 130, 246, 0.2);
        }

        .tech-card.next {
            opacity: 0.15;
            transform: translate(30%, -50%) scale(0.85);
            z-index: 0;
        }

        .tech-card.prev {
            opacity: 0.15;
            transform: translate(-130%, -50%) scale(0.85);
            z-index: 0;
        }

        /* New styles for product cards within interactive-demo */
        .ai-product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .ai-product-card {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            border: 1px solid rgba(226, 232, 240, 0.6);
            transition: all 0.3s ease;
            text-align: center;
        }

        .ai-product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #3b82f6;
        }

        .ai-product-card h4 {
            font-size: 1.4em;
            color: #1e293b;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .ai-product-card p {
            font-size: 0.95em;
            color: #64748b;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .ai-product-card .btn-learn-more {
            display: inline-block;
            background: #3b82f6;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            transition: background 0.3s ease;
        }

        .ai-product-card .btn-learn-more:hover {
            background: #2563eb;
        }

        @media (max-width: 768px) {
            .ai-product-grid {
                grid-template-columns: 1fr;
            }
            .interactive-demo {
                padding: 40px 20px;
            }
        }
        
        footer {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: white;
            padding: 80px 0 40px;
            text-align: center;
            margin-top: 120px;
            position: relative;
            overflow: hidden;
        }
        
        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #1e40af, #3b82f6, #059669, #ea580c, #7c3aed, #1e40af);
            background-size: 200% 100%;
            animation: gradient-shift 3s ease-in-out infinite;
        }
        
        @keyframes gradient-shift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        footer h3 {
            font-size: 1.8em;
            font-weight: 800;
            margin-bottom: 12px;
            letter-spacing: -0.5px;
        }
        
        footer .tagline {
            color: #94a3b8;
            font-size: 0.9em;
            font-weight: 500;
            letter-spacing: 1px;
            margin-bottom: 24px;
        }
        
        footer p {
            color: #cbd5e1;
            margin: 0 auto 40px;
            max-width: 600px;
            line-height: 1.6;
        }
        
        footer hr {
            border: none;
            border-top: 1px solid #475569;
            margin: 40px auto;
            max-width: 200px;
        }
        
        footer a {
            color: #94a3b8;
            text-decoration: none;
            margin: 0 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        footer a:hover {
            color: white;
            transform: translateY(-1px);
        }
        
        /* Cookie Consent Banner */
        .cookie-consent {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(30, 41, 59, 0.98);
            backdrop-filter: blur(10px);
            color: white;
            padding: 20px;
            z-index: 1000;
            transform: translateY(100%);
            transition: transform 0.3s ease;
            border-top: 2px solid #3b82f6;
        }

        .cookie-consent.show {
            transform: translateY(0);
        }

        .cookie-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
        }

        .cookie-text h4 {
            margin: 0 0 8px 0;
            color: #3b82f6;
            font-size: 1.1em;
        }

        .cookie-text p {
            margin: 0;
            color: #cbd5e1;
            font-size: 0.95em;
            line-height: 1.4;
        }

        .cookie-actions {
            display: flex;
            gap: 12px;
            flex-shrink: 0;
        }

        .cookie-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .cookie-btn-primary {
            background: #3b82f6;
            color: white;
        }

        .cookie-btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .cookie-btn-secondary {
            background: transparent;
            color: #cbd5e1;
            border: 1px solid #475569;
        }

        .cookie-btn-secondary:hover {
            background: #475569;
            color: white;
        }

        /* Privacy Modal */
        .privacy-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1001;
        }

        .privacy-modal.show {
            display: flex;
        }

        .privacy-modal-content {
            background: white;
            padding: 40px;
            border-radius: 12px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .privacy-modal-content h3 {
            margin: 0 0 24px 0;
            color: #1e293b;
            font-size: 1.4em;
        }

        .privacy-options {
            margin-bottom: 30px;
        }

        .privacy-option {
            margin-bottom: 20px;
        }

        .privacy-option label {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            cursor: pointer;
            font-weight: 500;
            color: #1e293b;
        }

        .privacy-option input[type="checkbox"] {
            margin-top: 2px;
        }

        .privacy-description {
            display: block;
            font-weight: 400;
            color: #64748b;
            font-size: 0.9em;
            margin-top: 4px;
        }

        .privacy-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }
        
        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.2em;
            }
            
            nav .tagline {
                display: none;
            }
            
            .grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .cookie-content {
                flex-direction: column;
                text-align: center;
            }

            .cookie-actions {
                justify-content: center;
            }

            .privacy-modal-content {
                padding: 30px 20px;
            }
        }

        /* Loading spinner */
        .loading-spinner {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #f8fafc;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #e2e8f0;
            border-top: 4px solid #1e40af;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .hidden {
            display: none;
        }

        /* NEW STYLES FOR TABBED TECHNOLOGIES SECTION */
        .tech-tabs {
            display: flex;
            justify-content: center; /* Center the tabs */
            flex-wrap: wrap; /* Allow tabs to wrap on smaller screens */
            gap: 10px; /* Space between tabs */
            margin-bottom: 40px; /* Space between tabs and content */
            border-bottom: 2px solid #e2e8f0; /* Subtle separator */
            padding-bottom: 10px;
            position: relative;
            z-index: 2; /* Ensure tabs are above content in some cases */
        }

        .tech-tab-button {
            background: transparent;
            border: none;
            padding: 12px 20px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: 600;
            color: #64748b;
            transition: all 0.3s ease;
            position: relative;
            outline: none; /* Remove outline on focus */
        }

        .tech-tab-button:hover {
            color: #1e40af;
        }

        .tech-tab-button.active {
            color: #1e40af;
            animation: tabBounce 0.3s ease;
        }

        .tech-tab-button::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%) scaleX(0);
            width: 100%;
            height: 3px;
            background: #1e40af;
            transition: transform 0.4s ease;
        }

        .tech-tab-button.active::after {
            transform: translateX(-50%) scaleX(1);
        }

        @keyframes tabBounce {
            0%,100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .tech-tab-content {
            display: none; /* Hidden by default */
            opacity: 0;
            transform: translateY(20px);
            max-height: 0;
            overflow: hidden;
            transition:
                opacity 0.5s ease-in-out,
                transform 0.5s ease-in-out,
                max-height 0.5s ease-in-out;
            background: transparent; /* Transparent background for seamless blend */
            padding: 20px;
            border-radius: 12px;
            position: relative; /* For the gradients */
        }

        .tech-tab-content.active {
            display: block; /* Show active content */
            opacity: 1;
            transform: translateY(0);
            max-height: 300px; /* Increased to accommodate larger carousel */
        }

        /* Re-apply carousel gradients within the content - REMOVED since we changed to single card display */
        .tech-tab-content .tech-carousel {
            position: relative;
        }

        /* Adjustments for smaller screens */
        @media (max-width: 600px) {
            .tech-tabs {
                flex-direction: column; /* Stack tabs vertically */
                align-items: stretch; /* Make tabs full width */
                padding-bottom: 0;
            }
            .tech-tab-button {
                border-bottom: 1px solid #e2e8f0;
                padding: 15px 20px;
                text-align: center;
            }
            .tech-tab-button.active::after {
                bottom: -1px; /* Align active indicator */
                height: 2px;
            }
            .tech-tab-content {
                padding: 20px 10px;
                transition-duration: 0.3s;
            }
            .tech-carousel {
                height: 140px; /* Adjusted for mobile */
                padding: 25px 15px;
            }
            .tech-carousel-wrapper {
                max-width: 350px; /* Wider for descriptions */
                height: 110px; /* Taller for mobile */
            }
            .tech-card {
                padding: 16px 20px; /* Adjusted padding for mobile */
                min-width: 260px; /* Wider for descriptions */
                max-width: 300px;
            }
            .tech-card-title {
                font-size: 1.2em; /* Slightly smaller on mobile */
            }
            .tech-card-description {
                font-size: 0.8em; /* Smaller description text */
                line-height: 1.3;
            }
        }
    </style>
</head>
<body>
    <div class="loading-spinner" id="loading">
        <div class="spinner"></div>
    </div>

    <div class="cookie-consent" id="cookieConsent">
        <div class="cookie-content">
            <div class="cookie-text">
                <h4>🍪 Privacy & Cookies</h4>
                <p>We use cookies and similar technologies to enhance your experience, analyze site usage, and assist with our marketing efforts. By continuing to use this site, you consent to our use of cookies.</p>
            </div>
            <div class="cookie-actions">
                <button class="cookie-btn cookie-btn-secondary" onclick="showPrivacySettings()">Settings</button>
                <button class="cookie-btn cookie-btn-primary" onclick="acceptAllCookies()">Accept All</button>
            </div>
        </div>
    </div>

    <div class="privacy-modal" id="privacyModal">
        <div class="privacy-modal-content">
            <h3>Privacy Settings</h3>
            <div class="privacy-options">
                <div class="privacy-option">
                    <label>
                        <input type="checkbox" checked disabled> Essential Cookies
                        <span class="privacy-description">Required for website functionality</span>
                    </label>
                </div>
                <div class="privacy-option">
                    <label>
                        <input type="checkbox" id="analyticsConsent"> Analytics Cookies
                        <span class="privacy-description">Help us understand how visitors interact with our website</span>
                    </label>
                </div>
                <div class="privacy-option">
                    <label>
                        <input type="checkbox" id="marketingConsent"> Marketing Cookies
                        <span class="privacy-description">Used to deliver relevant advertisements</span>
                    </label>
                </div>
            </div>
            <div class="privacy-actions">
                <button class="cookie-btn cookie-btn-secondary" onclick="closePrivacySettings()">Cancel</button>
                <button class="cookie-btn cookie-btn-primary" onclick="savePrivacySettings()">Save Preferences</button>
            </div>
        </div>
    </div>

    <div class="bg-animation" id="bgAnimation"></div>

    <nav id="navbar">
        <div class="container">
            <div class="nav-brand">
                <div class="logo-placeholder">AN</div>
                <div>
                    <span class="logo">ARION NETWORKS</span>
                    <span class="tagline">INNOVATION • PERFORMANCE • PARTNERSHIP</span>
                </div>
            </div>
            <div>
                <a href="#services">Services</a>
                <a href="#training">Training</a>
                <a href="#contact">Contact</a>
            </div>
        </div>
    </nav>

    <div class="container">
        
        <div class="hero">
            <div class="hero-bg"></div>
            <h1>Technology Excellence, Delivered</h1>
            <p class="subtitle">Bridging legacy and cutting-edge technologies with sustainable, innovative solutions for 5G networks, AI platforms, and enterprise infrastructure.</p>
            <div class="hero-scroll-indicator">
                <div class="scroll-arrow">↓</div>
                <span>Discover Our Capabilities</span>
            </div>
        </div>

        <div class="section" id="what-we-do">
            <div style="text-align: center; margin-bottom: 50px; padding: 40px; background: rgba(30, 64, 175, 0.05); border-radius: 16px; border-left: 4px solid #1e40af;">
                <h3 style="color: #1e40af; margin-bottom: 16px; font-size: 1.2em;">Our Mission</h3>
                <p style="font-style: italic; color: #475569; font-size: 1.1em; line-height: 1.6; margin: 0; max-width: 800px; margin: 0 auto;">"Empowering businesses with innovative, sustainable solutions that integrate legacy and new technologies and enhance existing infrastructure and operational environments, driving growth while fostering a lasting positive impact on the world."</p>
            </div>
            
            <h2>What We Do</h2>
            <p class="intro">From strategic consulting to hands-on implementation, we deliver comprehensive solutions across three core areas.</p>
            
            <div class="grid" style="grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 40px; margin-top: 50px;">
                <div class="card" style="text-align: center; padding: 50px 30px;">
                    <div style="font-size: 3em; margin-bottom: 20px;">🔧</div>
                    <h3>Technology Services</h3>
                    <p>Strategic consulting, 5G & network infrastructure, cloud & virtualization, security consulting, and managed services that bridge legacy and modern systems.</p>
                </div>
                
                <div class="card" style="text-align: center; padding: 50px 30px;">
                    <div style="font-size: 3em; margin-bottom: 20px;">🤖</div>
                    <h3>AI-Driven Solutions</h3>
                    <p>Transparent, auditable AI platforms including ArionComply for compliance management, Arion A5EP for 5G networks, and Arion EIP for enterprise integration.</p>
                </div>
                
                <div class="card" style="text-align: center; padding: 50px 30px;">
                    <div style="font-size: 3em; margin-bottom: 20px;">🎓</div>
                    <h3>Training & Knowledge Transfer</h3>
                    <p>Expert-led training programs with hands-on lab experiences, empowering your teams with the skills needed for long-term technology success.</p>
                </div>
            </div>
        </div>

        <div class="section" id="technologies">
            <h2>Technologies We Master</h2>
            <p class="intro">Our expertise spans the complete technology ecosystem, from traditional infrastructure to cutting-edge AI platforms.</p>
            
            <div class="tech-tabs" role="tablist">
                <button class="tech-tab-button active" data-tab="telecom-5g" role="tab" aria-selected="true" aria-controls="telecom-5g-panel">📡 Telecom & 5G</button>
                <button class="tech-tab-button" data-tab="cloud-platforms" role="tab" aria-selected="false" aria-controls="cloud-platforms-panel">☁️ Cloud Platforms</button>
                <button class="tech-tab-button" data-tab="infrastructure-networking" role="tab" aria-selected="false" aria-controls="infrastructure-networking-panel">🏗️ Infrastructure & Networking</button>
                <button class="tech-tab-button" data-tab="automation-devops" role="tab" aria-selected="false" aria-controls="automation-devops-panel">⚙️ Automation & DevOps</button>
                <button class="tech-tab-button" data-tab="ai-ml" role="tab" aria-selected="false" aria-controls="ai-ml-panel">🤖 AI & Machine Learning</button>
                <button class="tech-tab-button" data-tab="databases-analytics" role="tab" aria-selected="false" aria-controls="databases-analytics-panel">📊 Databases & Analytics</button>
            </div>

            <div class="tech-tab-content active" id="telecom-5g-panel" role="tabpanel" aria-labelledby="telecom-5g">
                <div class="tech-carousel">
                    <div class="tech-carousel-wrapper">
                        <div class="tech-card">
                            <div class="tech-card-title">5G SA/NSA</div>
                            <div class="tech-card-description">Next-generation mobile networks with ultra-low latency and massive IoT support</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">4G LTE</div>
                            <div class="tech-card-description">High-speed mobile broadband foundation for reliable connectivity</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">VoLTE</div>
                            <div class="tech-card-description">Voice calls over 4G/5G networks with HD audio quality</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">VoWiFi</div>
                            <div class="tech-card-description">Seamless voice calls over WiFi networks for indoor coverage</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">IMS</div>
                            <div class="tech-card-description">Core multimedia session management for unified communications</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">SIP</div>
                            <div class="tech-card-description">Standard protocol for voice and video communication sessions</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">RAN</div>
                            <div class="tech-card-description">Radio access network infrastructure connecting devices to core</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Core Network</div>
                            <div class="tech-card-description">Central network processing hub for routing and services</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">NFV</div>
                            <div class="tech-card-description">Virtualized network functions replacing dedicated hardware</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">SDN</div>
                            <div class="tech-card-description">Software-defined network control for dynamic management</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">CUPS</div>
                            <div class="tech-card-description">Control and user plane separation for flexible scaling</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Network Slicing</div>
                            <div class="tech-card-description">Dedicated virtual network segments for specific use cases</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">MEC</div>
                            <div class="tech-card-description">Multi-access edge computing for ultra-low latency applications</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Open RAN</div>
                            <div class="tech-card-description">Open radio access networks with vendor interoperability</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">SON</div>
                            <div class="tech-card-description">Self-optimizing network automation for performance tuning</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tech-tab-content" id="cloud-platforms-panel" role="tabpanel" aria-labelledby="cloud-platforms">
                <div class="tech-carousel">
                    <div class="tech-carousel-wrapper">
                        <div class="tech-card">
                            <div class="tech-card-title">AWS</div>
                            <div class="tech-card-description">Comprehensive cloud services for scalable infrastructure solutions</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Microsoft Azure</div>
                            <div class="tech-card-description">Enterprise cloud platform with hybrid integration capabilities</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Google Cloud</div>
                            <div class="tech-card-description">AI-powered cloud services with advanced analytics tools</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">OpenStack</div>
                            <div class="tech-card-description">Open-source private cloud infrastructure platform</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">VMware vSphere</div>
                            <div class="tech-card-description">Enterprise virtualization platform for datacenter optimization</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Hyper-V</div>
                            <div class="tech-card-description">Microsoft's hypervisor for Windows-based virtualization</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">KVM</div>
                            <div class="tech-card-description">Linux kernel-based virtual machine for high performance</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Proxmox</div>
                            <div class="tech-card-description">Open-source virtualization management platform</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Red Hat OpenShift</div>
                            <div class="tech-card-description">Enterprise Kubernetes platform for container orchestration</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Kubernetes</div>
                            <div class="tech-card-description">Container orchestration for automated deployment and scaling</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Docker</div>
                            <div class="tech-card-description">Containerization platform for application packaging</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Rancher</div>
                            <div class="tech-card-description">Complete Kubernetes management platform for multi-cluster</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tech-tab-content" id="infrastructure-networking-panel" role="tabpanel" aria-labelledby="infrastructure-networking">
                <div class="tech-carousel">
                    <div class="tech-carousel-wrapper">
                        <div class="tech-card">
                            <div class="tech-card-title">Cisco</div>
                            <div class="tech-card-description">Enterprise networking solutions and security infrastructure</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Juniper</div>
                            <div class="tech-card-description">High-performance routing and switching for service providers</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Arista</div>
                            <div class="tech-card-description">Cloud networking solutions for modern data centers</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">HPE</div>
                            <div class="tech-card-description">Enterprise servers and storage infrastructure solutions</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Dell Technologies</div>
                            <div class="tech-card-description">Comprehensive infrastructure from edge to core to cloud</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">NetApp</div>
                            <div class="tech-card-description">Data management and storage solutions for hybrid cloud</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Pure Storage</div>
                            <div class="tech-card-description">All-flash storage arrays for modern data centers</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">F5 Networks</div>
                            <div class="tech-card-description">Application delivery and security load balancing solutions</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Palo Alto</div>
                            <div class="tech-card-description">Next-generation firewall and cybersecurity platform</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Fortinet</div>
                            <div class="tech-card-description">Integrated security fabric for network protection</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Linux</div>
                            <div class="tech-card-description">Open-source operating system for servers and containers</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Windows Server</div>
                            <div class="tech-card-description">Microsoft's enterprise server operating system platform</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">RHEL</div>
                            <div class="tech-card-description">Enterprise Linux with commercial support and security</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Ubuntu</div>
                            <div class="tech-card-description">Popular Linux distribution for development and production</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tech-tab-content" id="automation-devops-panel" role="tabpanel" aria-labelledby="automation-devops">
                <div class="tech-carousel">
                    <div class="tech-carousel-wrapper">
                        <div class="tech-card">
                            <div class="tech-card-title">Ansible</div>
                            <div class="tech-card-description">Agentless automation platform for configuration management</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Terraform</div>
                            <div class="tech-card-description">Infrastructure as code for multi-cloud provisioning</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Jenkins</div>
                            <div class="tech-card-description">Open-source automation server for CI/CD pipelines</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">GitLab CI/CD</div>
                            <div class="tech-card-description">Integrated DevOps platform with built-in automation</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">GitHub Actions</div>
                            <div class="tech-card-description">Native CI/CD workflows directly in your repository</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Azure DevOps</div>
                            <div class="tech-card-description">Microsoft's complete DevOps toolchain and services</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Python</div>
                            <div class="tech-card-description">Versatile programming language for automation and AI</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">PowerShell</div>
                            <div class="tech-card-description">Microsoft's task automation and configuration framework</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Bash</div>
                            <div class="tech-card-description">Unix shell scripting for system administration tasks</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Puppet</div>
                            <div class="tech-card-description">Declarative configuration management at enterprise scale</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Chef</div>
                            <div class="tech-card-description">Infrastructure automation with code-driven configuration</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">SaltStack</div>
                            <div class="tech-card-description">Event-driven automation for configuration and orchestration</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Helm</div>
                            <div class="tech-card-description">Package manager for Kubernetes application deployment</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">ArgoCD</div>
                            <div class="tech-card-description">GitOps continuous delivery for Kubernetes environments</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tech-tab-content" id="ai-ml-panel" role="tabpanel" aria-labelledby="ai-ml">
                <div class="tech-carousel">
                    <div class="tech-carousel-wrapper">
                        <div class="tech-card">
                            <div class="tech-card-title">TensorFlow</div>
                            <div class="tech-card-description">Google's machine learning framework for production AI</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">PyTorch</div>
                            <div class="tech-card-description">Dynamic neural network framework for research and production</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">LangChain</div>
                            <div class="tech-card-description">Framework for building AI applications with language models</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">OpenAI GPT</div>
                            <div class="tech-card-description">Advanced language models for natural language processing</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Hugging Face</div>
                            <div class="tech-card-description">Platform for sharing and deploying machine learning models</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Claude</div>
                            <div class="tech-card-description">AI assistant for analysis, writing, and complex reasoning</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Llama</div>
                            <div class="tech-card-description">Meta's open-source large language model family</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Vector Databases</div>
                            <div class="tech-card-description">Specialized storage for AI embeddings and similarity search</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Qdrant</div>
                            <div class="tech-card-description">High-performance vector database for AI applications</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Pinecone</div>
                            <div class="tech-card-description">Managed vector database for machine learning workloads</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">ChromaDB</div>
                            <div class="tech-card-description">Open-source embedding database for AI applications</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Jupyter</div>
                            <div class="tech-card-description">Interactive computing environment for data science</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">MLflow</div>
                            <div class="tech-card-description">Platform for managing machine learning lifecycle</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Kubeflow</div>
                            <div class="tech-card-description">Kubernetes-native platform for ML workflows</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">FastAPI</div>
                            <div class="tech-card-description">High-performance Python framework for AI API development</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="tech-tab-content" id="databases-analytics-panel" role="tabpanel" aria-labelledby="databases-analytics">
                <div class="tech-carousel">
                    <div class="tech-carousel-wrapper">
                        <div class="tech-card">
                            <div class="tech-card-title">PostgreSQL</div>
                            <div class="tech-card-description">Advanced open-source relational database with JSON support</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">MySQL</div>
                            <div class="tech-card-description">Popular open-source relational database for web applications</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">MongoDB</div>
                            <div class="tech-card-description">Document-oriented NoSQL database for flexible data storage</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Redis</div>
                            <div class="tech-card-description">In-memory data store for caching and real-time applications</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Elasticsearch</div>
                            <div class="tech-card-description">Distributed search and analytics engine for big data</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">InfluxDB</div>
                            <div class="tech-card-description">Time-series database for IoT and monitoring data</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Supabase</div>
                            <div class="tech-card-description">Open-source Firebase alternative with PostgreSQL backend</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Firebase</div>
                            <div class="tech-card-description">Google's platform for real-time applications and analytics</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Oracle</div>
                            <div class="tech-card-description">Enterprise-grade relational database for mission-critical apps</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">SQL Server</div>
                            <div class="tech-card-description">Microsoft's enterprise database with advanced analytics</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Cassandra</div>
                            <div class="tech-card-description">Distributed NoSQL database for high-availability systems</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Grafana</div>
                            <div class="tech-card-description">Data visualization and monitoring dashboard platform</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">Prometheus</div>
                            <div class="tech-card-description">Monitoring system with time-series data collection</div>
                        </div>
                        <div class="tech-card">
                            <div class="tech-card-title">ELK Stack</div>
                            <div class="tech-card-description">Elasticsearch, Logstash, Kibana for log analysis</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="trust-section">
            <div class="container">
                <h2 style="color: #1e293b; margin-bottom: 30px;">Why Choose Arion Networks</h2>
                <div class="trust-grid">
                    <div class="trust-item">
                        <h4>🎯 Precision-Driven Partnerships</h4>
                        <p>Tailored solutions designed for your specific infrastructure needs through long-term strategic partnerships with ongoing support and continuous optimization</p>
                    </div>
                    <div class="trust-item">
                        <h4>🔒 Security-First</h4>
                        <p>Enterprise-grade security woven into every solution, ensuring comprehensive data protection, regulatory compliance, and robust defense strategies against evolving cybersecurity threats</p>
                    </div>
                    <div class="trust-item">
                        <h4>🚀 Innovation-Led</h4>
                        <p>Cutting-edge AI and 5G technologies combined with forward-thinking approaches that keep you ahead of the technological curve and competitive landscape</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="section" id="services">
            <h2>Our Technology Services</h2>
            <p class="intro">We bring together strategic expertise, hands-on implementation, and ongoing support across the complete technology lifecycle. From initial consultation through deployment and optimization, our team delivers solutions that drive real business value.</p>
            
            <div class="grid">
                <div class="card">
                    <h3>Strategic Consulting</h3>
                    <p>Expert guidance for 5G, infrastructure, and networking technologies. Tailored strategies to optimize performance, reduce risk, and accelerate time-to-market.</p>
                </div>
                
                <div class="card">
                    <h3>5G & Telco Network Services</h3>
                    <p>Complete lifecycle management for 5G and 4G networks, from assessment and design to implementation, integration, migration, and ongoing optimization. Specialized expertise in 5G standalone (SA) and non-standalone (NSA) deployments.</p>
                </div>
                
                <div class="card">
                    <h3>Infrastructure & Virtualization</h3>
                    <p>Comprehensive solutions spanning OpenStack, OpenShift, KVM, VMware, and Azure Cloud. From transformation consulting to managed operations.</p>
                </div>
                
                <div class="card">
                    <h3>Security Consulting</h3>
                    <p>Identify vulnerabilities and implement robust defense strategies. Comprehensive security measures designed to protect against evolving threats.</p>
                </div>
                
                <div class="card">
                    <h3>Advanced Lab Services</h3>
                    <p>Cutting-edge lab environments for AI application testing, hands-on training, and product validation. Specialized support for domain-driven AI models.</p>
                </div>
                
                <div class="card">
                    <h3>Managed Services</h3>
                    <p>Proactive support and continuous optimization to ensure peak network performance. Complete infrastructure management and scaling solutions.</p>
                </div>
            </div>
        </div>

        <div class="interactive-demo">
            <div class="container">
                <h2 style="text-align: center; margin-bottom: 40px;">Experience Our AI-Driven Solutions</h2>
                <p class="intro" style="color: #64748b; margin-top: 0; margin-bottom: 50px;">Discover how our intelligent platforms enhance compliance, optimize network performance, and streamline enterprise operations.</p>
                
                <div class="demo-terminal">
                    <div class="terminal-header">
                        <div class="terminal-dots">
                            <div class="dot red"></div>
                            <div class="dot yellow"></div>
                            <div class="dot green"></div>
                        </div>
                        <div class="terminal-title">ArionComply Demo Terminal</div>
                    </div>
                    <div class="terminal-content">
                        <div id="terminalOutput">
                            <div>$ arion-comply --scan security-policy</div>
                            <div style="color: #10b981;">✓ Scanning 247 compliance documents...</div>
                            <div style="color: #3b82f6;">→ ISO 27001: 98% compliance achieved</div>
                            <div style="color: #3b82f6;">→ GDPR: All requirements satisfied</div>
                            <div style="color: #f59e0b;">⚠ 3 minor recommendations identified</div>
                            <div class="typing-effect" id="typingLine">$ </div>
                        </div>
                    </div>
                </div>

                <h3 style="text-align: center; color: #1e293b; margin-top: 60px; margin-bottom: 30px; font-size: 2em; font-weight: 800;">Our Intelligent Platforms</h3>
                <div class="ai-product-grid">
                    <div class="ai-product-card">
                        <h4>Arion A5EP</h4>
                        <p>An advanced AI-powered platform designed to optimize 5G network performance, predict potential issues, and enhance operational efficiency for telecommunication providers.</p>
                        <a href="https://5g.arionetworks.com" target="_blank" class="btn-learn-more">Learn More</a>
                    </div>
                    <div class="ai-product-card">
                        <h4>Arion EIP</h4>
                        <p>Our Enterprise Integration Platform leverages AI to seamlessly connect disparate systems, automate workflows, and provide actionable insights across your organization's data landscape.</p>
                        <a href="https://ai.arionetworks.com" target="_blank" class="btn-learn-more">Learn More</a>
                    </div>
                    <div class="ai-product-card">
                        <h4>ArionComply</h4>
                        <p>Automate and streamline your compliance management processes with AI. ArionComply intelligently analyzes regulations, identifies gaps, and ensures continuous adherence to industry standards.</p>
                        <a href="https://iso.arionetworks.com" target="_blank" class="btn-learn-more">Learn More</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="section" id="training">
            <h2>Knowledge Transfer & Training</h2>
            <p class="intro">Beyond implementation, we believe in empowering your team with the knowledge and skills needed for long-term success. Our comprehensive training programs combine expert instruction with real-world, hands-on laboratory experiences.</p>
            
            <div class="card" style="background: white;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px; align-items: center;">
                    <div>
                        <h3>LabGuide Training Platform</h3>
                        <ul style="color: #666;">
                            <li>Intensive hands-on experience with real lab environments</li>
                            <li>Industry's highest-rated trainers and specialists</li>
                            <li>Flexible online, onsite, and blended delivery options</li>
                        </ul>
                        <p><strong>Training Areas:</strong></p>
                        <div>
                            <span style="background: #e3f2fd; color: #1976d2; padding: 5px 10px; border-radius: 15px; font-size: 0.9em; margin-right: 10px;">Unified Communications</span>
                            <span style="background: #e8f5e8; color: #388e3c; padding: 5px 10px; border-radius: 15px; font-size: 0.9em; margin-right: 10px;">4G/5G Networks</span>
                            <span style="background: #f3e5f5; color: #7b1fa2; padding: 5px 10px; border-radius: 15px; font-size: 0.9em;">Custom Programs</span>
                        </div>
                        <br>
                        <a href="https://labguide.io" target="_blank" style="background: #ffc107; color: black; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold;">Visit LabGuide Platform →</a>
                    </div>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                        <h4>Search Training Catalog</h4>
                        <div style="display: flex; margin-bottom: 15px;">
                            <input type="text" placeholder="What do you want to learn?" style="flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 5px 0 0 5px;">
                            <button style="background: #ffc107; color: black; padding: 10px 20px; border: none; border-radius: 0 5px 5px 0; font-weight: bold;">Search</button>
                        </div>
                        <p style="text-align: center; color: #666; font-size: 0.9em;">Explore our comprehensive training catalog with hands-on lab experiences</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="section" id="contact">
            <div class="interactive-demo">
                <div class="container">
                    <h2 style="text-align: center; margin-bottom: 40px;">Let's Start Your Transformation</h2>
                    <p class="intro" style="color: #64748b;">Ready to explore how our services, AI solutions, and training programs can accelerate your technology initiatives? We'd love to discuss your specific challenges and objectives.</p>
            
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 40px;">
                <div class="card">
                    <h3>Get in Touch</h3>
                    <form style="display: grid; gap: 15px;">
                        <input type="text" placeholder="Your Name" style="padding: 12px; border: 1px solid #ddd; border-radius: 5px;">
                        <input type="email" placeholder="Email Address" style="padding: 12px; border: 1px solid #ddd; border-radius: 5px;">
                        <input type="text" placeholder="Company" style="padding: 12px; border: 1px solid #ddd; border-radius: 5px;">
                        <textarea placeholder="How can we help you?" rows="4" style="padding: 12px; border: 1px solid #ddd; border-radius: 5px; resize: vertical;"></textarea>
                        <button type="submit" style="background: #1E3A8A; color: white; padding: 12px; border: none; border-radius: 5px; font-weight: bold; cursor: pointer;">Send Message</button>
                    </form>
                </div>
                
                <div class="card">
                    <h3>Connect With Us</h3>
                    <div style="display: grid; gap: 20px;">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div style="width: 40px; height: 40px; background: #e3f2fd; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #1976d2; font-weight: bold;">@</div>
                            <div>
                                <p style="margin: 0; font-weight: bold;">Email</p>
                                <p style="margin: 0; color: #666;"><EMAIL></p>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div style="width: 40px; height: 40px; background: #e3f2fd; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #1976d2; font-weight: bold;">C</div>
                            <div>
                                <p style="margin: 0; font-weight: bold;">Consulting</p>
                                <p style="margin: 0; color: #666;">Strategic guidance & implementation</p>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div style="width: 40px; height: 40px; background: #e3f2fd; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #1976d2; font-weight: bold;">AI</div>
                            <div>
                                <p style="margin: 0; font-weight: bold;">AI Products</p>
                                <p style="margin: 0; color: #666;">Schedule a demo of our solutions</p>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>

    </div>

    <footer>
        <div class="container">
            <h3>ARION NETWORKS</h3>
            <p class="tagline">INNOVATION • PERFORMANCE • PARTNERSHIP</p>
            <p>Empowering organizations with comprehensive technology services, AI-driven solutions, and specialized training.</p>
            
            <div style="margin: 30px 0;">
                <span style="color: #999; font-size: 0.9em;">Our Locations:</span><br>
                <span style="color: #cbd5e1; font-size: 0.95em;">
                    Prague, Czechia • Arkansas, USA
                </span>
            </div>
            
            <hr style="border: none; border-top: 1px solid #555; margin: 30px 0;">
            <p>&copy; 2025 Arion Networks. All rights reserved.</p>
            <div style="margin: 20px 0;">
                <a href="/privacy-policy.html">Privacy Policy</a>
                <a href="/data-security.html">Data Security</a>
                <a href="/terms-of-use.html">Terms of Use</a>
                <a href="/cookies-policy.html">Cookies Policy</a>
                <a href="#" onclick="showPrivacySettings()" style="color: #94a3b8;">Cookie Settings</a>
            </div>
            <div>
                <span style="color: #999;">Our AI Products:</span>
                <a href="https://iso.arionetworks.com" style="color: #059669;">ArionComply</a>
                <a href="https://5g.arionetworks.com" style="color: #EA580C;">Arion A5EP</a>
                <a href="https://ai.arionetworks.com" style="color: #7C3AED;">Arion EIP</a>
            </div>
        </div>
    </footer>

    <script>
        // Loading animation
        window.addEventListener('load', function() {
            setTimeout(() => {
                document.getElementById('loading').style.opacity = '0';
                setTimeout(() => {
                    document.getElementById('loading').classList.add('hidden');
                }, 500);
            }, 1000);
        });

        // Create animated background particles
        function createParticles() {
            const bgAnimation = document.getElementById('bgAnimation');
            const particleCount = 15;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                const size = Math.random() * 4 + 2;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                
                bgAnimation.appendChild(particle);
            }
        }

        // Scroll animations
        function animateOnScroll() {
            const elements = document.querySelectorAll('.section, .card, .ai-product-card');
            const windowHeight = window.innerHeight;
            
            elements.forEach(element => {
                const elementTop = element.getBoundingClientRect().top;
                const elementVisible = 150;
                
                if (elementTop < windowHeight - elementVisible) {
                    element.classList.add('visible');
                }
            });
        }

        // Navbar scroll effect
        function handleNavbarScroll() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        }

        // Terminal typing effect
        function typeInTerminal() {
            const terminalLines = [
                '$ arion-comply --scan security-policy',
                '→ Scanning 247 compliance documents...',
                '✓ ISO 27001: 98% compliance achieved',
                '✓ GDPR: All requirements satisfied',
                '⚠ 3 minor recommendations identified'
            ];
            
            let lineIndex = 0;
            let charIndex = 0;
            const typingLine = document.getElementById('typingLine');
            const terminalOutput = document.getElementById('terminalOutput');
            
            function typeLine() {
                if (lineIndex < terminalLines.length) {
                    if (charIndex < terminalLines[lineIndex].length) {
                        typingLine.textContent = terminalLines[lineIndex].substring(0, charIndex + 1) + '|';
                        charIndex++;
                        setTimeout(typeLine, 50);
                    } else {
                        // Line complete, add it to output and start next line
                        const newLine = document.createElement('div');
                        newLine.textContent = terminalLines[lineIndex];
                        if (terminalLines[lineIndex].includes('✓')) {
                            newLine.style.color = '#10b981';
                        } else if (terminalLines[lineIndex].includes('→')) {
                            newLine.style.color = '#3b82f6';
                        } else if (terminalLines[lineIndex].includes('⚠')) {
                            newLine.style.color = '#f59e0b';
                        }
                        terminalOutput.insertBefore(newLine, typingLine);
                        
                        lineIndex++;
                        charIndex = 0;
                        setTimeout(typeLine, 500);
                    }
                } else {
                    typingLine.textContent = '$ ';
                    // Restart the typing effect after a pause
                    setTimeout(() => {
                        // Clear previous lines except the initial ones
                        const lines = terminalOutput.children;
                        // Only clear dynamically added lines, keep initial prompt and results
                        while (lines.length > 5) {
                            terminalOutput.removeChild(lines[lines.length - 2]);
                        }
                        lineIndex = 0;
                        charIndex = 0;
                        setTimeout(typeLine, 3000);
                    }, 3000);
                }
            }
            
            setTimeout(typeLine, 2000);
        }

        // Smooth scrolling for navigation links and scroll indicator
        function setupSmoothScrolling() {
            const links = document.querySelectorAll('a[href^="#"]');
            
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    const targetId = this.getAttribute('href');
                    const targetElement = document.querySelector(targetId);
                    
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add click handler for hero scroll indicator
            const scrollIndicator = document.querySelector('.hero-scroll-indicator');
            if (scrollIndicator) {
                scrollIndicator.addEventListener('click', function() {
                    const servicesSection = document.querySelector('#what-we-do');
                    if (servicesSection) {
                        servicesSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            }
        }

        // Cookie Consent Functions
        function checkCookieConsent() {
            const consent = localStorage.getItem('cookieConsent');
            if (!consent) {
                setTimeout(() => {
                    document.getElementById('cookieConsent').classList.add('show');
                }, 2000);
            }
        }

        function acceptAllCookies() {
            localStorage.setItem('cookieConsent', 'all');
            localStorage.setItem('analyticsConsent', 'true');
            localStorage.setItem('marketingConsent', 'true');
            hideCookieConsent();
        }

        function showPrivacySettings() {
            document.getElementById('privacyModal').classList.add('show');
            // Set current preferences
            const analyticsConsent = localStorage.getItem('analyticsConsent') === 'true';
            const marketingConsent = localStorage.getItem('marketingConsent') === 'true';
            document.getElementById('analyticsConsent').checked = analyticsConsent;
            document.getElementById('marketingConsent').checked = marketingConsent;
        }

        function closePrivacySettings() {
            document.getElementById('privacyModal').classList.remove('show');
        }

        function savePrivacySettings() {
            const analyticsConsent = document.getElementById('analyticsConsent').checked;
            const marketingConsent = document.getElementById('marketingConsent').checked;
            
            localStorage.setItem('cookieConsent', 'custom');
            localStorage.setItem('analyticsConsent', analyticsConsent.toString());
            localStorage.setItem('marketingConsent', marketingConsent.toString());
            
            closePrivacySettings();
            hideCookieConsent();
        }

        function hideCookieConsent() {
            document.getElementById('cookieConsent').classList.remove('show');
        }

        // FIXED JAVASCRIPT FOR TABBED TECHNOLOGIES SECTION
        function setupTechTabs() {
            const tabButtons = document.querySelectorAll('.tech-tab-button');
            const tabContents = document.querySelectorAll('.tech-tab-content');

            // Add click event listeners to all tab buttons
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Remove active class from all buttons and contents
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active');
                        btn.setAttribute('aria-selected', 'false');
                    });
                    tabContents.forEach(content => {
                        content.classList.remove('active');
                    });

                    // Add active class to clicked button and corresponding content
                    button.classList.add('active');
                    button.setAttribute('aria-selected', 'true');
                    
                    const targetTab = button.getAttribute('data-tab');
                    const targetContent = document.getElementById(targetTab + '-panel');
                    if (targetContent) {
                        targetContent.classList.add('active');
                        // Restart carousel for the new tab
                        initializeCarousel(targetContent);
                    }
                });
            });

            // Set the first tab as active by default on load
            if (tabButtons.length > 0 && tabContents.length > 0) {
                tabButtons[0].classList.add('active');
                tabButtons[0].setAttribute('aria-selected', 'true');
                tabContents[0].classList.add('active');
            }
        }

        // Auto-scrolling carousel functionality
        let carouselIntervals = new Map();

        function initializeCarousel(tabContent) {
            const carousel = tabContent.querySelector('.tech-carousel-wrapper');
            if (!carousel) return;

            const cards = carousel.querySelectorAll('.tech-card');
            if (cards.length === 0) return;

            // Clear any existing interval for this carousel
            const existingInterval = carouselIntervals.get(carousel);
            if (existingInterval) {
                clearInterval(existingInterval);
            }

            let currentIndex = 0;

            function updateCarousel() {
                // Remove all classes
                cards.forEach((card, index) => {
                    card.classList.remove('active', 'prev', 'next');
                    
                    if (index === currentIndex) {
                        card.classList.add('active');
                    } else if (index === (currentIndex + 1) % cards.length) {
                        card.classList.add('next');
                    } else if (index === (currentIndex - 1 + cards.length) % cards.length) {
                        card.classList.add('prev');
                    }
                });
            }

            function nextCard() {
                currentIndex = (currentIndex + 1) % cards.length;
                updateCarousel();
            }

            // Initialize first card
            updateCarousel();

            // Start auto-cycling
            const interval = setInterval(nextCard, 3500); // Change card every 3.5 seconds (slower pace)
            carouselIntervals.set(carousel, interval);
        }

        function initializeAllCarousels() {
            // Initialize carousel for the first (active) tab
            const activeTab = document.querySelector('.tech-tab-content.active');
            if (activeTab) {
                initializeCarousel(activeTab);
            }
        }

        // Pause carousel on hover
        function setupCarouselHoverEffects() {
            document.querySelectorAll('.tech-carousel-wrapper').forEach(carousel => {
                carousel.addEventListener('mouseenter', () => {
                    const interval = carouselIntervals.get(carousel);
                    if (interval) {
                        clearInterval(interval);
                    }
                });

                carousel.addEventListener('mouseleave', () => {
                    // Restart the carousel after a short delay
                    setTimeout(() => {
                        const tabContent = carousel.closest('.tech-tab-content');
                        if (tabContent && tabContent.classList.contains('active')) {
                            initializeCarousel(tabContent);
                        }
                    }, 500);
                });
            });
        }

        // Initialize everything
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            setupSmoothScrolling();
            checkCookieConsent();
            setupTechTabs(); // Initialize the fixed tab functionality
            
            // Trigger initial scroll animations
            setTimeout(() => {
                animateOnScroll();
                typeInTerminal();
                initializeAllCarousels(); // Start the auto-scrolling carousels
                setupCarouselHoverEffects(); // Setup hover pause functionality
            }, 1500);
        });

        // Throttle scroll events for better performance
        let ticking = false;
        function updateOnScroll() {
            animateOnScroll();
            handleNavbarScroll();
            ticking = false;
        }

        window.addEventListener('scroll', function() {
            if (!ticking) {
                requestAnimationFrame(updateOnScroll);
                ticking = true;
            }
        });
    </script>

</body>
</html>
