class ComplianceResult {
  final bool isCompliant;
  final String? category;
  final Map<String, dynamic>? rejectionMessage;
  const ComplianceResult({required this.isCompliant, this.category, this.rejectionMessage});
}

class ComplianceFilterService {
  final List<String> _forbidden = [
    'weather','sports','entertainment','movies','music','games','food','recipes','travel','vacation','dating','relationship','fitness','medical','therapy','personal','homework','essay','story','poem','art','photography','cooking'
  ];

  final Map<String, List<String>> _allowed = {
    'iso': ['iso 27001','iso27001','iso 9001','iso 14001','iso 22301','iso 20000','iso 31000','information security','iso certification','iso audit','iso implementation'],
    'gdpr': ['gdpr','data protection','privacy','data subject','consent','dpo','privacy impact assessment','dpia','data breach','right to be forgotten','data portability','lawful basis','personal data','controller','processor','privacy by design','ccpa'],
    'ai': ['ai governance','ai ethics','ai compliance','ai risk','ai assessment','algorithmic','responsible ai','eu ai act','bias','explainable ai','fairness','automated decision'],
    'security': ['cybersecurity','security controls','security policies','procedures','audit','vulnerability','threat','incident','access control','identity management','mfa','encryption','penetration testing','security monitoring'],
    'frameworks': ['soc2','sox','pci dss','hipaa','nist','cobit','itil','cis controls','compliance framework'],
    'risk': ['risk management','risk assessment','risk analysis','risk mitigation','risk register','third party risk','vendor risk','risk appetite','risk governance'],
    'policy': ['compliance policy','security policy','privacy policy','incident response policy','policy management','procedures','guidelines','standards','documentation'],
  };

  ComplianceResult validate(String query) {
    final q = query.toLowerCase().trim();
    // allow if matches allowed keywords
    for (final entry in _allowed.entries) {
      for (final t in entry.value) {
        if (q.contains(t)) {
          return ComplianceResult(isCompliant: true, category: entry.key);
        }
      }
    }

    // reject if clearly forbidden
    for (final t in _forbidden) {
      if (q.contains(t)) {
        return ComplianceResult(isCompliant: false, rejectionMessage: _rejection('general'));
      }
    }

    // default: non-compliance until context added (simple approach)
    return ComplianceResult(isCompliant: false, rejectionMessage: _rejection('general'));
  }

  Map<String, dynamic> _rejection(String category) {
    return {
      'title': 'Outside Our Expertise',
      'message': 'ArionComply focuses on compliance, security, and governance topics (ISO 27001, GDPR, AI governance, SOC2, NIST, etc.).',
      'suggestion': 'Please ask about compliance requirements, policies, regulatory standards, or audit preparation.',
      'allowedTopics': _samples,
    };
  }

  List<String> get _samples => [
    'How do I prepare for ISO 27001 certification?',
    'What are GDPR data subject rights?',
    'How to implement an AI governance framework?',
    'SOC2 compliance requirements explained',
    'Risk assessment methodology best practices',
    'Security policy template guidance'
  ];
}


