Database inspection report
Generated: <PERSON><PERSON> Aug 26 12:21:41 CEST 2025
==================================

[Extensions]

                                            List of installed extensions
        Name        | Version |   Schema   |                              Description                               
--------------------+---------+------------+------------------------------------------------------------------------
 pg_graphql         | 1.5.11  | graphql    | pg_graphql: GraphQL support
 pg_stat_statements | 1.11    | extensions | track planning and execution statistics of all SQL statements executed
 pgcrypto           | 1.3     | extensions | cryptographic functions
 plpgsql            | 1.0     | pg_catalog | PL/pgSQL procedural language
 supabase_vault     | 0.3.1   | vault      | Supabase Vault Extension
 uuid-ossp          | 1.1     | extensions | generate universally unique identifiers (UUIDs)
 vector             | 0.8.0   | public     | vector data type and ivfflat and hnsw access methods
(7 rows)


[Table sizes & row estimates]

 schema  |           table            | bytes  |  size  | est_rows 
---------+----------------------------+--------+--------+----------
 public  | knowledge_documents        | 950272 | 928 kB |       -1
 public  | demo_interactions          | 581632 | 568 kB |      514
 public  | product_registrations      | 221184 | 216 kB |       -1
 public  | demo_users                 | 196608 | 192 kB |       -1
 auth    | users                      | 163840 | 160 kB |        4
 public  | demo_sessions              | 163840 | 160 kB |       71
 public  | demo_contacts              | 147456 | 144 kB |       -1
 auth    | refresh_tokens             | 131072 | 128 kB |       -1
 public  | demo_verification_requests | 114688 | 112 kB |       -1
 public  | contextual_prompts         | 114688 | 112 kB |        6
 auth    | one_time_tokens            | 114688 | 112 kB |       -1
 public  | admin_verification_codes   |  81920 | 80 kB  |       25
 auth    | sessions                   |  81920 | 80 kB  |       -1
 auth    | identities                 |  81920 | 80 kB  |       -1
 public  | response_templates         |  65536 | 64 kB  |       -1
 storage | objects                    |  65536 | 64 kB  |       -1
 public  | demo_documents             |  65536 | 64 kB  |       -1
 public  | knowledge_base             |  65536 | 64 kB  |       -1
 public  | faq_items                  |  65536 | 64 kB  |       -1
 public  | admin_sessions             |  57344 | 56 kB  |       -1
 auth    | mfa_factors                |  57344 | 56 kB  |       -1
 auth    | audit_log_entries          |  49152 | 48 kB  |       -1
 auth    | mfa_amr_claims             |  49152 | 48 kB  |       -1
 public  | prompt_usage_logs          |  40960 | 40 kB  |       -1
 public  | event_logs                 |  40960 | 40 kB  |       -1
 storage | migrations                 |  40960 | 40 kB  |       -1
 auth    | saml_relay_states          |  40960 | 40 kB  |       -1
 auth    | flow_state                 |  40960 | 40 kB  |       -1
 public  | goal_analytics             |  32768 | 32 kB  |       -1
 auth    | sso_domains                |  32768 | 32 kB  |       -1
 public  | user_custom_prompts        |  32768 | 32 kB  |       -1
 public  | prompt_performance_metrics |  32768 | 32 kB  |       -1
 public  | demo_embeddings            |  32768 | 32 kB  |       -1
 auth    | saml_providers             |  32768 | 32 kB  |       -1
 public  | user_queries_log           |  32768 | 32 kB  |       -1
 storage | buckets                    |  24576 | 24 kB  |       -1
 auth    | mfa_challenges             |  24576 | 24 kB  |       -1
 storage | s3_multipart_uploads       |  24576 | 24 kB  |       -1
 auth    | schema_migrations          |  24576 | 24 kB  |       61
 storage | prefixes                   |  24576 | 24 kB  |       -1
 auth    | sso_providers              |  24576 | 24 kB  |       -1
 storage | s3_multipart_uploads_parts |  16384 | 16 kB  |       -1
 public  | persistent_admin_sessions  |  16384 | 16 kB  |       -1
 public  | demo_intake_data           |  16384 | 16 kB  |       -1
 auth    | instances                  |  16384 | 16 kB  |       -1
 storage | buckets_analytics          |  16384 | 16 kB  |       -1
 public  | demo_browsing_logs         |  16384 | 16 kB  |       -1
(47 rows)


[RLS enabled tables]

