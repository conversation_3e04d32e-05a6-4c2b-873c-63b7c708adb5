# Arion Flutter (Web parity)

Flutter BLoC app mirroring the ArionComply web demo (landing, login, demo access, privacy policy), wired to the same API.

## Run (Web)

```bash
flutter pub get
flutter run -d chrome
```

## Build (Web)

```bash
flutter build web --release
```

Outputs to `build/web` (host with any static server).

## Structure

- `lib/src/core` – DI, config, API client, session
- `lib/src/data` – repositories
- `lib/src/presentation` – routes, theme, blocs, views, widgets


## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
