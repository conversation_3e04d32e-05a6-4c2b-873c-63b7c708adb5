/**
 * ArionComply Document Generation Module - Complete Version with Floating Button
 * Version: 1.1
 * Purpose: Handle document generation, viewing, and demo limitations
 */

const DocumentGenerator = {
  // Properties
  documentsGenerated: 0,
  maxDemoDocuments: 2,
  sessionId: null,
  currentDocuments: [],
  generatedDocuments: [], // Store generated documents
  userInitiatedSession: false, // NEW: Track if user has initiated interaction

  // Initialize
  init() {
    console.log('📄 DocumentGenerator initializing...');
    this.loadLimits();
    this.loadGeneratedDocuments();
    
    // Restore floating button if user has documents
    if (this.documentsGenerated > 0) {
      setTimeout(() => {
        this.createFloatingDocButton();
      }, 1000);
    }
    
    console.log('📄 DocumentGenerator initialized successfully');
  },

  // =============================================================================
  // DOCUMENT REQUEST DETECTION
  // =============================================================================

  isDocumentRequest(message) {
    console.log('🔍 Checking document request:', message);
    
    // Only trigger on explicit action words
    const actionWords = /\b(generate|create|make|build|produce|develop|draft|prepare)\b/i;
    
    // Document types
    const documentTypes = /\b(policy|document|register|report|analysis|framework|template|guide|plan|procedure|assessment|audit|compliance|gdpr|iso|dpia|soa|risk|security|privacy)\b/i;
    
    // Must contain both an action word AND a document type
    if (actionWords.test(message) && documentTypes.test(message)) {
      console.log('📄 Document request detected:', message);
      return true;
    }
    
    console.log('📄 Not a document request (missing action word):', message);
    return false;
  },

  // Remove the AI-specific method since we're going back to original approach

  detectDocumentType(message) {
    const types = {
      'information_security_policy': /information security policy|security policy|generate.*policy|create.*policy/i,
      'risk_register': /risk register|create.*risk|generate.*risk/i,
      'soa': /statement of applicability|soa|iso.*control/i,
      'gdpr_document': /gdpr.*document|privacy policy|data protection.*document/i,
      'audit_report': /audit report|compliance.*report|audit.*document/i,
      'dpia': /dpia|data protection impact|impact assessment/i,
      'gap_analysis': /gap analysis|compliance.*gap|assessment.*gap/i
    };

    for (const [type, pattern] of Object.entries(types)) {
      if (pattern.test(message)) {
        return type;
      }
    }
    
    return 'general_document';
  },

  // =============================================================================
  // DEMO LIMITATIONS
  // =============================================================================

  loadLimits() {
    try {
      const stored = localStorage.getItem('arion-doc-limits');
      if (stored) {
        const data = JSON.parse(stored);
        this.documentsGenerated = data.generated || 0;
      }
    } catch (e) {
      this.documentsGenerated = 0;
    }
  },

  saveLimits() {
    try {
      localStorage.setItem('arion-doc-limits', JSON.stringify({
        generated: this.documentsGenerated,
        timestamp: Date.now()
      }));
    } catch (e) {
      console.warn('Could not save document limits');
    }
  },

  loadGeneratedDocuments() {
    try {
      const stored = localStorage.getItem('arion-generated-docs');
      if (stored) {
        this.generatedDocuments = JSON.parse(stored);
      }
    } catch (e) {
      this.generatedDocuments = [];
    }
  },

  saveGeneratedDocuments() {
    try {
      localStorage.setItem('arion-generated-docs', JSON.stringify(this.generatedDocuments));
    } catch (e) {
      console.warn('Could not save generated documents');
    }
  },

  hasReachedLimit() {
    return this.documentsGenerated >= this.maxDemoDocuments;
  },

  getRemainingDocuments() {
    return Math.max(0, this.maxDemoDocuments - this.documentsGenerated);
  },

  // =============================================================================
  // FLOATING BUTTON FUNCTIONALITY
  // =============================================================================

  createFloatingDocButton() {
    // Remove existing button if present
    const existing = document.getElementById('floatingDocBtn');
    if (existing) existing.remove();
    
    const floatingBtn = document.createElement('div');
    floatingBtn.id = 'floatingDocBtn';
    floatingBtn.style.cssText = `
      position: fixed;
      bottom: 100px;
      right: 20px;
      background: #059669;
      color: white;
      border-radius: 50px;
      padding: 12px 18px;
      font-size: 0.9rem;
      font-weight: 600;
      cursor: pointer;
      box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
      z-index: 9999;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      user-select: none;
      animation: slideInUp 0.5s ease-out;
    `;
    
    floatingBtn.innerHTML = `
      <span class="btn-icon">📄</span>
      <span class="btn-text">View Docs</span>
      <span class="docs-badge">${this.documentsGenerated}</span>
    `;
    
    // Style the badge
    const badge = floatingBtn.querySelector('.docs-badge');
    if (badge) {
      badge.style.cssText = `
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        padding: 2px 8px;
        font-size: 0.75rem;
        font-weight: 700;
        min-width: 20px;
        text-align: center;
        backdrop-filter: blur(4px);
      `;
    }
    
    // Add CSS animation
    if (!document.getElementById('floatingBtnStyles')) {
      const styles = document.createElement('style');
      styles.id = 'floatingBtnStyles';
      styles.textContent = `
        @keyframes slideInUp {
          from {
            transform: translateY(100px);
            opacity: 0;
          }
          to {
            transform: translateY(0);
            opacity: 1;
          }
        }
        
        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
          }
          40% {
            transform: translateY(-5px);
          }
          60% {
            transform: translateY(-3px);
          }
        }
        
        @media (max-width: 768px) {
          #floatingDocBtn {
            bottom: 120px !important;
            right: 15px !important;
            padding: 10px 14px !important;
            font-size: 0.85rem !important;
          }
        }
      `;
      document.head.appendChild(styles);
    }
    
    // Hover effects
    floatingBtn.addEventListener('mouseenter', () => {
      floatingBtn.style.transform = 'translateY(-3px) scale(1.05)';
      floatingBtn.style.boxShadow = '0 8px 25px rgba(5, 150, 105, 0.4)';
    });
    
    floatingBtn.addEventListener('mouseleave', () => {
      floatingBtn.style.transform = 'translateY(0) scale(1)';
      floatingBtn.style.boxShadow = '0 4px 12px rgba(5, 150, 105, 0.3)';
    });
    
    // Click handler
    floatingBtn.addEventListener('click', () => {
      // Add click animation
      floatingBtn.style.animation = 'bounce 0.5s ease';
      setTimeout(() => {
        floatingBtn.style.animation = '';
      }, 500);
      
      this.viewDocuments();
    });
    
    document.body.appendChild(floatingBtn);
    
    console.log('📄 Floating document button created with', this.documentsGenerated, 'documents');
  },

  updateFloatingDocButton() {
    const floatingBtn = document.getElementById('floatingDocBtn');
    if (floatingBtn) {
      const badge = floatingBtn.querySelector('.docs-badge');
      if (badge) {
        badge.textContent = this.documentsGenerated;
        
        // Add update animation
        badge.style.transform = 'scale(1.3)';
        badge.style.transition = 'transform 0.2s ease';
        setTimeout(() => {
          badge.style.transform = 'scale(1)';
        }, 200);
      }
    }
  },

  removeFloatingDocButton() {
    const existing = document.getElementById('floatingDocBtn');
    if (existing) {
      existing.style.animation = 'slideInUp 0.3s ease-in reverse';
      setTimeout(() => {
        existing.remove();
      }, 300);
    }
    
    // Also remove styles if no longer needed
    const styles = document.getElementById('floatingBtnStyles');
    if (styles && this.documentsGenerated === 0) {
      styles.remove();
    }
  },

  // =============================================================================
  // DOCUMENT GENERATION
  // =============================================================================

  async handleDocumentRequest(message, sessionId) {
    console.log('📄 Handling document request:', message);
    
    this.sessionId = sessionId;
    
    // Check if user has reached limit
    if (this.hasReachedLimit()) {
      return {
        response: `📋 Demo Document Limit Reached

You've already generated ${this.maxDemoDocuments} documents in this demo session. To generate unlimited documents and access full features, please request full access to ArionComply.

🎯 With full access you'll get:
• Unlimited document generation
• Download & export capabilities
• Custom document templates
• Advanced compliance tools`,
        quickReplies: ['📄 View My Documents', '🚀 Request Full Access', 'Contact Sales'],
        limitReached: true
      };
    }

    try {
      console.log('📄 Generating document...');
      
      // Detect document type
      const documentType = this.detectDocumentType(message);
      console.log('📄 Document type detected:', documentType);
      
      // Generate document content
      const document = this.generateDocumentContent(documentType, message);
      
      // Store the generated document
      this.generatedDocuments.push(document);
      this.saveGeneratedDocuments();
      
      // Increment counter
      this.documentsGenerated++;
      this.saveLimits();
      
      // Handle floating button
      if (this.documentsGenerated === 1) {
        // First document - create floating button after success message
        setTimeout(() => {
          this.createFloatingDocButton();
        }, 2000);
      } else {
        // Update existing button
        this.updateFloatingDocButton();
      }
      
      const remaining = this.getRemainingDocuments();
      
      let responseText = `✅ Document Generated Successfully!

I've created your ${this.getDocumentTypeName(documentType)}. You have ${remaining} more document${remaining === 1 ? '' : 's'} remaining in your demo.

📄 Ready to view your documents?`;
      
      if (remaining === 0) {
        responseText += `

🎯 Demo Complete: You've used all your demo documents! Ready for full access?`;
      }

      const quickReplies = ['📄 View My Documents'];
      
      if (remaining > 0) {
        quickReplies.push('Generate another document');
      }
      
      quickReplies.push('🚀 Request Full Access');
      
      return {
        response: responseText,
        quickReplies: quickReplies,
        success: true,
        remaining: remaining,
        documentsGenerated: true
      };
      
    } catch (error) {
      console.error('❌ Document generation failed:', error);
      return {
        response: `❌ Document Generation Failed

I encountered an error while generating your document: ${error.message}

Please try again with a different request, or contact support if the issue persists.`,
        quickReplies: ['Try different document type', 'Contact support', 'Return to chat'],
        success: false,
        isError: true
      };
    }
  },

  generateDocumentContent(type, originalMessage) {
    const templates = {
      'information_security_policy': {
        title: 'Information Security Policy',
        type: 'Policy Document',
        icon: '🛡️',
        content: `# INFORMATION SECURITY POLICY
[Company Name]
Version 1.0
Date: ${new Date().toLocaleDateString()}

## 1. INTRODUCTION

### 1.1 Purpose
This Information Security Policy establishes guidelines and principles for protecting [Company Name]'s information assets and technology infrastructure.

### 1.2 Scope
This policy applies to all employees, contractors, and third parties who access company information systems and data.

## 2. INFORMATION SECURITY OBJECTIVES

The primary objectives of this policy are to:
• Ensure the confidentiality, integrity, and availability of information assets
• Comply with applicable legal and regulatory requirements  
• Protect against security threats and vulnerabilities
• Maintain business continuity and minimize impact of security incidents

## 3. ROLES AND RESPONSIBILITIES

### 3.1 Information Security Officer
• Develop and maintain information security policies and procedures
• Conduct regular security assessments and reviews
• Coordinate incident response activities
• Provide security awareness training

### 3.2 Management
• Provide leadership and support for information security initiatives
• Ensure adequate resources are allocated for security measures
• Review and approve security policies and procedures

### 3.3 All Employees
• Comply with information security policies and procedures
• Report suspected security incidents immediately
• Participate in security awareness training
• Use information systems responsibly

## 4. INFORMATION CLASSIFICATION

Information assets shall be classified according to their sensitivity:

### 4.1 Public Information
• Information intended for public disclosure
• Marketing materials and public announcements
• Published research and reports

### 4.2 Internal Information  
• Internal communications and documentation
• Business plans and strategies (non-confidential portions)
• Internal training materials

### 4.3 Confidential Information
• Customer data and personal information
• Financial records and business plans
• Proprietary technology and trade secrets
• Legal and contractual information

### 4.4 Restricted Information
• Information subject to regulatory requirements
• Highly sensitive personal data
• Critical business continuity information

## 5. ACCESS CONTROL

### 5.1 User Access Management
• Access rights shall be granted based on business need and job requirements
• Regular access reviews shall be conducted quarterly
• Access shall be promptly revoked when no longer required

### 5.2 Authentication Requirements
• Strong passwords meeting complexity requirements
• Multi-factor authentication for privileged accounts
• Regular password changes as required

## 6. INCIDENT MANAGEMENT

### 6.1 Incident Response
All security incidents shall be:
• Reported immediately to the security team
• Investigated and documented thoroughly
• Addressed with appropriate corrective actions
• Reviewed for lessons learned and process improvements

### 6.2 Business Continuity
• Business continuity plans shall be maintained and tested
• Critical systems shall have defined recovery procedures
• Regular backups shall be performed and tested
• Alternative processing facilities identified when necessary

## 7. COMPLIANCE AND MONITORING

### 7.1 Compliance Requirements
[Company Name] shall comply with:
• Applicable laws and regulations
• Industry standards and best practices
• Contractual obligations with clients and partners
• Internal security policies and procedures

### 7.2 Monitoring and Review
• Regular security assessments and audits
• Continuous monitoring of security controls
• Management review of security performance
• Annual review and update of security policies

## 8. POLICY ENFORCEMENT

Violation of this policy may result in:
• Disciplinary action up to and including termination
• Legal action if warranted
• Financial liability for damages incurred

## 9. POLICY REVIEW

This policy shall be reviewed annually and updated as necessary to address:
• Changes in business operations
• New security threats and vulnerabilities
• Regulatory and compliance requirements
• Lessons learned from security incidents

---

**Policy Owner:** Chief Information Security Officer  
**Approved By:** [Name], CEO  
**Effective Date:** ${new Date().toLocaleDateString()}  
**Next Review:** ${new Date(Date.now() + 365*24*60*60*1000).toLocaleDateString()}

---
**DEMO VERSION - PREVIEW ONLY**
This is a sample policy generated for demonstration purposes. 
For fully customizable and downloadable documents, request full access.`
      },

      'risk_register': {
        title: 'Information Security Risk Register',
        type: 'Risk Assessment',
        icon: '📋',
        content: `# INFORMATION SECURITY RISK REGISTER
[Company Name]
Date: ${new Date().toLocaleDateString()}

## Risk Assessment Methodology
- **Probability Scale:** 1-5 (Very Low to Very High)
- **Impact Scale:** 1-5 (Minimal to Catastrophic)
- **Risk Level:** Probability × Impact

## IDENTIFIED RISKS

### HIGH PRIORITY RISKS (15-25)

**R001 - Unauthorized Access to Customer Data**
- **Asset:** Customer Database
- **Threat:** External Attackers, Malicious Insiders
- **Vulnerability:** Weak authentication mechanisms
- **Probability:** 4 | **Impact:** 5 | **Risk Score:** 20
- **Current Controls:** Multi-factor authentication, Firewall protection
- **Residual Risk:** High
- **Treatment Plan:** Implement zero-trust architecture, Enhanced monitoring
- **Owner:** IT Security Manager
- **Status:** In Progress
- **Target Date:** ${new Date(Date.now() + 90*24*60*60*1000).toLocaleDateString()}

**R002 - Ransomware Attack**
- **Asset:** All IT Systems
- **Threat:** Malicious Software
- **Vulnerability:** Outdated systems, User susceptibility
- **Probability:** 3 | **Impact:** 5 | **Risk Score:** 15
- **Current Controls:** Antivirus software, Email filtering, User training
- **Residual Risk:** High
- **Treatment Plan:** Advanced endpoint protection, Improved backup strategy
- **Owner:** IT Operations Manager
- **Status:** Planned
- **Target Date:** ${new Date(Date.now() + 60*24*60*60*1000).toLocaleDateString()}

### MEDIUM PRIORITY RISKS (6-14)

**R003 - Data Loss Due to Hardware Failure**
- **Asset:** File Servers, Workstations
- **Threat:** Equipment Failure
- **Vulnerability:** Aging hardware, Inadequate backup frequency
- **Probability:** 3 | **Impact:** 3 | **Risk Score:** 9
- **Current Controls:** Daily backups, RAID configuration
- **Residual Risk:** Medium
- **Treatment Plan:** Hardware refresh cycle, Cloud backup implementation
- **Owner:** IT Infrastructure Team
- **Status:** In Progress
- **Target Date:** ${new Date(Date.now() + 120*24*60*60*1000).toLocaleDateString()}

**R004 - Phishing and Social Engineering**
- **Asset:** Employee Credentials, Sensitive Information
- **Threat:** Cybercriminals
- **Vulnerability:** Human factor, Insufficient awareness
- **Probability:** 4 | **Impact:** 2 | **Risk Score:** 8
- **Current Controls:** Email filtering, Security awareness training
- **Residual Risk:** Medium
- **Treatment Plan:** Enhanced training program, Simulation exercises
- **Owner:** HR and IT Security
- **Status:** Ongoing
- **Target Date:** ${new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString()}

### LOW PRIORITY RISKS (1-5)

**R005 - Physical Theft of Equipment**
- **Asset:** Laptops, Mobile Devices
- **Threat:** Theft
- **Vulnerability:** Mobile workforce, Inadequate physical security
- **Probability:** 2 | **Impact:** 2 | **Risk Score:** 4
- **Current Controls:** Device encryption, Remote wipe capability
- **Residual Risk:** Low
- **Treatment Plan:** Enhanced physical security measures, GPS tracking
- **Owner:** Facilities Manager
- **Status:** Accepted
- **Target Date:** N/A

## RISK TREATMENT SUMMARY

**Avoid:** 0 risks
**Mitigate:** 4 risks (R001, R002, R003, R004)
**Transfer:** 0 risks
**Accept:** 1 risk (R005)

## MONITORING AND REVIEW

This risk register will be reviewed quarterly and updated as necessary.

**Next Review Date:** ${new Date(Date.now() + 90*24*60*60*1000).toLocaleDateString()}

---
**DEMO VERSION - PREVIEW ONLY**
This is a sample risk register for demonstration purposes.
For complete risk assessment and customized register, request full access.`
      },

      'soa': {
        title: 'Statement of Applicability (ISO 27001)',
        type: 'ISO 27001 Document',
        icon: '📊',
        content: `# STATEMENT OF APPLICABILITY
ISO 27001:2022 Annex A Controls
[Company Name]
Date: ${new Date().toLocaleDateString()}

## INTRODUCTION

This Statement of Applicability (SoA) defines which controls from ISO 27001:2022 Annex A are applicable to [Company Name]'s Information Security Management System.

## CONTROL ASSESSMENT SUMMARY

**Total Controls:** 93
**Applicable and Implemented:** 45
**Applicable and Partially Implemented:** 15  
**Applicable and Planned:** 12
**Not Applicable:** 21

## SELECTED CONTROLS

### A.8 TECHNOLOGY CONTROLS

**A.8.1 User endpoint devices**
- **Status:** ✅ APPLICABLE
- **Implementation Status:** IMPLEMENTED
- **Justification:** All employees use endpoint devices requiring protection
- **Implementation Details:** Endpoint protection software, Device encryption, Remote management
- **Responsible:** IT Operations Team

**A.8.2 Privileged access rights**
- **Status:** ✅ APPLICABLE  
- **Implementation Status:** PARTIALLY IMPLEMENTED
- **Justification:** Privileged accounts exist and need proper management
- **Implementation Details:** Basic privileged account management, PAM solution planned
- **Responsible:** IT Security Team

**A.8.3 Information access restriction**
- **Status:** ✅ APPLICABLE
- **Implementation Status:** IMPLEMENTED
- **Justification:** Need-to-know principle essential for data protection
- **Implementation Details:** Role-based access control (RBAC) implemented
- **Responsible:** IT Operations

**A.8.4 Access to source code**
- **Status:** ✅ APPLICABLE
- **Implementation Status:** IMPLEMENTED
- **Justification:** Source code is critical intellectual property
- **Implementation Details:** Version control system with access logging
- **Responsible:** Development Team Lead

**A.8.5 Secure authentication**
- **Status:** ✅ APPLICABLE
- **Implementation Status:** IMPLEMENTED
- **Justification:** Strong authentication prevents unauthorized access
- **Implementation Details:** Multi-factor authentication for critical systems
- **Responsible:** IT Security

**A.8.6 Capacity management**
- **Status:** ✅ APPLICABLE
- **Implementation Status:** IMPLEMENTED
- **Justification:** Ensures system availability and performance
- **Implementation Details:** Automated monitoring and alerting systems
- **Responsible:** Infrastructure Team

### CONTROLS NOT APPLICABLE

**A.7.4 Equipment maintenance**
- **Status:** ❌ NOT APPLICABLE
- **Justification:** Cloud-based infrastructure, physical equipment maintenance handled by service providers
- **Responsible:** N/A

## IMPLEMENTATION SUMMARY

**Total Controls Assessed:** 93
- **Applicable and Implemented:** 45
- **Applicable and Partially Implemented:** 15  
- **Applicable and Planned:** 12
- **Not Applicable:** 21

**Implementation Priority:**
1. **High Priority:** Complete partially implemented controls
2. **Medium Priority:** Implement planned controls  
3. **Low Priority:** Enhance existing controls

**Next Review Date:** ${new Date(Date.now() + 180*24*60*60*1000).toLocaleDateString()}

---
**DEMO VERSION - PREVIEW ONLY**
This is a sample SOA for demonstration purposes.
For complete control assessment and customized SOA, request full access.`
      },

      'gdpr_document': {
        title: 'GDPR Compliance Documentation',
        type: 'Data Protection',
        icon: '🔐',
        content: `# GDPR COMPLIANCE DOCUMENTATION
General Data Protection Regulation (EU) 2016/679
[Company Name]
Date: ${new Date().toLocaleDateString()}

## 1. DATA PROCESSING OVERVIEW

### 1.1 Data Controller Information
**Company:** [Company Name]
**Address:** [Company Address]
**Data Protection Officer:** [DPO Name]
**Contact:** <EMAIL>

### 1.2 Processing Activities Summary
This document outlines our data processing activities and GDPR compliance measures.

## 2. LAWFUL BASIS FOR PROCESSING

### 2.1 Article 6 Legal Bases
- **Consent (6(1)(a)):** Marketing communications, Optional services
- **Contract (6(1)(b)):** Service delivery, Customer management  
- **Legal Obligation (6(1)(c)):** Tax records, Legal compliance
- **Vital Interests (6(1)(d)):** Emergency situations
- **Public Task (6(1)(e)):** Not applicable to our organization
- **Legitimate Interests (6(1)(f)):** Business operations, Fraud prevention

### 2.2 Special Category Data (Article 9)
Limited processing of special category data with explicit consent and additional safeguards.

## 3. DATA SUBJECT RIGHTS IMPLEMENTATION

### 3.1 Right to Information (Articles 13-14)
- ✅ Privacy notices at point of collection
- ✅ Clear information about processing purposes
- ✅ Contact details provided for DPO
- ✅ Information about data retention periods

### 3.2 Right of Access (Article 15)
- ✅ Subject Access Request (SAR) procedure
- ✅ Response within one month
- ✅ Identity verification process
- ✅ Free provision of information

### 3.3 Right to Rectification (Article 16)
- ✅ Process to correct inaccurate data
- ✅ Notification to third parties when applicable
- ✅ Response timeframe: one month

### 3.4 Right to Erasure (Article 17)
- ✅ "Right to be forgotten" procedures
- ✅ Assessment of grounds for retention
- ✅ Secure deletion methods
- ✅ Third-party notification process

### 3.5 Right to Restrict Processing (Article 18)
- ✅ Temporary processing suspension capability
- ✅ Clear marking of restricted data
- ✅ Limited processing scenarios defined

### 3.6 Right to Data Portability (Article 20)
- ✅ Structured data export functionality
- ✅ Machine-readable format provision
- ✅ Direct transfer capability where possible

### 3.7 Right to Object (Article 21)
- ✅ Objection handling procedures
- ✅ Marketing opt-out mechanisms
- ✅ Profiling objection rights

## 4. TECHNICAL AND ORGANIZATIONAL MEASURES

### 4.1 Security Measures (Article 32)

**Technical Measures:**
- ✅ Encryption of personal data at rest and in transit
- ✅ Multi-factor authentication for system access
- ✅ Regular security updates and patches
- ✅ Access logging and monitoring
- ✅ Backup and recovery procedures

**Organizational Measures:**
- ✅ Staff training on data protection
- ✅ Data protection policies and procedures
- ✅ Incident response plan
- ✅ Regular security assessments
- ✅ Vendor management and contracts

### 4.2 Data Protection by Design and Default (Article 25)

**Privacy by Design Implementation:**
- ✅ Data minimization in system design
- ✅ Purpose limitation in data collection
- ✅ Privacy settings default to most protective
- ✅ Regular privacy impact assessments

## 5. DATA TRANSFERS

### 5.1 International Transfers (Chapter V)

**Third Country Transfers:**
- **Adequacy Decisions:** UK (adequate), Switzerland (adequate)
- **Standard Contractual Clauses:** US cloud providers
- **Transfer Impact Assessments:** Completed for all transfers

### 5.2 Processor Agreements (Article 28)

**Key Processors:**
- Cloud hosting provider - AWS (Ireland)
- Email service - Microsoft 365
- Analytics - Google Analytics (anonymized IP)

All processors have signed Data Processing Agreements (DPAs).

## 6. DATA PROTECTION IMPACT ASSESSMENTS

### 6.1 DPIA Requirements (Article 35)

**Completed DPIAs:**
- Employee monitoring system (2024)
- Customer profiling for marketing (2024)
- Biometric access control system (2023)

**DPIA Threshold Criteria:**
- High risk to rights and freedoms
- Large-scale processing
- Special category data
- Automated decision-making

## 7. BREACH NOTIFICATION

### 7.1 Notification Procedures (Articles 33-34)

**Supervisory Authority Notification:**
- ✅ 72-hour notification process
- ✅ Breach assessment criteria
- ✅ Documentation requirements

**Data Subject Notification:**
- ✅ High risk threshold assessment
- ✅ Clear communication procedures
- ✅ Mitigation advice provision

### 7.2 Breach Register
All breaches are logged with:
- Nature and categories of data
- Number of individuals affected
- Consequences and mitigation measures
- Notification actions taken

## 8. ACCOUNTABILITY MEASURES

### 8.1 Compliance Documentation

**Records of Processing (Article 30):**
- ✅ Complete processing inventory maintained
- ✅ Regular updates and reviews
- ✅ Available for supervisory authority

**Training and Awareness:**
- ✅ Annual GDPR training for all staff
- ✅ Specialized training for data handlers
- ✅ Privacy awareness campaigns

**Audits and Reviews:**
- ✅ Annual compliance audit
- ✅ Quarterly policy reviews
- ✅ Continuous monitoring program

## 9. CONTACT AND COMPLAINTS

**Data Protection Officer:** [DPO Name]
**Email:** <EMAIL>
**Phone:** [Phone Number]

**Supervisory Authority:** [National DPA]
**Website:** [DPA Website]

---
**Next Review Date:** ${new Date(Date.now() + 365*24*60*60*1000).toLocaleDateString()}
**Document Owner:** Data Protection Officer

---
**DEMO VERSION - PREVIEW ONLY**
This is a sample GDPR documentation for demonstration purposes.
For complete GDPR compliance assessment and documentation, request full access.`
      },

      'general_document': {
        title: 'Compliance Document',
        type: 'Generated Document',
        icon: '📄',
        content: `# COMPLIANCE DOCUMENT
Generated based on your request
[Company Name]
Date: ${new Date().toLocaleDateString()}

## DOCUMENT PURPOSE

This document has been generated to address your compliance requirements based on your request: "${originalMessage}"

## COMPLIANCE FRAMEWORK

### Regulatory Considerations
• Industry-specific requirements
• International standards alignment  
• Best practice implementation
• Risk-based approach

### Implementation Approach
• Assess current state
• Identify gaps and requirements
• Develop implementation roadmap
• Monitor and review effectiveness

### Key Compliance Areas
• Policies and procedures
• Training and awareness
• Monitoring and reporting
• Continuous improvement

## RECOMMENDATIONS

Based on your request, we recommend:
• Conducting a detailed compliance assessment
• Developing specific policies and procedures
• Implementing appropriate controls
• Regular monitoring and review

## NEXT STEPS

1. **Assessment:** Evaluate current compliance posture
2. **Planning:** Develop detailed implementation plan
3. **Implementation:** Execute compliance measures
4. **Monitoring:** Ongoing compliance monitoring

---
**DEMO VERSION - PREVIEW ONLY**
This is a basic document template. For detailed, customized compliance documentation specific to your industry and requirements, request full access to ArionComply.

With full access, you'll receive:
• Industry-specific compliance frameworks
• Detailed policy templates
• Implementation guidance
• Ongoing support and updates`
      }
    };

    const template = templates[type] || templates['general_document'];
    
    return {
      id: `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      title: template.title,
      type: template.type,
      icon: template.icon,
      content: template.content,
      createdAt: new Date().toISOString(),
      requestMessage: originalMessage,
      documentType: type
    };
  },

  getDocumentTypeName(type) {
    const names = {
      'information_security_policy': 'Information Security Policy',
      'risk_register': 'Risk Register',
      'soa': 'Statement of Applicability',
      'gdpr_document': 'GDPR Compliance Documentation',
      'audit_report': 'Audit Report',
      'dpia': 'Data Protection Impact Assessment',
      'gap_analysis': 'Gap Analysis',
      'general_document': 'Compliance Document'
    };
    return names[type] || 'Document';
  },

  // =============================================================================
  // DOCUMENT VIEWING
  // =============================================================================

  viewDocuments() {
    console.log('📄 Opening document viewer...');
    
    if (this.generatedDocuments.length === 0) {
      alert('No documents generated yet. Try asking me to generate a document first!');
      return;
    }
    
    // Create enhanced modal with document list and content
    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      padding: 2rem;
      box-sizing: border-box;
    `;
    
    modal.innerHTML = `
      <div style="background: white; border-radius: 12px; width: 100%; max-width: 1200px; max-height: 90vh; display: flex; flex-direction: column; overflow: hidden; box-shadow: 0 20px 40px rgba(0,0,0,0.3);">
        
        <!-- Header -->
        <div style="padding: 2rem; border-bottom: 1px solid #e5e7eb; background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);">
          <h2 style="margin: 0 0 0.5rem 0; color: #111827; display: flex; align-items: center; gap: 0.5rem;">
            📄 Your Generated Documents
          </h2>
          <p style="margin: 0; color: #6b7280;">
            You've generated <strong>${this.documentsGenerated}</strong> document${this.documentsGenerated === 1 ? '' : 's'} • 
            <span style="color: #059669; font-weight: bold;">🔒 PREVIEW ONLY MODE</span>
          </p>
        </div>

        <!-- Content Area -->
        <div style="display: flex; flex: 1; overflow: hidden;">
          
          <!-- Document List Sidebar -->
          <div style="width: 350px; border-right: 1px solid #e5e7eb; background: #f9fafb; overflow-y: auto;">
            <div style="padding: 1.5rem;">
              <h3 style="margin: 0 0 1rem 0; font-size: 0.875rem; font-weight: 600; text-transform: uppercase; color: #6b7280; letter-spacing: 0.05em;">Documents</h3>
              ${this.generatedDocuments.map((doc, index) => `
                <div class="doc-item" onclick="showDocContent('${doc.id}')" style="
                  padding: 1rem;
                  border: 1px solid #e5e7eb;
                  border-radius: 8px;
                  margin-bottom: 0.75rem;
                  cursor: pointer;
                  transition: all 0.2s;
                  background: white;
                " onmouseover="this.style.borderColor='#059669'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(5,150,105,0.15)'" onmouseout="this.style.borderColor='#e5e7eb'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                  <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                    <span style="font-size: 1.25rem;">${doc.icon}</span>
                    <div style="font-weight: 600; color: #111827; font-size: 0.9rem; line-height: 1.3;">${doc.title}</div>
                  </div>
                  <div style="font-size: 0.75rem; color: #6b7280; margin-bottom: 0.5rem;">${doc.type}</div>
                  <div style="font-size: 0.7rem; color: #9ca3af; margin-bottom: 0.5rem;">Generated: ${new Date(doc.createdAt).toLocaleDateString()}</div>
                  <div style="font-size: 0.75rem; color: #059669; font-weight: 500; display: flex; align-items: center; gap: 0.25rem;">
                    🔒 Preview Only
                  </div>
                </div>
              `).join('')}
            </div>
          </div>

          <!-- Document Content Area -->
          <div style="flex: 1; display: flex; flex-direction: column; overflow: hidden;">
            <div id="docContentArea" style="flex: 1; padding: 2rem; overflow-y: auto; position: relative; font-family: 'Courier New', monospace; line-height: 1.6; background: #fafafa;">
              <div style="text-align: center; color: #9ca3af; padding: 4rem 2rem;">
                <div style="font-size: 3rem; margin-bottom: 1rem;">📄</div>
                <h3 style="margin-bottom: 0.5rem; color: #6b7280;">Select a document to view</h3>
                <p style="color: #9ca3af;">Click on a document from the left sidebar to view its contents in preview mode.</p>
              </div>
              
              <!-- Watermark (hidden initially) -->
              <div id="watermark" style="
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%) rotate(-45deg);
                font-size: 4rem;
                font-weight: bold;
                color: rgba(5, 150, 105, 0.1);
                pointer-events: none;
                white-space: nowrap;
                display: none;
                user-select: none;
              ">PREVIEW ONLY</div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div style="padding: 1.5rem 2rem; border-top: 1px solid #e5e7eb; background: #f9fafb; display: flex; justify-content: space-between; align-items: center;">
          <div style="font-size: 0.875rem; color: #6b7280; display: flex; align-items: center; gap: 0.5rem;">
            <span>📋</span>
            <strong>Demo Mode:</strong> Documents are preview-only. No download or copy features available.
          </div>
          <div style="display: flex; gap: 1rem;">
            <button onclick="this.closest('div').parentElement.parentElement.remove(); delete window.showDocContent;" style="
              padding: 0.75rem 1rem;
              border: 1px solid #d1d5db;
              background: white;
              border-radius: 6px;
              cursor: pointer;
              font-size: 0.875rem;
              color: #374151;
              transition: all 0.2s;
            " onmouseover="this.style.background='#f9fafb'" onmouseout="this.style.background='white'">Close</button>
            <button onclick="alert('🚀 Request full access to unlock:\\n\\n• Unlimited document generation\\n• Download & export capabilities\\n• Custom document templates\\n• Advanced editing tools\\n• Priority support\\n\\nContact us to upgrade your account!'); this.closest('div').parentElement.parentElement.remove(); delete window.showDocContent;" style="
              padding: 0.75rem 1rem;
              background: #059669;
              color: white;
              border: none;
              border-radius: 6px;
              cursor: pointer;
              font-size: 0.875rem;
              font-weight: 500;
              transition: all 0.2s;
            " onmouseover="this.style.background='#047857'" onmouseout="this.style.background='#059669'">🚀 Request Full Access</button>
          </div>
        </div>
      </div>
    `;
    
    // Add the modal to the page
    document.body.appendChild(modal);
    
    // Add global function to show document content
    window.showDocContent = (docId) => {
      const doc = this.generatedDocuments.find(d => d.id === docId);
      if (!doc) return;
      
      const contentArea = document.getElementById('docContentArea');
      const watermark = document.getElementById('watermark');
      
      if (contentArea && watermark) {
        // Show document content with copy protection
        contentArea.innerHTML = `
          <div style="
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            white-space: pre-wrap;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            position: relative;
          " oncontextmenu="return false;" ondragstart="return false;" onselectstart="return false;">
            <div style="margin-bottom: 2rem; padding: 1rem; background: #fef3c7; border: 1px solid #f59e0b; border-radius: 6px; text-align: center;">
              <div style="font-weight: 600; color: #92400e; margin-bottom: 0.5rem;">🔒 PREVIEW ONLY MODE</div>
              <div style="font-size: 0.875rem; color: #92400e;">This document is in preview mode. Text selection, copying, and downloading are disabled in the demo.</div>
            </div>
            ${doc.content.replace(/\n/g, '\n')}
          </div>
        `;
        
        // Show watermark
        watermark.style.display = 'block';
        
        // Add comprehensive copy protection
        const protectedContent = contentArea.querySelector('div');
        if (protectedContent) {
          // Prevent keyboard shortcuts
          protectedContent.addEventListener('keydown', function(e) {
            // Prevent Ctrl+A, Ctrl+C, Ctrl+S, Ctrl+P, F12, Ctrl+Shift+I
            if ((e.ctrlKey && ['a', 'c', 's', 'p'].includes(e.key.toLowerCase())) || 
                e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && ['i', 'j'].includes(e.key.toLowerCase()))) {
              e.preventDefault();
              e.stopPropagation();
              return false;
            }
          });
          
          // Prevent drag operations
          protectedContent.addEventListener('dragstart', function(e) {
            e.preventDefault();
            return false;
          });
          
          // Additional protection
          protectedContent.style.webkitTouchCallout = 'none';
          protectedContent.style.webkitUserSelect = 'none';
          protectedContent.style.khtmlUserSelect = 'none';
          protectedContent.style.mozUserSelect = 'none';
          protectedContent.style.msUserSelect = 'none';
        }
      }
    };
    
    // Remove modal when clicking outside
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
        delete window.showDocContent;
      }
    });
  },

  // =============================================================================
  // UTILITY METHODS
  // =============================================================================

  getStatus() {
    return {
      documentsGenerated: this.documentsGenerated,
      remainingDocuments: this.getRemainingDocuments(),
      hasReachedLimit: this.hasReachedLimit(),
      maxDemoDocuments: this.maxDemoDocuments,
      generatedDocuments: this.generatedDocuments.length,
      sessionId: this.sessionId,
      floatingButtonVisible: !document.getElementById('floatingDocBtn')
    };
  },

  reset() {
    this.documentsGenerated = 0;
    this.generatedDocuments = [];
    this.userInitiatedSession = false; // NEW: Reset user session flag
    this.saveLimits();
    this.saveGeneratedDocuments();
    
    // Remove floating button when reset
    this.removeFloatingDocButton();
    
    console.log('📄 Document generator reset');
  }
};

// Initialize
DocumentGenerator.init();

// Make globally available
window.DocumentGenerator = DocumentGenerator;

// NEW: Add function to mark user session as initiated (call this from sendMessage in demo.html)
window.markUserSessionInitiated = () => {
  if (window.DocumentGenerator) {
    window.DocumentGenerator.userInitiatedSession = true;
    console.log('✅ User session marked as initiated');
  }
};

// Debug functions
window.testDocGenerator = () => {
  console.log('📊 DocumentGenerator Status:', DocumentGenerator.getStatus());
  console.log('🧪 Testing detection:', DocumentGenerator.isDocumentRequest('Generate a policy'));
  console.log('📄 Generated documents:', DocumentGenerator.generatedDocuments);
};

window.resetDocLimits = () => {
  DocumentGenerator.reset();
};

window.testDocumentGeneration = async () => {
  console.log('🧪 Testing document generation...');
  const result = await DocumentGenerator.handleDocumentRequest('Generate an Information Security Policy', 'test-session');
  console.log('📄 Result:', result);
};

window.testFloatingButton = () => {
  console.log('🧪 Testing floating button...');
  if (DocumentGenerator.documentsGenerated > 0) {
    DocumentGenerator.createFloatingDocButton();
    console.log('✅ Button should now be visible');
  } else {
    console.log('❌ No documents generated yet');
  }
};

window.removeFloatingButton = () => {
  DocumentGenerator.removeFloatingDocButton();
  console.log('🗑️ Floating button removed');
};

console.log('📄 Complete DocumentGenerator with Floating Button loaded!');
console.log('🧪 Try: testDocGenerator(), resetDocLimits(), testFloatingButton()');
console.log('✅ NEW: User session protection added - documents will only generate after user interaction');
console.log('✅ PRESERVED: Your original viewDocuments() modal functionality');
