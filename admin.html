<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ArionComply Admin - Management Dashboard</title>
  <style>
    :root {
      --brand-primary: #059669;
      --brand-secondary: #047857;
      --neutral-50: #f8fafc;
      --neutral-100: #f1f5f9;
      --neutral-200: #e2e8f0;
      --neutral-600: #475569;
      --neutral-700: #334155;
      --error: #ef4444;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--neutral-50);
      color: var(--neutral-700);
    }

    /* LOGIN STYLES */
    .login-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background: var(--neutral-50);
      padding: 2rem;
    }

    .login-card {
      background: white;
      padding: 2rem;
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 400px;
    }

    .login-card h2 {
      text-align: center;
      color: var(--brand-primary);
      margin-bottom: 2rem;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    .form-group label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 600;
    }

    .form-group input {
      width: 100%;
      padding: 0.75rem;
      border: 2px solid var(--neutral-200);
      border-radius: 8px;
      font-size: 1rem;
      box-sizing: border-box;
    }

    .form-group input:focus {
      outline: none;
      border-color: var(--brand-primary);
    }

    .mfa-info {
      background: var(--neutral-100);
      padding: 1rem;
      border-radius: 8px;
      margin-bottom: 1rem;
      text-align: center;
      color: var(--neutral-600);
    }

    /* DASHBOARD STYLES */
    .dashboard-container {
      padding: 2rem;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
    }

    .nav-tabs {
      display: flex;
      gap: 1rem;
      margin-bottom: 2rem;
      border-bottom: 2px solid var(--neutral-200);
    }

    .nav-tab {
      padding: 1rem 2rem;
      background: none;
      border: none;
      color: var(--neutral-600);
      cursor: pointer;
      font-weight: 600;
      border-bottom: 3px solid transparent;
      transition: all 0.2s;
    }

    .nav-tab.active {
      color: var(--brand-primary);
      border-bottom-color: var(--brand-primary);
    }

    .nav-tab:hover:not(.active) {
      color: var(--neutral-700);
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 8px;
      background: var(--brand-primary);
      color: white;
      cursor: pointer;
      font-weight: 600;
      margin-right: 1rem;
      transition: background 0.2s ease;
    }

    .btn:hover {
      background: var(--brand-secondary);
    }

    .btn-secondary {
      background: var(--neutral-200);
      color: var(--neutral-700);
    }

    .btn-secondary:hover {
      background: var(--neutral-300);
    }

    .btn-small {
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
      margin: 0.25rem;
    }

    .btn-success {
      background: #10b981;
    }

    .btn-success:hover {
      background: #059669;
    }

    .btn-warning {
      background: #f59e0b;
    }

    .btn-warning:hover {
      background: #d97706;
    }

    .btn-danger {
      background: #ef4444;
    }

    .btn-danger:hover {
      background: #dc2626;
    }

    .stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .stat-card {
      background: white;
      padding: 1.5rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .stat-number {
      font-size: 2rem;
      font-weight: bold;
      color: var(--brand-primary);
    }

    .data-table {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th,
    td {
      padding: 1rem;
      text-align: left;
      border-bottom: 1px solid #eee;
    }

    th {
      background: var(--neutral-50);
      font-weight: 600;
    }

    /* Status badges */
    .status-active {
      background: #10b981;
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.875rem;
    }

    .status-pending {
      background: #f59e0b;
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.875rem;
    }

    .status-suspended {
      background: #6b7280;
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.875rem;
    }

    .status-expired {
      background: #ef4444;
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.875rem;
    }

    .qualification-sql {
      background: #10b981;
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.875rem;
    }

    .qualification-mql {
      background: #3b82f6;
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.875rem;
    }

    .qualification-lead {
      background: #f59e0b;
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.875rem;
    }

    .qualification-suspect {
      background: #6b7280;
      color: white;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.875rem;
    }

    .loading {
      text-align: center;
      padding: 2rem;
    }

    .error {
      color: var(--error);
      padding: 1rem;
      background: #fef2f2;
      border-radius: 8px;
      margin-bottom: 1rem;
    }

    .success-message {
      color: #166534;
      padding: 1rem;
      background: #f0fdf4;
      border-radius: 8px;
      margin-bottom: 1rem;
      border: 1px solid #bbf7d0;
    }

    /* RESPONSIVE */
    @media (max-width: 768px) {
      .login-container {
        padding: 1rem;
      }

      .dashboard-container {
        padding: 1rem;
      }

      .header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
      }

      .header>div {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
      }

      .btn {
        margin-right: 0;
        flex: 1;
        text-align: center;
      }

      .nav-tabs {
        flex-direction: column;
      }

      .nav-tab {
        text-align: center;
      }
    }
  </style>
</head>

<body>
  <!-- LOGIN SECTION (initially visible) -->
  <div id="loginSection" class="login-container">
    <div class="login-card">
      <h2>🔒 ArionComply Admin Access</h2>

      <!-- Step 1: Email + Password -->
      <div id="loginStep">
        <form id="loginForm">
          <div class="form-group">
            <label for="adminEmail">Admin Email:</label>
            <input type="email" id="adminEmail" required autocomplete="email">
          </div>
          <div class="form-group">
            <label for="adminPassword">Password:</label>
            <input type="password" id="adminPassword" required autocomplete="current-password">
          </div>
          <button type="submit" class="btn" style="width: 100%;">Continue to MFA</button>
        </form>
      </div>

      <!-- Step 2: MFA Code -->
      <div id="mfaStep" style="display: none;">
        <div class="mfa-info">
          <p>📧 Security code sent to<br><strong id="adminEmailDisplay">your email</strong></p>
          <p><small>Check your email and enter the 6-digit code below</small></p>
        </div>
        <form id="mfaForm">
          <div class="form-group">
            <label for="mfaCode">Enter 6-digit code:</label>
            <input type="text" id="mfaCode" maxlength="6" required autocomplete="one-time-code" pattern="[0-9]{6}"
              placeholder="123456">
          </div>
          <div style="display: flex; gap: 1rem;">
            <button type="button" class="btn btn-secondary" onclick="goBackToPassword()" style="flex: 1;">Back</button>
            <button type="submit" class="btn" style="flex: 1;">Login</button>
          </div>
        </form>
        <div style="text-align: center; margin-top: 1rem;">
          <button type="button" class="btn btn-secondary" onclick="resendCode()"
            style="font-size: 0.875rem; padding: 0.5rem 1rem;">Resend Code</button>
        </div>
      </div>

      <!-- Error Display -->
      <div id="loginError" class="error" style="display: none;"></div>
    </div>
  </div>

  <!-- DASHBOARD SECTION (initially hidden) -->
  <div id="dashboardSection" class="dashboard-container" style="display: none;">
    <div class="container">
      <div class="header">
        <h1>ArionComply Admin Dashboard</h1>
        <div>
          <button class="btn" onclick="refreshCurrentTab()">Refresh</button>
          <button class="btn" onclick="cleanupData()">Cleanup Data</button>
          <button class="btn btn-secondary" onclick="logout()">Logout</button>
        </div>
      </div>

      <!-- Navigation Tabs -->
      <div class="nav-tabs">
        <button class="nav-tab active" onclick="switchTab('users')">👥 User Management</button>
        <button class="nav-tab" onclick="switchTab('leads')">📊 Lead Management</button>
      </div>

      <!-- Error Display -->
      <div class="error" id="error" style="display: none;"></div>

      <!-- USER MANAGEMENT TAB -->
      <div id="usersTab" class="tab-content active">
        <div class="stats" id="userStats">
          <div class="stat-card">
            <div class="stat-number" id="totalUsers">-</div>
            <div>Total Users</div>
          </div>
          <div class="stat-card">
            <div class="stat-number" id="activeUsers">-</div>
            <div>Active Users</div>
          </div>
          <div class="stat-card">
            <div class="stat-number" id="pendingUsers">-</div>
            <div>Pending Users</div>
          </div>
          <div class="stat-card">
            <div class="stat-number" id="suspendedUsers">-</div>
            <div>Suspended Users</div>
          </div>
        </div>

        <div class="data-table">
          <div class="loading" id="usersLoading">Loading users...</div>
          <table id="usersTable" style="display: none;">
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Company</th>
                <th>Job Title</th>
                <th>Status</th>
                <th>Type</th>
                <th>Created</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="usersTableBody">
            </tbody>
          </table>
        </div>
      </div>

      <!-- LEAD MANAGEMENT TAB -->
      <div id="leadsTab" class="tab-content">
        <div class="stats" id="leadStats">
          <div class="stat-card">
            <div class="stat-number" id="totalLeads">-</div>
            <div>Total Leads</div>
          </div>
          <div class="stat-card">
            <div class="stat-number" id="sqlLeads">-</div>
            <div>SQL Leads</div>
          </div>
          <div class="stat-card">
            <div class="stat-number" id="mqlLeads">-</div>
            <div>MQL Leads</div>
          </div>
          <div class="stat-card">
            <div class="stat-number" id="todayLeads">-</div>
            <div>Today's Leads</div>
          </div>
        </div>

        <div style="margin-bottom: 1rem;">
          <button class="btn" onclick="exportCSV()">Export CSV</button>
          <button class="btn" onclick="exportJSON()">Export JSON</button>
        </div>

        <div class="data-table">
          <div class="loading" id="leadsLoading">Loading leads...</div>
          <table id="leadsTable" style="display: none;">
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Company</th>
                <th>Job Title</th>
                <th>Score</th>
                <th>Qualification</th>
                <th>Frameworks</th>
                <th>Company Size</th>
                <th>Created</th>
              </tr>
            </thead>
            <tbody id="leadsTableBody">
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Admin authentication state
    let isAdminAuthenticated = false;
    let adminSessionToken = null;
    let users = [];
    let leads = [];
    let currentTab = 'users';

    // Check for existing admin session on page load
    async function checkAdminSession() {
      const token = localStorage.getItem('arion-admin-token');
      if (token) {
        try {
          const response = await ArionUtils.api.call('verify-admin-session', { token });
          if (response.success) {
            adminSessionToken = token;
            showDashboard();
            return;
          }
        } catch (error) {
          console.log('Admin session expired, requiring new login');
          localStorage.removeItem('arion-admin-token');
        }
      }
      showLogin();
    }

    // Show/hide sections
    function showLogin() {
      document.getElementById('loginSection').style.display = 'flex';
      document.getElementById('dashboardSection').style.display = 'none';
      // Reset form states
      document.getElementById('loginStep').style.display = 'block';
      document.getElementById('mfaStep').style.display = 'none';
      document.getElementById('loginError').style.display = 'none';
      document.getElementById('adminEmail').value = '';
      document.getElementById('adminPassword').value = '';
      document.getElementById('mfaCode').value = '';
    }

    function showDashboard() {
      document.getElementById('loginSection').style.display = 'none';
      document.getElementById('dashboardSection').style.display = 'block';
      isAdminAuthenticated = true;
      loadCurrentTab(); // Load the current tab data
    }

    // Navigation functions
    function goBackToLogin() {
      document.getElementById('loginStep').style.display = 'block';
      document.getElementById('mfaStep').style.display = 'none';
      document.getElementById('loginError').style.display = 'none';
    }

    async function resendCode() {
      const email = document.getElementById('adminEmail').value;           // ADD THIS
      const password = document.getElementById('adminPassword').value;
      if (!email || !password) {                                          // CHANGE THIS
        showLoginError('Email and password required to resend code');     // CHANGE THIS
        return;
      }

      try {
        const response = await ArionUtils.api.call('admin-request-code', {
          email,                                                           // ADD THIS
          password
        });
        if (response.success) {
          showLoginError(`New code sent to ${email}`, false);             // CHANGE THIS
        } else {
          showLoginError(response.error);
        }
      } catch (error) {
        showLoginError('Failed to resend code');
      }
    }

    function logout() {
      localStorage.removeItem('arion-admin-token');
      adminSessionToken = null;
      isAdminAuthenticated = false;
      showLogin();
    }

    // Tab Management
    function switchTab(tabName) {
      // Update tab buttons
      document.querySelectorAll('.nav-tab').forEach(tab => tab.classList.remove('active'));
      event.target.classList.add('active');

      // Update tab content
      document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
      document.getElementById(tabName + 'Tab').classList.add('active');

      currentTab = tabName;
      loadCurrentTab();
    }

    function loadCurrentTab() {
      if (currentTab === 'users') {
        loadUsers();
      } else if (currentTab === 'leads') {
        loadLeads();
      }
    }

    function refreshCurrentTab() {
      loadCurrentTab();
    }

    // Handle password form
    document.getElementById('loginForm').addEventListener('submit', async (e) => {
      e.preventDefault();
      const email = document.getElementById('adminEmail').value;
      const password = document.getElementById('adminPassword').value;

      if (!email || !password) {
        showLoginError('Please enter email and password');
        return;
      }

      try {
        document.querySelector('#loginForm button').textContent = 'Verifying...';
        document.querySelector('#loginForm button').disabled = true;

        // Use updated admin-request-code that validates against existing ADMIN_USERS
        const response = await ArionUtils.api.call('admin-request-code', {
          email,
          password
        });

        if (response.success) {
          // Update MFA display with individual admin's email
          document.getElementById('adminEmailDisplay').textContent = email;

          document.getElementById('loginStep').style.display = 'none';
          document.getElementById('mfaStep').style.display = 'block';
          document.getElementById('loginError').style.display = 'none';
          setTimeout(() => document.getElementById('mfaCode').focus(), 100);
        } else {
          showLoginError(response.error || 'Invalid credentials');
        }
      } catch (error) {
        console.error('Authentication failed:', error);
        showLoginError('Authentication failed. Please try again.');
      } finally {
        document.querySelector('#loginForm button').textContent = 'Continue to MFA';
        document.querySelector('#loginForm button').disabled = false;
      }
    });

    // Handle MFA form
    document.getElementById('mfaForm').addEventListener('submit', async (e) => {
      e.preventDefault();
      const code = document.getElementById('mfaCode').value;

      if (!code || code.length !== 6) {
        showLoginError('Please enter the 6-digit code');
        return;
      }

      try {
        document.querySelector('#mfaForm button[type="submit"]').textContent = 'Verifying...';
        document.querySelector('#mfaForm button[type="submit"]').disabled = true;

        const response = await ArionUtils.api.call('admin-verify-code', { code });
        if (response.success) {
          adminSessionToken = response.data.token;
          localStorage.setItem('arion-admin-token', adminSessionToken);
          showDashboard();
        } else {
          showLoginError(response.error || 'Invalid code');
        }
      } catch (error) {
        console.error('Code verification failed:', error);
        showLoginError('Code verification failed. Please try again.');
      } finally {
        document.querySelector('#mfaForm button[type="submit"]').textContent = 'Login';
        document.querySelector('#mfaForm button[type="submit"]').disabled = false;
      }
    });

    function showLoginError(message, isError = true) {
      const errorDiv = document.getElementById('loginError');
      errorDiv.textContent = message;
      errorDiv.style.display = 'block';
      errorDiv.style.backgroundColor = isError ? '#fef2f2' : '#f0f9ff';
      errorDiv.style.color = isError ? '#ef4444' : '#1d4ed8';

      if (!isError) {
        setTimeout(() => errorDiv.style.display = 'none', 5000);
      }
    }

    // USER MANAGEMENT FUNCTIONS
    async function loadUsers() {
      if (!adminSessionToken) {
        showLogin();
        return;
      }

      try {
        document.getElementById('usersLoading').style.display = 'block';
        document.getElementById('usersTable').style.display = 'none';
        document.getElementById('error').style.display = 'none';

        const response = await ArionUtils.api.call('get-users', {});

        if (response.success) {
          users = response.data.users;
          renderUsers();
          updateUserStats(response.data.summary);
        } else {
          throw new Error(response.error);
        }
      } catch (error) {
        console.error('Failed to load users:', error);

        if (error.message.includes('unauthorized') || error.message.includes('token')) {
          logout();
          return;
        }

        document.getElementById('error').textContent = 'Failed to load users: ' + error.message;
        document.getElementById('error').style.display = 'block';
      } finally {
        document.getElementById('usersLoading').style.display = 'none';
      }
    }

    function renderUsers() {
      const tbody = document.getElementById('usersTableBody');
      tbody.innerHTML = '';

      users.forEach(user => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${user.first_name} ${user.last_name}</td>
          <td>${user.email}</td>
          <td>${user.company_name || '-'}</td>
          <td>${user.job_title || '-'}</td>
          <td><span class="status-${user.status}">${user.status}</span></td>
          <td>${user.type}</td>
          <td>${new Date(user.created_at).toLocaleDateString()}</td>
          <td>
            ${generateUserActions(user)}
          </td>
        `;
        tbody.appendChild(row);
      });

      document.getElementById('usersTable').style.display = 'table';
    }

    function generateUserActions(user) {
      let actions = '';

      if (user.status === 'pending' || user.status === 'suspended') {
        actions += `<button class="btn btn-success btn-small" onclick="manageUser('${user.id}', 'activate', '${user.type}')">Activate</button>`;
      }

      if (user.status === 'active') {
        actions += `<button class="btn btn-warning btn-small" onclick="manageUser('${user.id}', 'suspend', '${user.type}')">Suspend</button>`;
      }

      actions += `<button class="btn btn-danger btn-small" onclick="confirmDeleteUser('${user.id}', '${user.email}', '${user.type}')">Delete</button>`;

      return actions;
    }

    function updateUserStats(summary) {
      document.getElementById('totalUsers').textContent = summary.total;
      document.getElementById('activeUsers').textContent = summary.active;
      document.getElementById('pendingUsers').textContent = summary.pending;
      document.getElementById('suspendedUsers').textContent = summary.suspended;
    }

    async function manageUser(userId, action, userType) {
      if (!adminSessionToken) {
        showLogin();
        return;
      }

      try {
        console.log(`🔧 Managing user ${userId}: ${action} (type: ${userType})`);

        const response = await ArionUtils.api.call('admin-manage-user', {
          userId: userId,
          userAction: action,
          userType: userType,
          adminToken: adminSessionToken
        });

        if (response.success) {
          console.log(`✅ User ${action} successful`);
          await loadUsers();
          showSuccessMessage(`User ${action} completed successfully`);
        } else {
          throw new Error(response.error);
        }
      } catch (error) {
        console.error(`❌ Failed to ${action} user:`, error);
        alert(`Failed to ${action} user: ${error.message}`);
      }
    }

    function confirmDeleteUser(userId, email, userType) {
      if (confirm(`Are you sure you want to delete user ${email}?\n\nThis action cannot be undone.`)) {
        manageUser(userId, 'delete', userType);
      }
    }

    // LEAD MANAGEMENT FUNCTIONS
    async function loadLeads() {
      if (!adminSessionToken) {
        showLogin();
        return;
      }

      try {
        document.getElementById('leadsLoading').style.display = 'block';
        document.getElementById('leadsTable').style.display = 'none';
        document.getElementById('error').style.display = 'none';

        const response = await ArionUtils.api.call('get-leads', {}, null);

        if (response.success) {
          leads = response.data.leads;
          renderLeads();
          updateLeadStats();
        } else {
          throw new Error(response.error);
        }
      } catch (error) {
        console.error('Failed to load leads:', error);

        if (error.message.includes('unauthorized') || error.message.includes('token')) {
          logout();
          return;
        }

        document.getElementById('error').textContent = 'Failed to load leads: ' + error.message;
        document.getElementById('error').style.display = 'block';
      } finally {
        document.getElementById('leadsLoading').style.display = 'none';
      }
    }

    function renderLeads() {
      const tbody = document.getElementById('leadsTableBody');
      tbody.innerHTML = '';

      leads.forEach(lead => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${lead.first_name} ${lead.last_name}</td>
          <td>${lead.email}</td>
          <td>${lead.company_name}</td>
          <td>${lead.job_title || '-'}</td>
          <td>${lead.lead_score || '-'}</td>
          <td><span class="qualification-${lead.lead_qualification_status || 'suspect'}">${lead.lead_qualification_status || 'Suspect'}</span></td>
          <td>${Array.isArray(lead.compliance_frameworks) ? lead.compliance_frameworks.join(', ') : lead.compliance_frameworks || '-'}</td>
          <td>${lead.company_size || '-'}</td>
          <td>${new Date(lead.created_at).toLocaleDateString()}</td>
        `;
        tbody.appendChild(row);
      });

      document.getElementById('leadsTable').style.display = 'table';
    }

    function updateLeadStats() {
      const totalLeads = leads.length;
      const sqlLeads = leads.filter(l => l.lead_qualification_status === 'sql').length;
      const mqlLeads = leads.filter(l => l.lead_qualification_status === 'mql').length;
      const today = new Date().toDateString();
      const todayLeads = leads.filter(l => new Date(l.created_at).toDateString() === today).length;

      document.getElementById('totalLeads').textContent = totalLeads;
      document.getElementById('sqlLeads').textContent = sqlLeads;
      document.getElementById('mqlLeads').textContent = mqlLeads;
      document.getElementById('todayLeads').textContent = todayLeads;
    }

    function exportCSV() {
      if (!leads.length) {
        alert('No leads to export');
        return;
      }

      const headers = ['Name', 'Email', 'Company', 'Job Title', 'Phone', 'Score', 'Qualification', 'Frameworks', 'Company Size', 'Industry', 'Timeline', 'Budget', 'Created'];

      const csvData = leads.map(lead => [
        `${lead.first_name} ${lead.last_name}`,
        lead.email,
        lead.company_name,
        lead.job_title || '',
        lead.phone_number || '',
        lead.lead_score || '',
        lead.lead_qualification_status || '',
        Array.isArray(lead.compliance_frameworks) ? lead.compliance_frameworks.join('; ') : lead.compliance_frameworks || '',
        lead.company_size || '',
        lead.industry || '',
        lead.decision_timeline || '',
        lead.budget_range || '',
        new Date(lead.created_at).toISOString()
      ]);

      const csvContent = [headers, ...csvData]
        .map(row => row.map(cell => `"${cell}"`).join(','))
        .join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `arion-leads-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    function exportJSON() {
      if (!leads.length) {
        alert('No leads to export');
        return;
      }

      const blob = new Blob([JSON.stringify(leads, null, 2)], { type: 'application/json' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `arion-leads-${new Date().toISOString().split('T')[0]}.json`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    // UTILITY FUNCTIONS
    async function cleanupData() {
      if (!confirm('This will clean up expired verification requests and old sessions. Continue?')) {
        return;
      }

      try {
        const response = await ArionUtils.api.call('cleanup-expired-data', { daysOld: 30 });

        if (response.success) {
          alert(`Cleanup completed:\n- ${response.data.expiredVerifications} expired verifications removed\n- ${response.data.oldSessions} old sessions removed`);
          loadCurrentTab();
        } else {
          throw new Error(response.error);
        }
      } catch (error) {
        console.error('Cleanup failed:', error);
        alert('Cleanup failed: ' + error.message);
      }
    }

    function showSuccessMessage(message) {
      const successMsg = document.createElement('div');
      successMsg.className = 'success-message';
      successMsg.textContent = message;

      const container = document.querySelector('.container');
      container.insertBefore(successMsg, container.firstChild);

      setTimeout(() => successMsg.remove(), 3000);
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', checkAdminSession);

    // Auto-format MFA code input
    document.getElementById('mfaCode').addEventListener('input', function (e) {
      let value = e.target.value.replace(/\D/g, '');
      value = value.substring(0, 6);
      e.target.value = value;
    });

    // Enter key handling
    document.getElementById('adminPassword').addEventListener('keypress', function (e) {
      if (e.key === 'Enter') {
        document.getElementById('loginForm').dispatchEvent(new Event('submit'));
      }
    });

    document.getElementById('mfaCode').addEventListener('keypress', function (e) {
      if (e.key === 'Enter') {
        document.getElementById('mfaForm').dispatchEvent(new Event('submit'));
      }
    });
  </script>
  <script src="src/config.js"></script>
  <script src="src/utils.js"></script>

</body>

</html>
