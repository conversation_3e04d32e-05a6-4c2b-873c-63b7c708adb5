/* =============================================================================
   DOCUMENT PREVIEW STYLING
   ============================================================================= */

.document-content {
  line-height: 1.6;
  font-size: 14px;
  color: var(--neutral-700);
}

.document-content h1 {
  font-size: 1.5rem;
  color: var(--brand-primary);
  margin: 1.5rem 0 1rem 0;
  border-bottom: 2px solid var(--brand-light);
  padding-bottom: 0.5rem;
  font-weight: 700;
}

.document-content h2 {
  font-size: 1.3rem;
  color: var(--neutral-700);
  margin: 1.25rem 0 0.75rem 0;
  font-weight: 600;
}

.document-content h3 {
  font-size: 1.1rem;
  color: var(--neutral-600);
  margin: 1rem 0 0.5rem 0;
  font-weight: 600;
}

.document-content p {
  margin: 0.75rem 0;
  text-align: left;
}

.document-content ul {
  margin: 0.5rem 0 1rem 0;
  padding-left: 1.5rem;
}

.document-content li {
  margin: 0.25rem 0;
  line-height: 1.5;
}

.document-content strong {
  color: var(--neutral-800);
  font-weight: 600;
}

.document-content em {
  font-style: italic;
  color: var(--neutral-600);
}

/* Document sections styling */
.document-section {
  margin-bottom: 2rem;
}

.section-title {
  border-bottom: 1px solid var(--neutral-200);
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

/* Watermark styling */
.document-watermark {
  text-align: center;
  color: var(--neutral-400);
  font-size: 0.875rem;
  font-weight: 600;
  padding: 1rem;
  margin-bottom: 1.5rem;
  border: 1px dashed var(--neutral-300);
  border-radius: 4px;
  background: var(--neutral-50);
}

/* Special content blocks */
.document-content .risk-info {
  background: var(--neutral-50);
  border-left: 4px solid var(--brand-primary);
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 0 4px 4px 0;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .document-content {
    font-size: 13px;
  }
  
  .document-content h1 {
    font-size: 1.3rem;
  }
  
  .document-content h2 {
    font-size: 1.2rem;
  }
  
  .document-content h3 {
    font-size: 1.1rem;
  }
}