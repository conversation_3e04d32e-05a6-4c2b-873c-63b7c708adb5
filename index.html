<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArionComply - AI-Powered Compliance Automation</title>
    <style>
        :root {
            --brand-primary: #059669;
            --brand-secondary: #047857;
            --brand-accent: #065f46;
            --brand-dark: #064e3b;
            --brand-light: #ecfdf5;
            --neutral-50: #f8fafc;
            --neutral-100: #f1f5f9;
            --neutral-200: #e2e8f0;
            --neutral-300: #cbd5e1;
            --neutral-400: #94a3b8;
            --neutral-500: #64748b;
            --neutral-600: #475569;
            --neutral-700: #334155;
            --neutral-800: #1e293b;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --shadow: 0 4px 16px rgba(5, 150, 105, 0.15);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            margin: 0;
            padding: 0;
            background: var(--neutral-50);
            color: var(--neutral-700);
            height: 100vh;
            overflow: hidden;
        }

        /* Landing Page Styles */
        .page-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            text-align: center;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .hero {
            background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 1rem;
        }

        .hero h1 {
            font-size: 2.2rem;
            margin-bottom: 0.8rem;
        }

        .hero p {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: 1.5rem;
        }

        .cta-button {
            display: inline-block;
            background: white;
            color: var(--brand-primary);
            padding: 1rem 2rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            margin: 0.5rem;
            transition: all 0.2s ease;
            border: 2px solid white;
            cursor: pointer;
        }

        .cta-button:hover {
            background: var(--neutral-50);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }

        .cta-button.secondary {
            background: transparent;
            color: white;
            border-color: rgba(255, 255, 255, 0.3);
        }

        .cta-button.secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: white;
        }

        /* Dynamic Section Styles */
        .dynamic-section {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* Features Showcase */
        .features-showcase {
            flex: 1;
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            padding: 2rem 1.5rem;
            border-radius: 12px;
            overflow: hidden;
        }

        .features-showcase h2 {
            text-align: center;
            color: var(--neutral-800);
            margin-bottom: 1.5rem;
            font-size: 1.6rem;
        }

        .showcase-container {
            max-width: 1000px;
            margin: 0 auto;
            position: relative;
        }

        .showcase-content {
            position: relative;
            max-width: 1000px;
            margin: 0 auto;
        }

        .showcase-slide {
            display: none;
            opacity: 0;
            transition: opacity 0.6s ease;
        }

        .showcase-slide.active {
            display: block;
            opacity: 1;
        }

        .slide-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            align-items: center;
        }

        .showcase-text h3 {
            color: var(--neutral-800);
            margin-bottom: 0.8rem;
            font-size: 1.3rem;
        }

        .showcase-text p {
            color: var(--neutral-600);
            line-height: 1.4;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }

        .feature-benefits {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .benefit-tag {
            background: var(--brand-light);
            color: var(--brand-primary);
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .showcase-visual {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .demo-preview {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            padding: 1rem;
            width: 100%;
            max-width: 300px;
            border: 1px solid var(--neutral-200);
        }

        .preview-header {
            color: var(--brand-primary);
            font-weight: 600;
            font-size: 0.85rem;
            margin-bottom: 0.8rem;
            border-bottom: 1px solid var(--neutral-200);
            padding-bottom: 0.4rem;
        }

        .preview-content {
            display: flex;
            flex-direction: column;
            gap: 0.6rem;
        }

        .preview-line {
            color: var(--neutral-700);
            font-size: 0.8rem;
            padding: 0.4rem;
            background: var(--neutral-50);
            border-radius: 6px;
            border-left: 3px solid var(--brand-primary);
        }

        .showcase-nav {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: none;
            background: var(--neutral-300);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-dot.active {
            background: var(--brand-primary);
            transform: scale(1.2);
        }

        /* Animations */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(30px);
            }

            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .showcase-slide.active .slide-content {
            animation: slideIn 0.6s ease-out;
        }

        /* Buttons */
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin: 0.5rem;
        }

        .btn-primary {
            background: var(--brand-primary);
            color: white;
        }

        .btn-primary:hover {
            background: var(--brand-secondary);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--neutral-200);
            color: var(--neutral-700);
        }

        .btn-secondary:hover {
            background: var(--neutral-300);
        }

        /* Modal Styles */
        .modal-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 10000;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .modal-overlay.show {
            display: flex;
        }

        .modal {
            background: white;
            border-radius: 16px;
            max-width: 500px;
            width: 100%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, var(--brand-primary), var(--brand-secondary));
            color: white;
            padding: 2rem;
            text-align: center;
            border-radius: 16px 16px 0 0;
        }

        .modal-body {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            color: var(--neutral-700);
            font-weight: 500;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .form-group label .required {
            color: var(--error);
            margin-left: 2px;
        }

        .form-control {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 2px solid var(--neutral-200);
            border-radius: 8px;
            font-size: 1rem;
            font-family: inherit;
            transition: all 0.2s ease;
            background: white;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--brand-primary);
            box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
        }

        .form-control.error {
            border-color: var(--error);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .error-message {
            color: var(--error);
            font-size: 0.8rem;
            margin-top: 0.25rem;
            display: none;
        }

        .error-message.show {
            display: block;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .modal-footer {
            padding: 0 2rem 2rem;
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
        }

        .success-state {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .success-state.show {
            display: block;
        }

        .success-icon {
            font-size: 4rem;
            color: var(--success);
            margin-bottom: 1.5rem;
        }

        /* Warning box for login modal */
        .warning-box {
            background: #fef3c7;
            border: 1px solid var(--warning);
            color: #92400e;
            padding: 1rem;
            margin: 1rem 2rem 2rem;
            border-radius: 8px;
            text-align: center;
            display: none;
        }

        .warning-box.show {
            display: block;
        }

        .warning-box p {
            margin: 0;
        }

        .warning-box .warning-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .warning-box .warning-text {
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }

        /* Privacy Policy Modal Styles */
        .close-btn:hover {
            background: rgba(255, 255, 255, 0.3) !important;
        }

        #privacyPolicyModal .modal-body h3 {
            margin-bottom: 1rem;
        }

        #privacyPolicyModal .modal-body p {
            margin-bottom: 1rem;
        }

        #privacyPolicyModal .modal-body ul {
            margin-bottom: 1.5rem;
        }

        #privacyPolicyModal .modal-body table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }

        #privacyPolicyModal .modal-body th,
        #privacyPolicyModal .modal-body td {
            border: 1px solid var(--neutral-300);
            padding: 0.5rem;
            text-align: left;
        }

        #privacyPolicyModal .modal-body th {
            background: var(--neutral-100);
            font-weight: 600;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .page-container {
                padding: 1rem;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .modal-footer {
                flex-direction: column;
            }

            .cta-button,
            .btn {
                display: block;
                margin: 0.5rem 0;
            }

            .slide-content {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 2rem;
            }

            .features-showcase {
                padding: 2rem 1rem;
            }

            .features-showcase h2 {
                font-size: 1.5rem;
            }

            #privacyPolicyModal .modal {
                margin: 1rem;
                max-height: 95vh;
            }
            
            #privacyPolicyModal .modal-body {
                padding: 1.5rem;
            }
        }
    </style>
</head>

<body>
    <!-- Landing Page -->
    <div class="page-container">
        <div class="hero">
            <h1>ArionComply Demo</h1>
            <p>Revolutionize your compliance workflow with intelligent AI automation that delivers auditable results and
                complete transparency. Simplify regulatory management, accelerate policy development, and maintain
                continuous compliance effortlessly.</p>
            <div style="margin-top: 1.5rem;">
                <div style="display: flex; gap: 1.5rem; justify-content: center; flex-wrap: wrap;">
                    <!-- New User Button -->
                    <div style="background: white; color: var(--brand-primary); padding: 1.5rem; border-radius: 12px; text-decoration: none; font-weight: 600; transition: all 0.3s ease; cursor: pointer; border: 2px solid white; min-width: 220px; text-align: center; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);" onclick="showDemoAccessModal()" onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 8px 25px rgba(0, 0, 0, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(0, 0, 0, 0.1)'">
                        <div style="font-size: 2.5rem; margin-bottom: 0.8rem;">🚀</div>
                        <div style="font-size: 1.2rem; font-weight: 700; margin-bottom: 0.4rem;">Get Started</div>
                        <div style="font-size: 0.85rem; opacity: 0.8; line-height: 1.3;">New to ArionComply? Create your free account</div>
                    </div>
                    
                    <!-- Existing User Button -->
                    <div style="background: rgba(255,255,255,0.15); color: white; padding: 1.5rem; border-radius: 12px; text-decoration: none; font-weight: 600; transition: all 0.3s ease; cursor: pointer; border: 2px solid rgba(255,255,255,0.3); min-width: 220px; text-align: center; backdrop-filter: blur(10px);" onclick="startDemo()" onmouseover="this.style.background='rgba(255,255,255,0.25)'; this.style.transform='translateY(-3px)'; this.style.borderColor='rgba(255,255,255,0.5)'" onmouseout="this.style.background='rgba(255,255,255,0.15)'; this.style.transform='translateY(0)'; this.style.borderColor='rgba(255,255,255,0.3)'">
                        <div style="font-size: 2.5rem; margin-bottom: 0.8rem;">🔒</div>
                        <div style="font-size: 1.2rem; font-weight: 700; margin-bottom: 0.4rem;">Sign In</div>
                        <div style="font-size: 0.85rem; opacity: 0.9; line-height: 1.3;">Already registered? Access the demo platform</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dynamic Features Section -->
        <div class="dynamic-section">
            <!-- Sliding Features Showcase -->
            <div class="features-showcase">
                <h2>See ArionComply in Action</h2>
                <div class="showcase-container">
                    <div class="showcase-content">
                        <!-- Feature 1: ISO 27001 -->
                        <div class="showcase-slide active">
                            <div class="slide-content">
                                <div class="showcase-text">
                                    <h3>🏆 ISO 27001 Certification</h3>
                                    <p>Generate risk registers, policies, and implementation roadmaps automatically. Our AI creates tailored documentation that passes audits.</p>
                                    <div class="feature-benefits">
                                        <span class="benefit-tag">3-6 months faster</span>
                                        <span class="benefit-tag">Audit-ready docs</span>
                                        <span class="benefit-tag">Expert guidance</span>
                                    </div>
                                </div>
                                <div class="showcase-visual">
                                    <div class="demo-preview">
                                        <div class="preview-header">Risk Register Preview</div>
                                        <div class="preview-content">
                                            <div class="preview-line">📋 Data Breach Risk - High Priority</div>
                                            <div class="preview-line">🔍 Access Control Gap - Medium</div>
                                            <div class="preview-line">☁️ Cloud Security Review - Low</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Feature 2: GDPR -->
                        <div class="showcase-slide">
                            <div class="slide-content">
                                <div class="showcase-text">
                                    <h3>🛡️ GDPR Compliance</h3>
                                    <p>Automated ROPA, Data Protection Impact Assessments, and privacy policy generation. Stay compliant with evolving data protection regulations.</p>
                                    <div class="feature-benefits">
                                        <span class="benefit-tag">Auto ROPA</span>
                                        <span class="benefit-tag">DPIA templates</span>
                                        <span class="benefit-tag">Legal updates</span>
                                    </div>
                                </div>
                                <div class="showcase-visual">
                                    <div class="demo-preview">
                                        <div class="preview-header">GDPR Assessment</div>
                                        <div class="preview-content">
                                            <div class="preview-line">✅ Data mapping complete</div>
                                            <div class="preview-line">🔍 Consent mechanisms reviewed</div>
                                            <div class="preview-line">📊 Privacy impact assessed</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Feature 3: AI Governance -->
                        <div class="showcase-slide">
                            <div class="slide-content">
                                <div class="showcase-text">
                                    <h3>🤖 AI Governance</h3>
                                    <p>Stay ahead of AI regulations with comprehensive governance frameworks. Risk assessments, ethical guidelines, and EU AI Act compliance tools.</p>
                                    <div class="feature-benefits">
                                        <span class="benefit-tag">EU AI Act ready</span>
                                        <span class="benefit-tag">Risk assessment</span>
                                        <span class="benefit-tag">Ethics framework</span>
                                    </div>
                                </div>
                                <div class="showcase-visual">
                                    <div class="demo-preview">
                                        <div class="preview-header">AI Risk Dashboard</div>
                                        <div class="preview-content">
                                            <div class="preview-line">🎯 High-risk AI systems: 2</div>
                                            <div class="preview-line">⚖️ Bias assessment: Pending</div>
                                            <div class="preview-line">📊 Compliance score: 85%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation dots -->
                    <div class="showcase-nav">
                        <button class="nav-dot active" onclick="goToSlide(0)"></button>
                        <button class="nav-dot" onclick="goToSlide(1)"></button>
                        <button class="nav-dot" onclick="goToSlide(2)"></button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Login Modal for "Already Registered?" -->
    <div class="modal-overlay" id="loginModal">
        <div class="modal" style="max-width: 400px;">
            <div class="modal-header">
                <h2>Access Demo</h2>
                <p>Enter your registered email to continue</p>
            </div>
            <div class="modal-body">
                <form id="loginForm">
                    <div class="form-group">
                        <label for="loginEmail">Email Address <span class="required">*</span></label>
                        <input type="email" id="loginEmail" name="loginEmail" class="form-control" required
                            placeholder="Enter your registered email">
                        <div class="error-message" id="loginEmailError"></div>
                    </div>
                    <div class="form-group">
                        <label for="loginPassword">Password <span class="required">*</span></label>
                        <input type="password" id="loginPassword" name="loginPassword" class="form-control" required
                            placeholder="Enter your password">
                        <div class="error-message" id="loginPasswordError"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideLoginModal()">Cancel</button>
                <button type="button" class="btn btn-primary" id="submitLogin">Continue to Demo</button>
            </div>

            <!-- Warning message for unregistered users -->
            <div class="warning-box" id="registrationWarning">
                <p class="warning-title">⚠️ Email not found</p>
                <p class="warning-text">Please register first to access the demo</p>
                <button type="button" class="btn btn-primary"
                    onclick="hideLoginModal(); showDemoAccessModal();">Register Now</button>
            </div>
        </div>
    </div>

    <!-- Demo Access Modal -->
    <div class="modal-overlay" id="demoAccessModal">
        <div class="modal">
            <div class="modal-header">
                <h2>Get Demo Access</h2>
                <p>Register to access our AI-powered compliance platform</p>
            </div>

            <!-- Request Form -->
            <div id="accessRequestForm">
                <div class="modal-body">
                    <form id="demoAccessForm" novalidate>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="firstName">First Name <span class="required">*</span></label>
                                <input type="text" id="firstName" name="firstName" class="form-control" required>
                                <div class="error-message" id="firstNameError"></div>
                            </div>
                            <div class="form-group">
                                <label for="lastName">Last Name <span class="required">*</span></label>
                                <input type="text" id="lastName" name="lastName" class="form-control" required>
                                <div class="error-message" id="lastNameError"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="workEmail">Work Email <span class="required">*</span></label>
                            <input type="email" id="workEmail" name="workEmail" class="form-control" required>
                            <div class="error-message" id="workEmailError"></div>
                        </div>

                        <div class="form-group">
                            <label for="company">Company <span class="required">*</span></label>
                            <input type="text" id="company" name="company" class="form-control" required>
                            <div class="error-message" id="companyError"></div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="jobTitle">Job Title <span class="required">*</span></label>
                                <select id="jobTitle" name="jobTitle" class="form-control" required>
                                    <option value="">Select your role...</option>
                                    <option value="CISO">CISO/Security Leader</option>
                                    <option value="Compliance Manager">Compliance Manager</option>
                                    <option value="Risk Manager">Risk Manager</option>
                                    <option value="Legal Counsel">Legal Counsel</option>
                                    <option value="Privacy Officer">Privacy Officer</option>
                                    <option value="CEO/Founder">CEO/Founder</option>
                                    <option value="CTO">CTO</option>
                                    <option value="Other">Other</option>
                                </select>
                                <div class="error-message" id="jobTitleError"></div>
                            </div>
                            <div class="form-group">
                                <label for="companySize">Company Size</label>
                                <select id="companySize" name="companySize" class="form-control">
                                    <option value="">Select size...</option>
                                    <option value="1-10">1-10 employees</option>
                                    <option value="11-50">11-50 employees</option>
                                    <option value="51-250">51-250 employees</option>
                                    <option value="251-1000">251-1000 employees</option>
                                    <option value="1000+">1000+ employees</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="complianceInterest">Primary Compliance Interest <span
                                    class="required">*</span></label>
                            <select id="complianceInterest" name="complianceInterest" class="form-control" required>
                                <option value="">What brings you here?</option>
                                <option value="ISO 27001">ISO 27001 Certification</option>
                                <option value="GDPR">GDPR Compliance</option>
                                <option value="AI Governance">AI Risk Management</option>
                                <option value="SOC 2">SOC 2 Compliance</option>
                                <option value="Multiple Standards">Multiple Standards</option>
                                <option value="General Exploration">General Exploration</option>
                            </select>
                            <div class="error-message" id="complianceInterestError"></div>
                        </div>
                        <div class="form-group">
                            <label for="password">Create Password <span class="required">*</span></label>
                            <input type="password" id="password" name="password" class="form-control" required
                                minlength="8" placeholder="Minimum 8 characters">
                            <div class="error-message" id="passwordError"></div>
                            <small style="color: #64748b; font-size: 0.8rem; margin-top: 0.25rem; display: block;">
                                Use this password to login after email verification
                            </small>
                        </div>
                        <div class="form-group">
                            <label for="confirmPassword">Confirm Password <span class="required">*</span></label>
                            <input type="password" id="confirmPassword" name="confirmPassword" class="form-control"
                                required placeholder="Re-enter your password">
                            <div class="error-message" id="confirmPasswordError"></div>
                        </div>
                        
                        <!-- Data Processing Consent - Required -->
                        <div class="form-group">
                            <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                                <input type="checkbox" id="agreedToDataProcessing" name="agreedToDataProcessing" required
                                    style="margin-top: 0.25rem;">
                                <label for="agreedToDataProcessing" style="margin: 0; font-size: 0.85rem; line-height: 1.4;">
                                    I agree to the processing of my personal data for demo access purposes. <span class="required">*</span>
                                </label>
                            </div>
                            <div class="error-message" id="dataProcessingError"></div>
                        </div>

                        <!-- Privacy Policy Checkbox -->
                        <div class="form-group">
                            <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                                <input type="checkbox" id="agreedToPrivacy" name="agreedToPrivacy" required
                                    style="margin-top: 0.25rem;">
                                <label for="agreedToPrivacy" style="margin: 0; font-size: 0.85rem; line-height: 1.4;">
                                    I have read and understood to the 
                                    <a href="#" onclick="showPrivacyPolicy()" style="color: #059669; text-decoration: none; font-weight: 500;">
                                        Privacy Policy
                                    </a> <span class="required">*</span>
                                </label>
                            </div>
                            <div class="error-message" id="privacyError"></div>
                        </div>

                        <!-- Marketing Communications - Optional -->
                        <div class="form-group">
                            <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                                <input type="checkbox" id="agreedToMarketing" name="agreedToMarketing"
                                    style="margin-top: 0.25rem;">
                                <label for="agreedToMarketing" style="margin: 0; font-size: 0.85rem; line-height: 1.4;">
                                    I agree to receive product communications (optional)
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="hideDemoAccessModal()">Cancel</button>
                    <button type="button" class="btn btn-primary" id="submitAccessRequest">Request Access</button>
                </div>
            </div>

            <!-- Success State -->
            <div class="success-state" id="accessRequestSuccess">
                <div class="success-icon">✅</div>
                <h3>Access Request Submitted!</h3>
                <p>We've sent a verification email to <strong id="submittedEmail"></strong></p>
                <p>Please check your email and click the verification link to access the demo platform.</p>
                <div style="margin-top: 2rem;">
                    <button class="btn btn-primary" onclick="hideDemoAccessModal()">Got it</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Privacy Policy Modal -->
    <div class="modal-overlay" id="privacyPolicyModal" style="display: none; z-index: 10000;">
        <div class="modal" style="max-width: 800px; max-height: 90vh; overflow-y: auto;">
            <div class="modal-header">
                <h2>Arion Networks Data Privacy Policy</h2>
                <button type="button" class="close-btn" onclick="hidePrivacyPolicy()" 
                    style="position: absolute; top: 1rem; right: 1rem; background: rgba(255,255,255,0.2); border: none; color: white; font-size: 1.5rem; cursor: pointer; padding: 0.5rem; border-radius: 4px;">
                    ×
                </button>
            </div>
            <div class="modal-body" style="padding: 2rem; font-size: 0.9rem; line-height: 1.6; color: #374151;">
                <p><strong>Effective Date:</strong> July 11, 2025<br>
                <strong>Document Owner:</strong> Data Protection & Risk Manager<br>
                <strong>Applies to:</strong> All users of Arion Networks' Services, including demos, pilots, and full subscriptions.</p>

                <h3 style="color: #059669; margin-top: 2rem;">1. INTRODUCTION</h3>
                <p>Arion Networks s.r.o. ("Arion Networks", "we", "our") respects your privacy and is committed to protecting personal data in accordance with the General Data Protection Regulation (GDPR), ISO/IEC 27001, ISO/IEC 27701, and other applicable regulations.</p>
                
                <p>This Data Privacy Policy explains:</p>
                <ul>
                    <li>What personal data we collect</li>
                    <li>How we use it</li>
                    <li>How we protect it</li>
                    <li>Your rights as a data subject</li>
                </ul>

                <h3 style="color: #059669; margin-top: 2rem;">2. DATA COLLECTION</h3>
                <h4>2.1 Types of Data Collected</h4>
                <p>We may collect the following categories of personal data:</p>
                <ul>
                    <li><strong>User identifiers:</strong> Full name, email, company name</li>
                    <li><strong>Metadata:</strong> Login logs, activity logs, browser type, device info</li>
                    <li><strong>Account configuration data:</strong> Frameworks used, service tiers, billing details</li>
                </ul>

                <p>We <strong>do not</strong> knowingly collect:</p>
                <ul>
                    <li>Protected health information (PHI)</li>
                    <li>Government-issued identification (e.g., SSN, passport #)</li>
                    <li>Payment card data (payment is handled by third-party processors)</li>
                </ul>

                <h4>2.2 Data Sources</h4>
                <ul>
                    <li>Direct input by user (registration, forms)</li>
                    <li>System logs (access history, usage data)</li>
                    <li>Integrations with 3rd party services where authorized by the user</li>
                </ul>

                <h3 style="color: #059669; margin-top: 2rem;">3. PURPOSE AND LEGAL BASIS FOR PROCESSING</h3>
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <tr>
                        <th style="border: 1px solid #cbd5e1; padding: 0.5rem; background: #f1f5f9;">Purpose</th>
                        <th style="border: 1px solid #cbd5e1; padding: 0.5rem; background: #f1f5f9;">Legal Basis</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #cbd5e1; padding: 0.5rem;">Account creation and access</td>
                        <td style="border: 1px solid #cbd5e1; padding: 0.5rem;">Contract performance</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #cbd5e1; padding: 0.5rem;">Service delivery and customization</td>
                        <td style="border: 1px solid #cbd5e1; padding: 0.5rem;">Contract performance</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #cbd5e1; padding: 0.5rem;">Billing and invoicing</td>
                        <td style="border: 1px solid #cbd5e1; padding: 0.5rem;">Legal obligation</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #cbd5e1; padding: 0.5rem;">Platform security and auditing</td>
                        <td style="border: 1px solid #cbd5e1; padding: 0.5rem;">Legitimate interest</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #cbd5e1; padding: 0.5rem;">Usage analytics and product improvement</td>
                        <td style="border: 1px solid #cbd5e1; padding: 0.5rem;">Legitimate interest</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #cbd5e1; padding: 0.5rem;">Anonymized data for AI model improvement</td>
                        <td style="border: 1px solid #cbd5e1; padding: 0.5rem;">Legitimate interest, with safeguards</td>
                    </tr>
                </table>

                <h3 style="color: #059669; margin-top: 2rem;">4. DATA SHARING AND SUBPROCESSORS</h3>
                <p>We do <strong>not</strong> sell, rent, or share personal data with third parties for marketing purposes.</p>
                <p>Authorized subprocessors (e.g., infrastructure and analytics providers) may access data only as strictly necessary to provide services on our behalf. A current list is maintained at: [Available upon request or internal portal]</p>
                <p>All subprocessors are bound by contractual agreements aligned with GDPR Art. 28.</p>

                <h3 style="color: #059669; margin-top: 2rem;">5. DATA RETENTION AND DELETION</h3>
                <p>We retain personal data only as long as needed to:</p>
                <ul>
                    <li>Fulfill the purpose of collection</li>
                    <li>Meet legal, contractual, or regulatory obligations</li>
                </ul>

                <h4>Standard Retention Periods</h4>
                <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                    <tr>
                        <th style="border: 1px solid #cbd5e1; padding: 0.5rem; background: #f1f5f9;">Data Type</th>
                        <th style="border: 1px solid #cbd5e1; padding: 0.5rem; background: #f1f5f9;">Retention Period</th>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #cbd5e1; padding: 0.5rem;">Account data</td>
                        <td style="border: 1px solid #cbd5e1; padding: 0.5rem;">Duration of active use + 1 year</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #cbd5e1; padding: 0.5rem;">Logs & analytics</td>
                        <td style="border: 1px solid #cbd5e1; padding: 0.5rem;">Up to 12 months</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #cbd5e1; padding: 0.5rem;">Anonymized data</td>
                        <td style="border: 1px solid #cbd5e1; padding: 0.5rem;">Retained indefinitely</td>
                    </tr>
                </table>

                <p>Users may request data deletion under the Right to Erasure by emailing: <strong><EMAIL></strong></p>

                <h3 style="color: #059669; margin-top: 2rem;">6. DATA SUBJECT RIGHTS</h3>
                <p>Under applicable data protection laws, you have the following rights:</p>
                <ul>
                    <li>Right to Access</li>
                    <li>Right to Rectification</li>
                    <li>Right to Erasure</li>
                    <li>Right to Restriction</li>
                    <li>Right to Data Portability</li>
                    <li>Right to Object</li>
                    <li>Right to Lodge a Complaint with a supervisory authority</li>
                </ul>
                <p>To exercise your rights, contact us at: <strong><EMAIL></strong></p>

                <h3 style="color: #059669; margin-top: 2rem;">7. SECURITY MEASURES</h3>
                <p>We implement administrative, technical, and physical safeguards including:</p>
                <ul>
                    <li>Role-based access control (RBAC)</li>
                    <li>Encryption at rest and in transit</li>
                    <li>Continuous monitoring and anomaly detection</li>
                    <li>Regular security audits and penetration testing</li>
                </ul>
                <p>All employees and contractors are trained on secure data handling and confidentiality.</p>

                <h3 style="color: #059669; margin-top: 2rem;">8. INTERNATIONAL DATA TRANSFERS</h3>
                <p>Where data is transferred outside the EEA or Switzerland, we ensure appropriate safeguards such as:</p>
                <ul>
                    <li>Standard Contractual Clauses (SCCs)</li>
                    <li>Adequacy decisions under GDPR</li>
                </ul>

                <h3 style="color: #059669; margin-top: 2rem;">9. COOKIES</h3>
                <p>Arion Networks does not use tracking, analytics, or advertising cookies on its public-facing or authenticated Services. Functional session cookies may be used strictly for login/session state and are not persisted or tracked beyond the active session.</p>

                <h3 style="color: #059669; margin-top: 2rem;">10. CONTACT</h3>
                <p>For any privacy-related concerns or requests, contact:<br>
                <strong>Data Protection & Risk Manager</strong><br>
                Email: <strong><EMAIL></strong></p>

                <p style="margin-top: 2rem; font-size: 0.8rem; color: #6b7280;">
                    <em>Last reviewed: July 11, 2025<br>
                    Next review: January 11, 2026</em>
                </p>
            </div>
            <div class="modal-footer" style="padding: 1rem 2rem 2rem; text-align: center;">
                <button type="button" class="btn btn-primary" onclick="hidePrivacyPolicy()">
                    I Understand
                </button>
            </div>
        </div>
    </div>

    <script>
        // Privacy Policy Modal Functions
        function showPrivacyPolicy() {
            document.getElementById('privacyPolicyModal').style.display = 'flex';
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        }

        function hidePrivacyPolicy() {
            document.getElementById('privacyPolicyModal').style.display = 'none';
            document.body.style.overflow = 'auto'; // Restore scrolling
        }

        // Close modal when clicking outside
        document.getElementById('privacyPolicyModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hidePrivacyPolicy();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && document.getElementById('privacyPolicyModal').style.display === 'flex') {
                hidePrivacyPolicy();
            }
        });

        // Demo functionality - show login modal
        function startDemo() {
            console.log('Opening login modal for demo access');
            showLoginModal();
        }

        // Login Modal Management
        function showLoginModal() {
            document.getElementById('loginModal').classList.add('show');
            document.body.style.overflow = 'hidden';
            document.getElementById('loginEmail').focus();
        }

        function hideLoginModal() {
            document.getElementById('loginModal').classList.remove('show');
            document.body.style.overflow = '';

            // Reset form
            document.getElementById('loginForm').reset();
            document.getElementById('loginEmailError').classList.remove('show');
            document.getElementById('loginPasswordError').classList.remove('show');
            document.getElementById('loginEmail').classList.remove('error');
            document.getElementById('loginPassword').classList.remove('error');
            document.getElementById('registrationWarning').classList.remove('show');
        }

        // Demo Access Modal Management
        function showDemoAccessModal() {
            document.getElementById('demoAccessModal').classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function hideDemoAccessModal() {
            document.getElementById('demoAccessModal').classList.remove('show');
            document.body.style.overflow = '';

            // Reset form
            document.getElementById('demoAccessForm').reset();
            document.querySelectorAll('.error-message').forEach(el => el.classList.remove('show'));
            document.querySelectorAll('.form-control').forEach(el => el.classList.remove('error'));

            // Reset states
            document.getElementById('accessRequestForm').style.display = 'block';
            document.getElementById('accessRequestSuccess').classList.remove('show');
        }

        // Login Modal Initialization and Handlers
        function initializeLoginModal() {
            const submitBtn = document.getElementById('submitLogin');
            const loginEmail = document.getElementById('loginEmail');
            const loginPassword = document.getElementById('loginPassword');

            // Submit button click handler
            submitBtn.addEventListener('click', handleLoginSubmit);

            // Enter key handlers
            loginEmail.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    loginPassword.focus();
                }
            });

            loginPassword.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    handleLoginSubmit();
                }
            });

            // Close on overlay click
            document.getElementById('loginModal').addEventListener('click', (e) => {
                if (e.target === e.currentTarget) {
                    hideLoginModal();
                }
            });

            // Clear errors on input
            loginEmail.addEventListener('input', () => {
                document.getElementById('loginEmailError').classList.remove('show');
                loginEmail.classList.remove('error');
                document.getElementById('registrationWarning').classList.remove('show');
            });

            loginPassword.addEventListener('input', () => {
                document.getElementById('loginPasswordError').classList.remove('show');
                loginPassword.classList.remove('error');
                document.getElementById('registrationWarning').classList.remove('show');
            });
        }

        // Handle login form submission
        async function handleLoginSubmit() {
            const email = document.getElementById('loginEmail').value.trim();
            const password = document.getElementById('loginPassword').value.trim();
            const submitBtn = document.getElementById('submitLogin');
            const errorDiv = document.getElementById('loginEmailError');
            const passwordErrorDiv = document.getElementById('loginPasswordError');
            const warningDiv = document.getElementById('registrationWarning');

            // Reset previous errors
            errorDiv.classList.remove('show');
            passwordErrorDiv.classList.remove('show');
            warningDiv.classList.remove('show');
            document.getElementById('loginEmail').classList.remove('error');
            document.getElementById('loginPassword').classList.remove('error');

            // Basic validation
            let hasError = false;

            if (!email) {
                showLoginError('loginEmail', 'Please enter your email address');
                hasError = true;
            } else if (!isValidEmailFormat(email)) {
                showLoginError('loginEmail', 'Please enter a valid email address');
                hasError = true;
            }

            if (!password) {
                showLoginError('loginPassword', 'Please enter your password');
                hasError = true;
            }

            if (hasError) return;

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.textContent = 'Signing in...';

            try {
                console.log('Authenticating user:', email);

                // Check if this is an admin user
                const isAdmin = email.toLowerCase() === '<EMAIL>' || email.toLowerCase() === '<EMAIL>';

                if (isAdmin) {
                    console.log('🔍 Admin user detected:', email);
                    submitBtn.textContent = 'Verifying admin access...';
                }

                // Call the API to authenticate user
                const response = await ArionUtils.api.call('authenticate-user', {
                    email: email,
                    password: password
                });

                if (response.success && response.authenticated) {
                    // User authenticated successfully
                    console.log('✅ User authenticated, redirecting to demo');

                    if (response.userType === 'admin') {
                        console.log('👑 Admin user authenticated:', response.adminRole);

                        // Show admin success message
                        showAdminLoginSuccess(response);

                        // Store admin session info
                        if (response.sessionId) {
                            ArionUtils.session.saveSessionToStorage(response.sessionId);

                            // Store admin info for demo interface
                            localStorage.setItem('arion-admin-info', JSON.stringify({
                                email: response.userEmail,
                                role: response.adminRole,
                                permissions: response.permissions,
                                loginTime: new Date().toISOString()
                            }));
                        }

                        // Redirect to demo with admin flag
                        setTimeout(() => {
                            window.location.href = response.demoUrl;
                        }, 2000);

                    } else {
                        // Regular user
                        if (response.sessionId) {
                            ArionUtils.session.saveSessionToStorage(response.sessionId);
                        }

                        window.location.href = response.demoUrl || 'demo.html';
                    }

                } else {
                    // Authentication failed - show appropriate message
                    console.log('❌ Authentication failed');

                    if (isAdmin) {
                        showLoginError('loginPassword', 'Invalid admin credentials. Please check your password.');
                    } else {
                        warningDiv.classList.add('show');
                    }
                }

            } catch (error) {
                console.error('Login failed:', error);

                if (error.message.includes('invalid credentials') || error.message.includes('not found')) {
                    const isAdmin = email.toLowerCase().includes('@arionetworks.com');

                    if (isAdmin) {
                        showLoginError('loginPassword', 'Invalid admin credentials');
                    } else {
                        warningDiv.classList.add('show');
                    }
                } else {
                    showLoginError('loginEmail', 'Connection error. Please try again.');
                }
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Continue to Demo';
            }
        }

        // Admin login success handler
        function showAdminLoginSuccess(response) {
            // Update the modal content to show admin success
            const loginModal = document.getElementById('loginModal');
            const modalBody = loginModal.querySelector('.modal-body');
            const modalFooter = loginModal.querySelector('.modal-footer');

            modalBody.innerHTML = `
                <div style="text-align: center; padding: 2rem 0;">
                    <div style="font-size: 4rem; margin-bottom: 1rem;">👑</div>
                    <h3 style="color: var(--brand-primary); margin-bottom: 1rem;">Admin Access Granted!</h3>
                    <p>Welcome back, <strong>${response.userEmail.split('@')[0]}</strong></p>
                    <div style="background: var(--brand-light); padding: 1rem; border-radius: 8px; margin: 1rem 0;">
                        <p><strong>Role:</strong> ${response.adminRole}</p>
                        <p><strong>Permissions:</strong> ${response.permissions.join(', ')}</p>
                    </div>
                    <p style="color: var(--neutral-600); font-size: 0.9rem;">Redirecting to demo platform with admin privileges...</p>
                </div>
            `;

            modalFooter.innerHTML = `
                <div style="width: 100%; text-align: center;">
                    <div class="spinner-modern" style="margin: 0 auto;"></div>
                </div>
            `;
        }

        function showAdminCredentials() {
            console.log('🔍 Admin Credentials:');
            console.log('<EMAIL> / ArionYusuf2025!');
            console.log('<EMAIL> / ArionLibor2025!');
        }

        function showLoginError(fieldName, message) {
            const errorDiv = document.getElementById(`${fieldName}Error`);
            const inputField = document.getElementById(fieldName);

            errorDiv.textContent = message;
            errorDiv.classList.add('show');
            inputField.classList.add('error');
        }

        function isValidEmailFormat(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        // Demo Access Manager
        class DemoAccessManager {
            constructor() {
                this.init();
            }

            init() {
                document.getElementById('submitAccessRequest').addEventListener('click', this.handleSubmit.bind(this));
                this.setupValidation();

                document.getElementById('demoAccessModal').addEventListener('click', (e) => {
                    if (e.target === e.currentTarget) {
                        hideDemoAccessModal();
                    }
                });
            }

            setupValidation() {
                const inputs = document.querySelectorAll('#demoAccessForm input[required]:not([type="checkbox"]), #demoAccessForm select[required]');

                inputs.forEach(input => {
                    input.addEventListener('blur', () => this.validateField(input));
                    input.addEventListener('input', () => this.clearError(input));
                });

                // Add event listeners for checkboxes to clear errors when clicked
                const checkboxes = document.querySelectorAll('#demoAccessForm input[type="checkbox"][required]');
                checkboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', () => this.clearError(checkbox));
                });
            }

            validateField(field) {
                const value = field.value.trim();
                let isValid = true;
                let errorMessage = '';

                if (!value) {
                    isValid = false;
                    errorMessage = 'This field is required';
                } else if (field.type === 'email' && !this.isValidEmail(value)) {
                    isValid = false;
                    errorMessage = 'Please enter a valid email address';
                }

                if (!isValid) {
                    this.showError(field, errorMessage);
                } else {
                    this.clearError(field);
                }

                return isValid;
            }

            isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }

            clearError(field) {
                field.classList.remove('error');
                const errorElement = document.getElementById(`${field.name}Error`);
                if (errorElement) {
                    errorElement.classList.remove('show');
                } else {
                    // For checkboxes, find the error div in the parent form-group
                    const formGroup = field.closest('.form-group');
                    if (formGroup) {
                        const errorDiv = formGroup.querySelector('.error-message');
                        if (errorDiv) {
                            errorDiv.classList.remove('show');
                        }
                    }
                }
            }

            showError(field, message) {
                field.classList.add('error');
                const errorElement = document.getElementById(`${field.name}Error`);
                if (errorElement) {
                    errorElement.textContent = message;
                    errorElement.classList.add('show');
                } else {
                    // For checkboxes, find the error div in the parent form-group
                    const formGroup = field.closest('.form-group');
                    if (formGroup) {
                        const errorDiv = formGroup.querySelector('.error-message');
                        if (errorDiv) {
                            errorDiv.textContent = message;
                            errorDiv.classList.add('show');
                        }
                    }
                }
            }

            validateForm() {
                const requiredFields = document.querySelectorAll('#demoAccessForm input[required]:not([type="checkbox"]), #demoAccessForm select[required]');
                let isValid = true;

                requiredFields.forEach(field => {
                    if (!this.validateField(field)) {
                        isValid = false;
                    }
                });

                // Check password confirmation
                const password = document.getElementById('password').value;
                const confirmPassword = document.getElementById('confirmPassword').value;

                if (password !== confirmPassword) {
                    this.showError(document.getElementById('confirmPassword'), 'Passwords do not match');
                    isValid = false;
                }

                // Check password strength
                if (password && password.length < 8) {
                    this.showError(document.getElementById('password'), 'Password must be at least 8 characters');
                    isValid = false;
                }

                // Check data processing checkbox (required)
                const dataProcessing = document.getElementById('agreedToDataProcessing');
                if (!dataProcessing.checked) {
                    this.showError(dataProcessing, 'You must consent to data processing for demo access');
                    isValid = false;
                }

                // Check privacy checkbox (required)
                const privacy = document.getElementById('agreedToPrivacy');
                if (!privacy.checked) {
                    this.showError(privacy, 'You must agree to the privacy policy');
                    isValid = false;
                }

                // Marketing checkbox is optional, no validation needed

                return isValid;
            }

            async handleSubmit() {
                if (!this.validateForm()) {
                    return;
                }

                const submitBtn = document.getElementById('submitAccessRequest');
                submitBtn.disabled = true;
                submitBtn.textContent = 'Submitting...';

                try {
                    const formData = this.getFormData();
                    const response = await ArionUtils.api.call('request-demo-access', formData);

                    if (response.success) {
                        document.getElementById('submittedEmail').textContent = formData.workEmail;
                        document.getElementById('accessRequestForm').style.display = 'none';
                        document.getElementById('accessRequestSuccess').classList.add('show');
                    } else {
                        throw new Error(response.error || 'Submission failed');
                    }

                } catch (error) {
                    console.error('Demo access request failed:', error);
                    alert('Failed to submit request. Please try again.');
                } finally {
                    submitBtn.disabled = false;
                    submitBtn.textContent = 'Request Access';
                }
            }

            getFormData() {
                const form = document.getElementById('demoAccessForm');
                const formData = new FormData(form);
                const data = {};

                for (let [key, value] of formData.entries()) {
                    data[key] = value.trim();
                }

                // Add additional metadata
                data.requestSource = 'landing_page';
                data.timestamp = new Date().toISOString();
                data.userAgent = navigator.userAgent;
                data.referrer = document.referrer;

                return data;
            }
        }

        // Dynamic showcase functionality
        let currentSlide = 0;
        let slideInterval;

        function goToSlide(index) {
            const slides = document.querySelectorAll('.showcase-slide');
            const dots = document.querySelectorAll('.nav-dot');

            // Remove active class from all
            slides.forEach(slide => slide.classList.remove('active'));
            dots.forEach(dot => dot.classList.remove('active'));

            // Add active class to current
            slides[index].classList.add('active');
            dots[index].classList.add('active');

            currentSlide = index;
        }

        function nextSlide() {
            const totalSlides = document.querySelectorAll('.showcase-slide').length;
            currentSlide = (currentSlide + 1) % totalSlides;
            goToSlide(currentSlide);
        }

        // Initialize dynamic elements
        function initializeDynamicElements() {
            // Auto-rotate slides every 5 seconds
            slideInterval = setInterval(nextSlide, 5000);

            // Pause auto-rotation when user hovers over showcase
            const showcase = document.querySelector('.showcase-container');
            if (showcase) {
                showcase.addEventListener('mouseenter', () => {
                    clearInterval(slideInterval);
                });

                showcase.addEventListener('mouseleave', () => {
                    slideInterval = setInterval(nextSlide, 5000);
                });
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            new DemoAccessManager();
            initializeLoginModal();
            initializeDynamicElements();
        });
    </script>

    <script src="src/config.js"></script>
    <script src="src/utils.js"></script>
</body>

</html>
