/**
 * ArionComply Context-Aware Topic Filter System
 * Enhanced compliance topic validation with conversation context
 * Version: 2.0.2 (Browser Compatible)
 */

(function() {
  'use strict';

  class ContextAwareComplianceFilter {
    constructor() {
      // Track conversation state
      this.conversationState = {
        lastComplianceTopics: [],
        recentQueries: [],
        isInComplianceContext: false,
        lastValidationTime: null,
        complianceSession: false
      };

      // Conversational continuation phrases - always allow these
      this.conversationContinuers = [
        // Direct continuations
        'continue', 'more', 'next', 'go on', 'keep going', 'proceed',
        'tell me more', 'explain more', 'provide more', 'give me more',
        'what else', 'anything else', 'further details', 'elaborate',
        
        // Questions/clarifications
        'how', 'what', 'when', 'where', 'why', 'which', 'who',
        'can you', 'could you', 'would you', 'please', 'help',
        'explain', 'clarify', 'show', 'demonstrate', 'example',
        
        // Acknowledgments that might lead to questions
        'ok', 'okay', 'got it', 'understood', 'thanks', 'thank you',
        'yes', 'yeah', 'right', 'correct', 'good', 'great',
        
        // Short responses
        'and', 'also', 'but', 'however', 'what about', 'how about'
      ];

      // Strict whitelist of allowed compliance topics (enhanced)
      this.allowedTopics = {
        // ISO Standards
        iso: [
          'iso 27001', 'iso27001', 'iso 9001', 'iso9001', 'iso 14001', 'iso14001',
          'iso 22301', 'iso22301', 'iso 20000', 'iso20000', 'iso 31000', 'iso31000',
          'information security management', 'quality management', 'environmental management',
          'business continuity', 'service management', 'risk management framework',
          'iso certification', 'iso audit', 'iso implementation', 'iso requirements'
        ],

        // GDPR and Privacy
        gdpr: [
          'gdpr', 'general data protection regulation', 'data protection', 'privacy',
          'data subject rights', 'data processing', 'consent management', 'dpo',
          'data protection officer', 'privacy impact assessment', 'pia', 'dpia',
          'data breach', 'right to be forgotten', 'data portability', 'lawful basis',
          'personal data', 'sensitive data', 'data controller', 'data processor',
          'privacy by design', 'privacy by default', 'ccpa', 'california consumer privacy act'
        ],

        // AI Governance and Ethics (Enhanced)
        aiGovernance: [
          'ai governance', 'artificial intelligence governance', 'ai ethics', 'ai compliance',
          'ai systems', 'ai risk management', 'ai risk assessment', 'ai implementation',
          'ai deployment', 'ai monitoring systems', 'ai framework', 'ai policy',
          'algorithmic accountability', 'ai bias', 'machine learning ethics', 'ai audit',
          'responsible ai', 'ai transparency', 'explainable ai', 'ai fairness',
          'automated decision making', 'ai safety', 'ai regulation', 'ai standards',
          'ai act', 'european ai act', 'algorithmic impact assessment', 'ai monitoring'
        ],

        // Security Frameworks
        security: [
          'cybersecurity', 'information security', 'network security', 'data security',
          'security controls', 'security policies', 'security procedures', 'security audit',
          'vulnerability management', 'threat assessment', 'security incident', 'siem',
          'access control', 'identity management', 'multi factor authentication', 'mfa',
          'encryption', 'penetration testing', 'security monitoring', 'security awareness'
        ],

        // Compliance Frameworks
        frameworks: [
          'soc2', 'soc 2', 'sox', 'sarbanes oxley', 'pci dss', 'pci-dss', 'hipaa',
          'nist', 'coso', 'cobit', 'itil', 'cis controls', 'nist cybersecurity framework',
          'nist csf', 'compliance framework', 'regulatory compliance', 'audit framework'
        ],

        // Audit and Assessment
        audit: [
          'compliance audit', 'internal audit', 'external audit', 'audit preparation',
          'audit readiness', 'audit evidence', 'audit findings', 'corrective actions',
          'compliance assessment', 'risk assessment', 'control testing', 'audit trail',
          'audit documentation', 'compliance monitoring', 'audit committee'
        ],

        // Risk Management
        risk: [
          'risk management', 'risk assessment', 'risk analysis', 'risk mitigation',
          'risk register', 'risk monitoring', 'operational risk', 'cyber risk',
          'third party risk', 'vendor risk', 'supply chain risk', 'risk appetite',
          'risk tolerance', 'risk governance', 'enterprise risk management', 'erm'
        ],

        // Policy and Documentation
        policy: [
          'compliance policy', 'security policy', 'privacy policy', 'data policy',
          'information security policy', 'acceptable use policy', 'incident response policy',
          'policy management', 'policy framework', 'procedures', 'guidelines',
          'standards', 'compliance documentation', 'policy review'
        ],

        // Training and Awareness
        training: [
          'compliance training', 'security awareness', 'privacy training', 'policy training',
          'regulatory training', 'compliance education', 'security education',
          'awareness program', 'training compliance', 'certification training'
        ]
      };

      // Forbidden topics - but more contextual now
      this.strictlyForbiddenTopics = [
        // Only clearly non-business topics
        'weather', 'sports', 'entertainment', 'movies', 'music', 'games',
        'food', 'recipes', 'travel', 'vacation', 'dating', 'relationship',
        'fitness', 'medical diagnosis', 'therapy', 'personal problems',
        'homework', 'school assignment', 'essay writing', 'creative writing',
        'story', 'poem', 'art', 'photography', 'cooking'
      ];

      // Pre-defined rejection messages
      this.rejectionMessages = {
        general: {
          title: "Outside Our Expertise",
          message: "ArionComply is exclusively designed for compliance, security, and governance topics. We specialize in ISO standards, GDPR, AI governance, SOC2, and related compliance frameworks.",
          suggestion: "Please ask questions related to compliance, security policies, regulatory requirements, or audit preparation."
        },
        
        business: {
          title: "Business Consultation Required",
          message: "This appears to be a general business question outside our compliance focus. ArionComply is dedicated solely to compliance and governance matters.",
          suggestion: "For broader business questions, please contact our sales team who can direct you to appropriate consulting resources."
        },
        
        technical: {
          title: "Technical Implementation Focus",
          message: "ArionComply focuses on compliance requirements rather than technical implementation details. We help with what you need to comply with, not how to build it.",
          suggestion: "Try asking about compliance requirements, security policies, or regulatory standards instead."
        },
        
        personal: {
          title: "Professional Compliance Only",
          message: "ArionComply is designed for professional compliance and governance topics only. We don't provide personal advice or general information.",
          suggestion: "Please ask about ISO standards, GDPR compliance, security frameworks, or audit preparation."
        }
      };
    }

    /**
     * Enhanced validation with context awareness
     * @param {string} query - User input to validate
     * @returns {Object} - Validation result with isCompliant flag and response
     */
    validateQuery(query) {
      const normalizedQuery = query.toLowerCase().trim();
      const currentTime = Date.now();
      
      // Add to recent queries for context
      this.conversationState.recentQueries.push({
        query: normalizedQuery,
        timestamp: currentTime
      });
      
      // Keep only last 5 queries
      if (this.conversationState.recentQueries.length > 5) {
        this.conversationState.recentQueries.shift();
      }

      // 1. ALWAYS allow conversation continuers (even single words)
     // if (this.isConversationContinuer(normalizedQuery)) {
     //   return {
     //     isCompliant: true,
     //     category: 'conversation',
     //     confidence: 1.0,
     //     reason: 'Conversational continuation',
     //     contextual: true
     //   };
     // }

      // 2. If we're in a compliance session, be more lenient
    //  if (this.conversationState.complianceSession) {
        // Allow short queries if we recently had compliance topics
    //    if (normalizedQuery.length >= 1 && this.hasRecentComplianceContext()) {
    //      return {
    //        isCompliant: true,
    //        category: 'contextual_compliance',
    //        confidence: 0.8,
    //        reason: 'Short query in compliance context',
    //        contextual: true
    //      };
    //    }
    //  }

      // 3. Check for explicit compliance topics
      const allowedMatch = this.checkAllowedTopics(normalizedQuery);
      if (allowedMatch.found) {
        // Mark as compliance session
        this.conversationState.complianceSession = true;
        this.conversationState.isInComplianceContext = true;
        this.conversationState.lastComplianceTopics = allowedMatch.matchedTopics;
        this.conversationState.lastValidationTime = currentTime;
        
        return {
          isCompliant: true,
          category: allowedMatch.category,
          confidence: allowedMatch.confidence,
          topics: allowedMatch.matchedTopics,
          contextual: false
        };
      }

      // 4. Only reject if clearly non-compliance AND not in context
      const forbiddenMatch = this.checkStrictlyForbiddenTopics(normalizedQuery);
      if (forbiddenMatch.found && forbiddenMatch.confidence > 0.4) {
        return {
          isCompliant: false,
          category: forbiddenMatch.category,
          confidence: forbiddenMatch.confidence,
          rejectionMessage: this.formatRejectionMessage(forbiddenMatch.category)
        };
      }

      // 5. Default behavior based on context
      if (this.conversationState.complianceSession) {
        // In compliance context or short query - allow with guidance
        return {
          isCompliant: true,
          category: 'assumed_compliance',
          confidence: 0.6,
          reason: 'Assuming compliance context - will provide guidance if off-topic'
        };
      }

      // 6. Longer queries with no clear compliance indicators
      return {
        isCompliant: false,
        category: 'general',
        confidence: 0.7,
        rejectionMessage: this.formatRejectionMessage('general')
      };
    }

    /**
     * Check if query is a conversation continuer
     * @param {string} query - Normalized query
     * @returns {boolean}
     */
    isConversationContinuer(query) {
      // Exact matches
      if (this.conversationContinuers.includes(query)) {
        return true;
      }
      
      // Starts with common continuers
      const startsWithContinuer = this.conversationContinuers.some(continuer => 
        query.startsWith(continuer + ' ') || query.startsWith(continuer + '?')
      );
      
      return startsWithContinuer;
    }

    /**
     * Check if we have recent compliance context
     * @returns {boolean}
     */
    hasRecentComplianceContext() {
      const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
      return this.conversationState.lastValidationTime && 
             this.conversationState.lastValidationTime > fiveMinutesAgo &&
             this.conversationState.lastComplianceTopics.length > 0;
    }

    /**
     * Check against allowed topics (same as before)
     */
    checkAllowedTopics(query) {
      let bestMatch = { found: false, confidence: 0, category: null, matchedTopics: [] };

      for (const [category, topics] of Object.entries(this.allowedTopics)) {
        for (const topic of topics) {
          if (query.includes(topic)) {
            const confidence = Math.min(0.9, topic.length / Math.max(query.length, topic.length));
            if (confidence > bestMatch.confidence) {
              bestMatch = {
                found: true,
                confidence: confidence,
                category: category,
                matchedTopics: [topic]
              };
            }
          }
        }
      }

      // Lower threshold for allowed topics
      if (bestMatch.confidence < 0.05) {
        bestMatch.found = false;
      }

      return bestMatch;
    }

    /**
     * Check strictly forbidden topics
     */
    checkStrictlyForbiddenTopics(query) {
      let bestMatch = { found: false, confidence: 0, category: 'general' };

      for (const topic of this.strictlyForbiddenTopics) {
        if (query.includes(topic)) {
          const confidence = topic.length / query.length;
          if (confidence > bestMatch.confidence) {
            bestMatch = {
              found: true,
              confidence: confidence,
              category: this.categorizeForbiddenTopic(topic)
            };
          }
        }
      }

      return bestMatch;
    }

    /**
     * Reset conversation context (call this when starting new conversation)
     */
    resetContext() {
      this.conversationState = {
        lastComplianceTopics: [],
        recentQueries: [],
        isInComplianceContext: false,
        lastValidationTime: null,
        complianceSession: false
      };
    }

    /**
     * Get current conversation context
     */
    getContext() {
      return {
        ...this.conversationState,
        isActive: this.hasRecentComplianceContext()
      };
    }

    // ... (rest of the methods remain the same as original)
    categorizeForbiddenTopic(topic) {
      const personalTopics = ['dating', 'relationship', 'medical', 'personal', 'fitness'];
      if (personalTopics.some(t => topic.includes(t))) return 'personal';
      return 'general';
    }

    formatRejectionMessage(category) {
      const template = this.rejectionMessages[category] || this.rejectionMessages.general;
      
      return {
        type: 'rejection',
        title: template.title,
        message: template.message,
        suggestion: template.suggestion,
        allowedTopics: this.getSampleAllowedTopics(),
        timestamp: new Date().toISOString()
      };
    }

    getSampleAllowedTopics() {
      return [
        "How do I prepare for ISO 27001 certification?",
        "What are GDPR data subject rights?",
        "How to implement AI governance framework?",
        "SOC2 compliance requirements explained",
        "Risk assessment methodology best practices",
        "Security policy template guidance"
      ];
    }
  }

  // Create global instance
  const contextAwareFilter = new ContextAwareComplianceFilter();

  // Export to global scope
  window.ArionComply = {
    validateQuery: function(query) {
      return contextAwareFilter.validateQuery(query);
    },
    
    getComplianceResponse: function(query) {
      const validation = contextAwareFilter.validateQuery(query);
      
      if (!validation.isCompliant) {
        return {
          success: false,
          response: validation.rejectionMessage,
          blocked: true
        };
      }
      
      return {
        success: true,
        category: validation.category,
        confidence: validation.confidence,
        topics: validation.topics || [],
        contextual: validation.contextual || false,
        reason: validation.reason,
        blocked: false
      };
    },
    
    resetContext: function() {
      return contextAwareFilter.resetContext();
    },
    
    getContext: function() {
      return contextAwareFilter.getContext();
    }
  };

  console.log('✅ ArionComply Context-Aware Compliance Filter v2.0 loaded successfully');

})();
