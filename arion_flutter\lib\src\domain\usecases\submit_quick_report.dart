import '../repositories/feedback_repository.dart';

class SubmitQuickReport {
  final FeedbackRepository repo;
  SubmitQuickReport(this.repo);

  Future<void> call({
    required String messageContent,
    required String issueType,
    required String description,
  }) => repo.submitQuickMessageReport(
    messageContent: messageContent,
    issueType: issueType,
    description: description,
  );
}
