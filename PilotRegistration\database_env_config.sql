-- =====================================================
-- PRODUCT REGISTRATION DATABASE SCHEMA
-- =====================================================
-- Enhanced schema for product-based pilot/waitlist programs
-- Run this in your Supabase SQL editor or via migration

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- MAIN PRODUCT REGISTRATION TABLE
-- =====================================================

-- Drop existing table if it exists (for development/testing)
DROP TABLE IF EXISTS public.product_registrations CASCADE;

-- Create product_registrations table
CREATE TABLE public.product_registrations (
    -- Primary key and identification
    id                  UUID            PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Product and program identification
    product_id          TEXT            NOT NULL DEFAULT 'default',
    program_type        TEXT            NOT NULL DEFAULT 'pilot',
    source_url          TEXT,
    utm_source          TEXT,
    utm_campaign        TEXT,
    
    -- Personal information
    full_name           TEXT            NOT NULL,
    email               TEXT            NOT NULL,
    company             TEXT            NOT NULL,
    job_title           TEXT            NOT NULL,
    primary_business    TEXT            NOT NULL,
    company_size        TEXT            NOT NULL,
    phone               TEXT            NOT NULL,
    use_case            TEXT            NOT NULL,
    timeline            TEXT,
    
    -- Technical metadata
    ip_address          INET,
    user_agent          TEXT,
    submission_source   TEXT            DEFAULT 'web',
    
    -- Audit timestamps
    created_at          TIMESTAMPTZ     DEFAULT timezone('utc', now()),
    updated_at          TIMESTAMPTZ     DEFAULT timezone('utc', now()),
    
    -- Status and workflow fields
    status              TEXT            DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'waitlist', 'contacted', 'onboarded')),
    priority            TEXT            DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    assigned_to         UUID,
    notes               TEXT,
    
    -- Communication tracking
    last_contacted      TIMESTAMPTZ,
    contact_count       INTEGER         DEFAULT 0,
    
    -- Constraints for data integrity
    CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT valid_product_id CHECK (product_id IN ('arioncomply', 'arionsecure', 'arionanalytics', 'arionplatform', 'default')),
    CONSTRAINT valid_program_type CHECK (program_type IN ('pilot', 'waitlist', 'beta', 'early_access')),
    CONSTRAINT valid_primary_business CHECK (primary_business IN ('Technology', 'Financial Services', 'Healthcare', 'Manufacturing', 'Government', 'Education', 'Retail', 'Energy', 'Telecommunications', 'Other')),
    CONSTRAINT valid_company_size CHECK (company_size IN ('1-10', '11-50', '51-200', '201-1000', '1001-5000', '5000+')),
    CONSTRAINT valid_timeline CHECK (timeline IS NULL OR timeline IN ('Immediately', '1-3 months', '3-6 months', '6-12 months', '12+ months', 'Just exploring')),
    CONSTRAINT full_name_length CHECK (char_length(full_name) BETWEEN 2 AND 100),
    CONSTRAINT company_length CHECK (char_length(company) BETWEEN 2 AND 100),
    CONSTRAINT job_title_length CHECK (char_length(job_title) BETWEEN 2 AND 100),
    CONSTRAINT phone_length CHECK (char_length(phone) BETWEEN 10 AND 20),
    CONSTRAINT use_case_length CHECK (char_length(use_case) BETWEEN 20 AND 1000)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Unique constraint on email per product per program type (prevent duplicate registrations)
CREATE UNIQUE INDEX product_registrations_email_product_program_idx 
    ON public.product_registrations (LOWER(email), product_id, program_type);

-- Performance indexes
CREATE INDEX product_registrations_product_id_idx 
    ON public.product_registrations (product_id);

CREATE INDEX product_registrations_program_type_idx 
    ON public.product_registrations (program_type);

CREATE INDEX product_registrations_created_at_idx 
    ON public.product_registrations (created_at DESC);

CREATE INDEX product_registrations_status_idx 
    ON public.product_registrations (status);

CREATE INDEX product_registrations_priority_idx 
    ON public.product_registrations (priority);

CREATE INDEX product_registrations_ip_address_idx 
    ON public.product_registrations (ip_address);

CREATE INDEX product_registrations_assigned_to_idx 
    ON public.product_registrations (assigned_to);

-- Composite indexes for common queries
CREATE INDEX product_registrations_product_status_idx 
    ON public.product_registrations (product_id, status);

CREATE INDEX product_registrations_program_status_idx 
    ON public.product_registrations (program_type, status);

CREATE INDEX product_registrations_company_business_idx 
    ON public.product_registrations (primary_business, company_size);

-- Full-text search index for searching registrations
CREATE INDEX product_registrations_search_idx 
    ON public.product_registrations 
    USING gin(to_tsvector('english', full_name || ' ' || company || ' ' || job_title || ' ' || primary_business || ' ' || use_case));

-- =====================================================
-- ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable Row-Level Security
ALTER TABLE public.product_registrations ENABLE ROW LEVEL SECURITY;

-- Policy: Allow service role full access (for Edge Function)
CREATE POLICY "service_role_full_access"
    ON public.product_registrations
    FOR ALL 
    USING (auth.role() = 'service_role')
    WITH CHECK (auth.role() = 'service_role');

-- Policy: Allow users to view their own registrations (optional)
CREATE POLICY "users_view_own_registrations"
    ON public.product_registrations
    FOR SELECT 
    USING (auth.email() = email);

-- Policy: Block anonymous access
CREATE POLICY "block_anonymous_access"
    ON public.product_registrations
    FOR ALL 
    USING (auth.role() != 'anon')
    WITH CHECK (auth.role() != 'anon');

-- =====================================================
-- TRIGGERS AND FUNCTIONS
-- =====================================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc', now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER update_product_registrations_updated_at
    BEFORE UPDATE ON public.product_registrations
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Function to normalize email addresses and clean data
CREATE OR REPLACE FUNCTION normalize_registration_data()
RETURNS TRIGGER AS $$
BEGIN
    -- Normalize email to lowercase
    NEW.email = LOWER(TRIM(NEW.email));
    
    -- Trim whitespace from text fields
    NEW.full_name = TRIM(NEW.full_name);
    NEW.company = TRIM(NEW.company);
    NEW.job_title = TRIM(NEW.job_title);
    NEW.phone = TRIM(NEW.phone);
    NEW.use_case = TRIM(NEW.use_case);
    
    -- Clean source URL
    IF NEW.source_url IS NOT NULL THEN
        NEW.source_url = TRIM(NEW.source_url);
        IF NEW.source_url = '' THEN
            NEW.source_url = NULL;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to normalize data before insert/update
CREATE TRIGGER normalize_registration_data_trigger
    BEFORE INSERT OR UPDATE ON public.product_registrations
    FOR EACH ROW 
    EXECUTE FUNCTION normalize_registration_data();

-- Function to update contact tracking
CREATE OR REPLACE FUNCTION update_contact_tracking()
RETURNS TRIGGER AS $$
BEGIN
    -- If status changed to 'contacted', update tracking fields
    IF OLD.status != 'contacted' AND NEW.status = 'contacted' THEN
        NEW.last_contacted = timezone('utc', now());
        NEW.contact_count = COALESCE(OLD.contact_count, 0) + 1;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for contact tracking
CREATE TRIGGER update_contact_tracking_trigger
    BEFORE UPDATE ON public.product_registrations
    FOR EACH ROW 
    EXECUTE FUNCTION update_contact_tracking();

-- =====================================================
-- ANALYTICS AND REPORTING VIEWS
-- =====================================================

-- View for registration statistics by product
CREATE OR REPLACE VIEW product_registration_stats AS
SELECT 
    product_id,
    program_type,
    status,
    COUNT(*) as registration_count,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '24 hours') as last_24h,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '7 days') as last_7d,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '30 days') as last_30d,
    MIN(created_at) as first_registration,
    MAX(created_at) as latest_registration,
    COUNT(DISTINCT ip_address) as unique_ips,
    COUNT(DISTINCT company) as unique_companies
FROM public.product_registrations
GROUP BY product_id, program_type, status
ORDER BY product_id, program_type, status;

-- View for daily registration trends
CREATE OR REPLACE VIEW daily_registration_trends AS
SELECT 
    DATE(created_at) as registration_date,
    product_id,
    program_type,
    COUNT(*) as daily_count,
    COUNT(DISTINCT ip_address) as unique_ips,
    COUNT(DISTINCT company) as unique_companies,
    array_agg(DISTINCT primary_business) as industries
FROM public.product_registrations
WHERE created_at >= NOW() - INTERVAL '90 days'
GROUP BY DATE(created_at), product_id, program_type
ORDER BY registration_date DESC, product_id, program_type;

-- View for company analysis
CREATE OR REPLACE VIEW company_analysis AS
SELECT 
    primary_business,
    company_size,
    COUNT(*) as registration_count,
    array_agg(DISTINCT product_id) as products_of_interest,
    AVG(CASE 
        WHEN timeline = 'Immediately' THEN 1
        WHEN timeline = '1-3 months' THEN 2
        WHEN timeline = '3-6 months' THEN 3
        WHEN timeline = '6-12 months' THEN 4
        WHEN timeline = '12+ months' THEN 5
        WHEN timeline = 'Just exploring' THEN 6
        ELSE NULL
    END) as avg_timeline_urgency
FROM public.product_registrations
WHERE created_at >= NOW() - INTERVAL '6 months'
GROUP BY primary_business, company_size
ORDER BY registration_count DESC;

-- View for conversion funnel analysis
CREATE OR REPLACE VIEW conversion_funnel AS
SELECT 
    product_id,
    program_type,
    COUNT(*) as total_registrations,
    COUNT(*) FILTER (WHERE status = 'contacted') as contacted,
    COUNT(*) FILTER (WHERE status = 'approved') as approved,
    COUNT(*) FILTER (WHERE status = 'onboarded') as onboarded,
    ROUND(
        COUNT(*) FILTER (WHERE status = 'contacted')::decimal / 
        NULLIF(COUNT(*), 0) * 100, 2
    ) as contact_rate_percent,
    ROUND(
        COUNT(*) FILTER (WHERE status = 'approved')::decimal / 
        NULLIF(COUNT(*), 0) * 100, 2
    ) as approval_rate_percent,
    ROUND(
        COUNT(*) FILTER (WHERE status = 'onboarded')::decimal / 
        NULLIF(COUNT(*), 0) * 100, 2
    ) as onboarding_rate_percent
FROM public.product_registrations
GROUP BY product_id, program_type
ORDER BY total_registrations DESC;

-- =====================================================
-- SAMPLE DATA (for testing - remove in production)
-- =====================================================

-- Insert sample registrations for each product (remove in production)
/*
INSERT INTO public.product_registrations (
    product_id, program_type, full_name, email, company, job_title, 
    primary_business, company_size, phone, use_case, timeline,
    ip_address, user_agent
) VALUES 
    ('arioncomply', 'pilot', 'John Smith', '<EMAIL>', 'Example Corp', 'CISO', 
     'Technology', '201-1000', '******-123-4567', 
     'We need to streamline our ISO 27001 compliance process and automate evidence collection for our upcoming audit.',
     '1-3 months', '127.0.0.1', 'Mozilla/5.0 (Test Browser)'),
    
    ('arionsecure', 'waitlist', 'Jane Doe', '<EMAIL>', 'Security Corp', 'Head of Security', 
     'Financial Services', '1001-5000', '******-987-6543',
     'Looking for advanced threat detection capabilities to enhance our current security stack.',
     '3-6 months', '*********', 'Mozilla/5.0 (Test Browser)'),
     
    ('arionanalytics', 'pilot', 'Bob Johnson', '<EMAIL>', 'Data Corp', 'Head of Analytics',
     'Healthcare', '51-200', '******-456-7890',
     'Need better business intelligence tools to analyze patient outcomes and operational efficiency.',
     'Immediately', '*********', 'Mozilla/5.0 (Test Browser)');
*/

-- =====================================================
-- GRANT PERMISSIONS
-- =====================================================

-- Grant necessary permissions to authenticated users
GRANT SELECT ON public.product_registrations TO authenticated;
GRANT SELECT ON product_registration_stats TO authenticated;
GRANT SELECT ON daily_registration_trends TO authenticated;
GRANT SELECT ON company_analysis TO authenticated;
GRANT SELECT ON conversion_funnel TO authenticated;

-- Service role should have full access (already handled by RLS policy)

-- =====================================================
-- ENVIRONMENT VARIABLES CONFIGURATION
-- =====================================================

/*
=== ENVIRONMENT VARIABLES SETUP ===

Create a .env file in your project root with the following variables:

--- SUPABASE CONFIGURATION ---
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

--- RECAPTCHA CONFIGURATION ---
# Get these from https://www.google.com/recaptcha/admin
RECAPTCHA_SITE_KEY=your-recaptcha-site-key-here
RECAPTCHA_SECRET_KEY=your-recaptcha-secret-key-here

--- PRODUCT CONFIGURATION ---
# Rate limiting (requests per hour per IP per product)
RATE_LIMIT_REQUESTS_PER_HOUR=8

# Notification settings (optional)
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url
TEAM_EMAIL=<EMAIL>

--- OPTIONAL CONFIGURATION ---
# Environment
NODE_ENV=production

# Application settings
APP_NAME="ArionNetworks Product Registration"
APP_VERSION=1.0.0
COMPANY_DOMAIN=arionetworks.com

--- DEPLOYMENT CONFIGURATION ---
# For Verpex or other hosting
DATABASE_URL=postgresql://user:password@host:port/database
REDIS_URL=redis://localhost:6379 (for advanced rate limiting)

=== SUPABASE EDGE FUNCTION ENVIRONMENT VARIABLES ===

These need to be set in your Supabase project dashboard:
1. Go to your Supabase project dashboard
2. Navigate to Edge Functions
3. Set the following environment variables:

RECAPTCHA_SECRET_KEY=your-recaptcha-secret-key-here
RATE_LIMIT_REQUESTS_PER_HOUR=8
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url (optional)
TEAM_EMAIL=<EMAIL> (optional)

Note: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are automatically provided by Supabase

=== FRONTEND CONFIGURATION ===

Update these values in your HTML file:
- SUPABASE_URL: Replace 'YOUR_SUPABASE_URL' with your actual Supabase URL
- RECAPTCHA_SITE_KEY: Replace 'YOUR_RECAPTCHA_SITE_KEY' with your reCAPTCHA site key

=== PRODUCT PAGE INTEGRATION ===

Add these buttons to your product pages:

<!-- ArionComply ISO page (iso.arionetworks.com) -->
<a href="https://register.arionetworks.com/register.html?product=arioncomply&type=pilot&source=https://iso.arionetworks.com&utm_source=iso_page&utm_campaign=pilot_launch" 
   class="pilot-btn">
   Join ArionComply Pilot Program
</a>

<a href="https://register.arionetworks.com/register.html?product=arioncomply&type=waitlist&source=https://iso.arionetworks.com&utm_source=iso_page&utm_campaign=waitlist" 
   class="waitlist-btn">
   Join ArionComply Waitlist
</a>

<!-- ArionSecure Security page -->
<a href="https://register.arionetworks.com/register.html?product=arionsecure&type=pilot&source=https://security.arionetworks.com&utm_source=security_page&utm_campaign=pilot_launch" 
   class="pilot-btn">
   Join ArionSecure Pilot
</a>

<!-- ArionAnalytics Analytics page -->
<a href="https://register.arionetworks.com/register.html?product=arionanalytics&type=pilot&source=https://analytics.arionetworks.com&utm_source=analytics_page&utm_campaign=pilot_launch" 
   class="pilot-btn">
   Join ArionAnalytics Pilot
</a>

<!-- ArionPlatform Integrated Platform page -->
<a href="https://register.arionetworks.com/register.html?product=arionplatform&type=early_access&source=https://platform.arionetworks.com&utm_source=platform_page&utm_campaign=early_access" 
   class="early-access-btn">
   Get Early Access to ArionPlatform
</a>

=== URL PARAMETER REFERENCE ===

Required Parameters:
- product: arioncomply, arionsecure, arionanalytics, arionplatform, default
- type: pilot, waitlist, beta, early_access

Optional Parameters:
- source: The referring page URL
- utm_source: Source tracking (e.g., iso_page, security_page)
- utm_campaign: Campaign tracking (e.g., pilot_launch, waitlist)

=== RECAPTCHA SETUP ===

1. Visit https://www.google.com/recaptcha/admin
2. Create a new site
3. Choose reCAPTCHA v3
4. Add your domains:
   - arionetworks.com
   - register.arionetworks.com
   - iso.arionetworks.com
   - security.arionetworks.com
   - analytics.arionetworks.com
   - platform.arionetworks.com
   - localhost (for testing)
5. Copy the Site Key and Secret Key
6. Update your environment variables

=== SECURITY CHECKLIST ===

□ Database RLS policies are enabled
□ Service role key is not exposed to frontend
□ reCAPTCHA keys are properly configured
□ Rate limiting is configured per product
□ HTTPS is enabled on all domains
□ Environment variables are secure
□ Database backups are configured
□ Monitoring is set up for each product
□ Product ID validation is in place
□ Email normalization is working
□ Duplicate prevention is active

*/

-- =====================================================
-- MAINTENANCE QUERIES
-- =====================================================

-- Query to check registration statistics by product
/*
SELECT 
    product_id,
    program_type,
    COUNT(*) as total_registrations,
    COUNT(DISTINCT DATE(created_at)) as active_days,
    COUNT(DISTINCT company) as unique_companies,
    MIN(created_at) as first_registration,
    MAX(created_at) as latest_registration
FROM product_registrations 
GROUP BY product_id, program_type 
ORDER BY total_registrations DESC;
*/

-- Query to identify high-value prospects
/*
SELECT 
    product_id,
    company,
    job_title,
    primary_business,
    company_size,
    timeline,
    use_case,
    created_at,
    CASE 
        WHEN company_size IN ('1001-5000', '5000+') AND timeline IN ('Immediately', '1-3 months') THEN 'High Priority'
        WHEN company_size IN ('201-1000', '1001-5000') AND timeline IN ('1-3 months', '3-6 months') THEN 'Medium Priority'
        ELSE 'Standard Priority'
    END as priority_level
FROM product_registrations
WHERE status = 'pending'
ORDER BY 
    CASE company_size 
        WHEN '5000+' THEN 1
        WHEN '1001-5000' THEN 2
        WHEN '201-1000' THEN 3
        ELSE 4
    END,
    CASE timeline
        WHEN 'Immediately' THEN 1
        WHEN '1-3 months' THEN 2
        WHEN '3-6 months' THEN 3
        ELSE 4
    END;
*/

-- Query to identify potential spam/abuse patterns
/*
SELECT 
    ip_address,
    COUNT(*) as registration_count,
    array_agg(DISTINCT product_id) as products,
    array_agg(DISTINCT email) as emails,
    MIN(created_at) as first_attempt,
    MAX(created_at) as latest_attempt
FROM product_registrations 
GROUP BY ip_address 
HAVING COUNT(*) > 5
ORDER BY registration_count DESC;
*/

-- Query to analyze conversion by industry and company size
/*
SELECT 
    primary_business,
    company_size,
    COUNT(*) as total_registrations,
    COUNT(*) FILTER (WHERE status = 'approved') as approved,
    COUNT(*) FILTER (WHERE status = 'onboarded') as onboarded,
    ROUND(
        COUNT(*) FILTER (WHERE status = 'approved')::decimal / 
        NULLIF(COUNT(*), 0) * 100, 2
    ) as approval_rate_percent
FROM product_registrations
WHERE created_at >= NOW() - INTERVAL '3 months'
GROUP BY primary_business, company_size
HAVING COUNT(*) >= 5
ORDER BY approval_rate_percent DESC, total_registrations DESC;
*/

-- Query for product performance comparison
/*
SELECT 
    product_id,
    COUNT(*) as total_registrations,
    COUNT(DISTINCT company) as unique_companies,
    COUNT(*) FILTER (WHERE program_type = 'pilot') as pilot_registrations,
    COUNT(*) FILTER (WHERE program_type = 'waitlist') as waitlist_registrations,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '30 days') as last_30_days,
    ROUND(AVG(char_length(use_case))) as avg_use_case_length
FROM product_registrations
GROUP BY product_id
ORDER BY total_registrations DESC;
*/

-- Clean up test data (use carefully!)
/*
DELETE FROM product_registrations 
WHERE email LIKE '%example.com' 
   OR email LIKE '%test.%' 
   OR full_name ILIKE '%test%'
   OR company ILIKE '%test%';
*/