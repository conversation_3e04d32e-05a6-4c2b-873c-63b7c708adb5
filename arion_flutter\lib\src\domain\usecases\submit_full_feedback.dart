import '../repositories/feedback_repository.dart';

class SubmitFullFeedback {
  final FeedbackRepository repo;
  SubmitFullFeedback(this.repo);

  Future<void> call({
    required bool isAdmin,
    required String id,
    required String type,
    required String priority,
    required String subject,
    required String message,
    String? additionalNotes,
    String? rating,
    String? contactMethod,
    String adminStatus = 'new',
    String? adminAssignee,
    String? adminNotes,
  }) => repo.submitFullFeedback(
    isAdmin: isAdmin,
    id: id,
    type: type,
    priority: priority,
    subject: subject,
    message: message,
    additionalNotes: additionalNotes,
    rating: rating,
    contactMethod: contactMethod,
    adminStatus: adminStatus,
    adminAssignee: adminAssignee,
    adminNotes: adminNotes,
  );
}
