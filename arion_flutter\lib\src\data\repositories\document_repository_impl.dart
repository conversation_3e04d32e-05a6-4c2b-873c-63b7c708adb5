import '../../core/network/api_client.dart';
import '../../core/services/config_service.dart';
import '../../core/services/session_service.dart';
import '../../domain/repositories/document_repository.dart';

class DocumentRepositoryImpl implements DocumentRepository {
  final ApiClient _api;
  final ConfigService _config;
  final SessionService _session;

  DocumentRepositoryImpl(this._api, this._config, this._session);

  @override
  Future<Map<String, dynamic>> getDocuments() async {
    await _config.load();
    final sessionId = await _ensureSession();
    final action = _config.actions['getDocuments'] ?? 'get-documents';
    final res = await _api.post(action, {}, sessionId: sessionId);
    return Map<String, dynamic>.from(res.data ?? {});
  }

  @override
  Future<Map<String, dynamic>> getDocument(String documentId) async {
    await _config.load();
    final sessionId = await _ensureSession();
    final action = _config.actions['getDocument'] ?? 'get-document';
    final res = await _api.post(action, {
      'documentId': documentId,
    }, sessionId: sessionId);
    return Map<String, dynamic>.from(res.data ?? {});
  }

  Future<String> _ensureSession() async {
    var sessionId = await _session.getSessionId();
    if (sessionId == null) {
      await _config.load();
      final action = _config.actions['initSession'] ?? 'init-session';
      final res = await _api.post(action, {
        'timestamp': DateTime.now().toIso8601String(),
        'user_type': 'regular',
      });
      final data = Map<String, dynamic>.from(res.data ?? {});
      sessionId =
          (data['sessionId'] ?? data['id'] ?? data['data']?['sessionId'] ?? '')
              as String;
      if (sessionId.isNotEmpty) {
        await _session.saveSessionId(sessionId);
      }
    }
    return sessionId;
  }
}
