import 'dart:async';
import 'package:flutter/material.dart';
import 'package:arion_flutter/src/presentation/widgets/feature_showcase.dart';
import 'package:arion_flutter/src/presentation/widgets/login_dialog.dart';
import 'package:arion_flutter/src/presentation/widgets/demo_access_dialog.dart';
import 'package:arion_flutter/src/presentation/widgets/privacy_policy_dialog.dart';

class LandingPage extends StatefulWidget {
  const LandingPage({super.key});

  @override
  State<LandingPage> createState() => _LandingPageState();
}

class _LandingPageState extends State<LandingPage> {
  int _currentSlide = 0;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(seconds: 5), (_) {
      setState(() => _currentSlide = (_currentSlide + 1) % 3);
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _HeroSection(),
                const SizedBox(height: 16),
                FeatureShowcase(
                  currentIndex: _currentSlide,
                  onDotTap: (i) => setState(() => _currentSlide = i),
                ),
                const SizedBox(height: 16),
                TextButton(
                  onPressed: () => showDialog(
                    context: context,
                    builder: (_) => const PrivacyPolicyDialog(),
                  ),
                  child: const Text('Privacy Policy'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _HeroSection extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isNarrow = constraints.maxWidth < 760;
        return Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF059669), Color(0xFF047857)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.all(Radius.circular(16)),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 28),
          child: Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 980),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Text(
                    'ArionComply Demo',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 30,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Revolutionize your compliance workflow with intelligent AI automation that delivers auditable results and complete transparency. Simplify regulatory management, accelerate policy development, and maintain continuous compliance effortlessly.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      height: 1.5,
                    ),
                  ),
                  const SizedBox(height: 20),
                  Flex(
                    direction: isNarrow ? Axis.vertical : Axis.horizontal,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _ActionTile(
                        icon: Icons.rocket_launch,
                        title: 'Get Started',
                        subtitle:
                            'New to ArionComply? Create your free account',
                        onTap: () async {
                          await showDialog(
                            context: context,
                            builder: (_) => const DemoAccessDialog(),
                          );
                        },
                      ),
                      SizedBox(
                        width: isNarrow ? 0 : 24,
                        height: isNarrow ? 16 : 0,
                      ),
                      _ActionTile(
                        icon: Icons.lock,
                        title: 'Sign In',
                        subtitle:
                            'Already registered? Access the demo platform',
                        onTap: () async {
                          await showDialog(
                            context: context,
                            builder: (_) => const LoginDialog(),
                          );
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class _ActionTile extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;
  const _ActionTile({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final w = MediaQuery.of(context).size.width;
    final tileWidth = w < 420 ? w - 48 : 360;
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: tileWidth.toDouble(),
        constraints: const BoxConstraints(minHeight: 120),
        padding: const EdgeInsets.all(14),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 16,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 28, color: const Color(0xFF059669)),
            const SizedBox(height: 10),
            Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.w700,
                fontSize: 16,
                color: Color(0xFF064E3B),
              ),
            ),
            const SizedBox(height: 6),
            Text(
              subtitle,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 12, color: Color(0xFF065F46)),
            ),
          ],
        ),
      ),
    );
  }
}
