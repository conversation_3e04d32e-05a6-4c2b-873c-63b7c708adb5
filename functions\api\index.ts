import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
// Environment variables
const SUPABASE_URL = Deno.env.get("SUPABASE_URL");
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY");
const CLAUDE_API_KEY = Deno.env.get("ANTHROPIC_API_KEY");
// Environment variables (add to your Supabase secrets)
const RESEND_API_KEY = Deno.env.get("RESEND_API_KEY"); // Get from resend.com
const FROM_EMAIL = Deno.env.get("FROM_EMAIL") || "<EMAIL>";
const SITE_URL = Deno.env.get("SITE_URL") || "https://iso.arionetworks.com";
const ADMIN_PASSWORD = Deno.env.get("ADMIN_PASSWORD") || "arion-admin-2025";
if (!CLAUDE_API_KEY) {
  throw new Error("ANTHROPIC_API_KEY not configured");
}
// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
// CORS headers - Enhanced for admin access
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type, x-api-key, x-session-id, accept, x-admin-token",
  "Access-Control-Allow-Methods": "POST, OPTIONS, GET",
  "Access-Control-Max-Age": "3600",
  "Access-Control-Allow-Credentials": "false",
};
// Goal detection patterns
const GOAL_PATTERNS = {
  iso_certification:
    /\b(iso|27001|certification|isms|information security management)\b/i,
  gdpr_audit: /\b(gdpr|privacy|data protection|personal data|consent)\b/i,
  ai_risk_management:
    /\b(ai|artificial intelligence|machine learning|algorithm|automated)\b/i,
  soa_generation: /\b(soa|statement of applicability|controls|annex)\b/i,
  capa_flow: /\b(capa|corrective|preventive|action|incident|nonconformity)\b/i,
};
// Goal information
const GOAL_INFO = {
  iso_certification: {
    title: "ISO 27001 Certification",
    questions: [
      "How many people are in your organization?",
      "Which industry are you in?",
      "Are your systems cloud-based, on-premises, or hybrid?",
      "What key assets do you want to protect?",
      "Do you already have any policies or registers?",
    ],
    documents: ["risk_register", "soa", "policy", "gap_analysis"],
  },
  gdpr_audit: {
    title: "GDPR Compliance",
    questions: [
      "What type of personal data do you process?",
      "What is the legal basis for processing?",
      "Do you transfer data outside the EU?",
      "How many data subjects are affected?",
      "Do you have a Data Protection Officer?",
    ],
    documents: ["ropa", "dpia", "policy", "assessment"],
  },
  ai_risk_management: {
    title: "AI Risk Management",
    questions: [
      "What AI systems are you deploying?",
      "What is the risk level of your AI systems?",
      "Are you subject to the EU AI Act?",
      "What data does your AI process?",
      "Do you have AI governance policies?",
    ],
    documents: ["assessment", "risk_register", "policy", "implementation_plan"],
  },
  soa_generation: {
    title: "Statement of Applicability",
    questions: [
      "Which ISO 27001 controls are applicable?",
      "What is your implementation status?",
      "Are there any excluded controls?",
      "What is your risk appetite?",
      "Do you have existing controls documentation?",
    ],
    documents: ["soa", "gap_analysis", "implementation_plan"],
  },
  capa_flow: {
    title: "CAPA Process",
    questions: [
      "What type of nonconformity occurred?",
      "What is the severity level?",
      "Who is responsible for corrective actions?",
      "What is the expected timeline?",
      "Are there similar incidents in the past?",
    ],
    documents: ["assessment", "implementation_plan", "audit_report"],
  },
};
// Document templates
const DOCUMENT_TEMPLATES = {
  risk_register: {
    title: "Information Security Risk Register",
    icon: "📋",
    description: "Comprehensive inventory of information security risks",
  },
  soa: {
    title: "Statement of Applicability",
    icon: "📄",
    description: "ISO 27001 control selection and justification document",
  },
  policy: {
    title: "Information Security Policy",
    icon: "📜",
    description: "Formal organizational security policies and procedures",
  },
  assessment: {
    title: "Compliance Assessment Report",
    icon: "🔍",
    description: "Detailed compliance assessment and recommendations",
  },
  ropa: {
    title: "Record of Processing Activities",
    icon: "🛡️",
    description: "GDPR-required record of data processing activities",
  },
  dpia: {
    title: "Data Protection Impact Assessment",
    icon: "🔒",
    description: "Privacy impact assessment for high-risk processing",
  },
  audit_report: {
    title: "Internal Audit Report",
    icon: "✅",
    description: "Internal audit findings and recommendations",
  },
  gap_analysis: {
    title: "Gap Analysis Report",
    icon: "📊",
    description: "Current state vs. required compliance analysis",
  },
  implementation_plan: {
    title: "Implementation Plan",
    icon: "🗺️",
    description: "Step-by-step compliance implementation roadmap",
  },
};
// Admin user configuration
const ADMIN_USERS = {
  "<EMAIL>": {
    name: "Yusuf",
    role: "Super Admin",
    permissions: ["demo_access", "admin_panel", "lead_management", "analytics"],
    company: "ArionNetworks",
    jobTitle: "CEO",
  },
  "<EMAIL>": {
    name: "Libor",
    role: "Technical Admin",
    permissions: ["demo_access", "admin_panel", "lead_management"],
    company: "ArionNetworks",
    jobTitle: "CTO",
  },
};
// Session management
class SessionManager {
  static async createSession(metadata = {}) {
    const sessionId = crypto.randomUUID();
    const { data, error } = await supabase
      .from("demo_sessions")
      .insert([
        {
          id: sessionId,
          metadata,
          user_type: metadata.user_type || "regular",
          admin_role: metadata.admin_role || null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ])
      .select()
      .single();
    if (error) throw error;
    return data;
  }
  static async updateSession(sessionId, updates) {
    const { data, error } = await supabase
      .from("demo_sessions")
      .update({
        ...updates,
        updated_at: new Date().toISOString(),
      })
      .eq("id", sessionId)
      .select()
      .single();
    if (error) throw error;
    return data;
  }
  static async getSession(sessionId) {
    const { data, error } = await supabase
      .from("demo_sessions")
      .select("*")
      .eq("id", sessionId)
      .single();
    if (error) throw error;
    return data;
  }
}
// Interaction logging
class InteractionLogger {
  static async logInteraction(sessionId, eventType, eventData = {}) {
    try {
      const { error } = await supabase.from("demo_interactions").insert([
        {
          session_id: sessionId,
          event_type: eventType,
          event_data: eventData,
          created_at: new Date().toISOString(),
        },
      ]);
      if (error) console.error("Logging error:", error);
    } catch (error) {
      console.error("Failed to log interaction:", error);
    }
  }
}
// Document generator
class DocumentGenerator {
  static generateDocument(docType, intakeData, goalType) {
    const companyName =
      this.extractCompanyName(intakeData) || "Your Organization";
    const industry = this.extractIndustry(intakeData) || "Technology Services";
    const currentDate = new Date().toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
    const template = DOCUMENT_TEMPLATES[docType];
    if (!template) throw new Error(`Unknown document type: ${docType}`);
    const content = this.generateContent(
      docType,
      companyName,
      industry,
      currentDate,
      intakeData
    );
    return {
      title: template.title,
      icon: template.icon,
      description: template.description,
      content,
      metadata: {
        docType,
        goalType,
        companyName,
        industry,
        generatedAt: currentDate,
      },
    };
  }
  static extractCompanyName(intakeData) {
    return (
      intakeData?.company ||
      intakeData?.organization ||
      intakeData?.companyName ||
      null
    );
  }
  static extractIndustry(intakeData) {
    return intakeData?.industry || intakeData?.sector || null;
  }
  static generateContent(docType, companyName, industry, date, intakeData) {
    switch (docType) {
      case "risk_register":
        return `# Information Security Risk Register

**Organization:** ${companyName}
**Industry:** ${industry}
**Date:** ${date}
**Version:** 1.0

## Executive Summary
This risk register identifies and assesses information security risks relevant to ${companyName}'s operations in the ${industry} sector.

## Identified Risks

### 1. Data Breach Risk
- **Risk ID:** ISR-001
- **Description:** Unauthorized access to customer data
- **Probability:** 3 (Medium)
- **Impact:** 5 (Catastrophic)
- **Risk Level:** 15 (High)
- **Mitigation:** Multi-factor authentication, encryption, access controls

### 2. Ransomware Attack
- **Risk ID:** ISR-002
- **Description:** Malicious encryption of business systems
- **Probability:** 4 (High)
- **Impact:** 4 (Major)
- **Risk Level:** 16 (High)
- **Mitigation:** Regular backups, endpoint protection, user training

### 3. Cloud Service Failure
- **Risk ID:** ISR-003
- **Description:** Extended outage of critical cloud services
- **Probability:** 2 (Low)
- **Impact:** 3 (Moderate)
- **Risk Level:** 6 (Medium)
- **Mitigation:** Multi-cloud strategy, backup systems, SLA monitoring

### 4. Insider Threat
- **Risk ID:** ISR-004
- **Description:** Malicious or accidental data exposure by employees
- **Probability:** 3 (Medium)
- **Impact:** 4 (Major)
- **Risk Level:** 12 (High)
- **Mitigation:** Background checks, access controls, monitoring

### 5. Third-Party Data Loss
- **Risk ID:** ISR-005
- **Description:** Data breach at vendor or supplier
- **Probability:** 3 (Medium)
- **Impact:** 3 (Moderate)
- **Risk Level:** 9 (Medium)
- **Mitigation:** Vendor assessments, contracts, monitoring

---
*This document is confidential and proprietary to ${companyName}.*`;
      case "soa":
        return `# Statement of Applicability - ISO 27001:2022

**Organization:** ${companyName}
**Industry:** ${industry}
**Date:** ${date}
**Version:** 1.0

## 1. Introduction
This Statement of Applicability (SoA) documents ${companyName}'s selection and implementation of ISO 27001:2022 Annex A controls.

## 2. Control Objectives and Controls

### A.5 Information Security Policies
**A.5.1 Policies for Information Security**
- **Applicable:** Yes
- **Implementation Status:** Implemented
- **Justification:** Essential for establishing management direction and support

### A.6 Organization of Information Security
**A.6.1 Internal Organization**
- **Applicable:** Yes
- **Implementation Status:** Implemented
- **Justification:** Defines roles and responsibilities for information security

**A.6.2 Mobile Devices and Teleworking**
- **Applicable:** Yes
- **Implementation Status:** Planned
- **Justification:** Critical for ${companyName}'s hybrid work environment

### A.7 Human Resource Security
**A.7.1 Prior to Employment**
- **Applicable:** Yes
- **Implementation Status:** Implemented
- **Justification:** Ensures personnel suitability and awareness

**A.7.2 During Employment**
- **Applicable:** Yes
- **Implementation Status:** Implemented
- **Justification:** Ongoing awareness and disciplinary process

**A.7.3 Termination and Change of Employment**
- **Applicable:** Yes
- **Implementation Status:** Partially Implemented
- **Justification:** Access removal procedures established

### A.8 Asset Management
**A.8.1 Responsibility for Assets**
- **Applicable:** Yes
- **Implementation Status:** Implemented
- **Justification:** Asset inventory and ownership essential

**A.8.2 Information Classification**
- **Applicable:** Yes
- **Implementation Status:** Planned
- **Justification:** Required for appropriate protection measures

**A.8.3 Media Handling**
- **Applicable:** Yes
- **Implementation Status:** Implemented
- **Justification:** Secure disposal and transfer procedures

---
*This SoA is reviewed annually and updated as needed to reflect changes in ${companyName}'s risk environment.*`;
      case "policy":
        return `# Information Security Policy

**Organization:** ${companyName}
**Effective Date:** ${date}
**Version:** 1.0

## 1. Purpose
This policy establishes the framework for information security at ${companyName} to protect our information assets and ensure business continuity.

## 2. Scope
This policy applies to all employees, contractors, and third parties who have access to ${companyName}'s information systems and data.

## 3. Information Security Objectives
- Protect confidentiality, integrity, and availability of information
- Comply with legal, regulatory, and contractual requirements
- Maintain customer trust and business reputation
- Ensure business continuity and minimize business risk

## 4. Roles and Responsibilities

### 4.1 Senior Management
- Provide leadership and support for information security
- Allocate adequate resources for security implementation
- Review and approve information security policies

### 4.2 Information Security Manager
- Develop and maintain the information security program
- Monitor compliance with security policies
- Coordinate incident response activities

### 4.3 All Personnel
- Follow security policies and procedures
- Report security incidents promptly
- Participate in security training programs
- Protect information assets under their control

## 5. Risk Management
${companyName} will:
- Conduct regular risk assessments
- Implement appropriate security controls
- Monitor and review security effectiveness
- Continuously improve security measures

## 6. Incident Management
All security incidents must be:
- Reported immediately to the security team
- Investigated and documented
- Addressed with appropriate corrective actions
- Reviewed for lessons learned

## 7. Compliance
This policy is mandatory for all personnel. Non-compliance may result in disciplinary action, including termination of employment or contract.

## 8. Policy Review
This policy will be reviewed annually or when significant changes occur to ${companyName}'s business or threat environment.

---
**Approved by:** [CEO Name]
**Date:** ${date}
**Next Review:** ${new Date(
          new Date().setFullYear(new Date().getFullYear() + 1)
        ).toLocaleDateString()}`;
      case "ropa":
        return `# Record of Processing Activities (ROPA)

**Organization:** ${companyName}
**Industry:** ${industry}
**Date:** ${date}
**Version:** 1.0

## Processing Activity 1: Customer Management
**Purpose:** Managing customer relationships and service delivery
**Legal Basis:** Contract performance (GDPR Art. 6.1.b)
**Categories of Data Subjects:** Customers, prospects
**Categories of Personal Data:** 
- Identity data (name, contact details)
- Commercial data (purchase history, preferences)
- Technical data (IP addresses, usage data)

**Recipients:** Internal teams, cloud service providers
**Third Country Transfers:** None
**Retention Period:** 7 years after contract termination
**Security Measures:** Encryption, access controls, regular backups

## Processing Activity 2: Employee Management
**Purpose:** HR administration and employment management
**Legal Basis:** Contract performance, legal obligation
**Categories of Data Subjects:** Employees, job applicants
**Categories of Personal Data:**
- Identity and contact data
- Employment data (salary, performance)
- Health data (where applicable)

**Recipients:** HR team, payroll providers, authorities
**Third Country Transfers:** None
**Retention Period:** As per employment law requirements
**Security Measures:** Role-based access, secure storage, audit trails

## Processing Activity 3: Website Analytics
**Purpose:** Website optimization and user experience improvement
**Legal Basis:** Legitimate interest (GDPR Art. 6.1.f)
**Categories of Data Subjects:** Website visitors
**Categories of Personal Data:**
- Technical data (IP addresses, browser info)
- Usage data (page views, session duration)
- Device data (screen resolution, operating system)

**Recipients:** Marketing team, analytics providers
**Third Country Transfers:** USA (adequacy decision/standard contractual clauses)
**Retention Period:** 26 months
**Security Measures:** Pseudonymization, encryption in transit

---
*This ROPA is maintained by ${companyName}'s Data Protection Officer and updated regularly to reflect changes in processing activities.*`;
      case "dpia":
        return `# Data Protection Impact Assessment (DPIA)

**Organization:** ${companyName}
**Processing Operation:** Customer Data Analytics Platform
**Date:** ${date}
**Version:** 1.0

## 1. Description of Processing
${companyName} plans to implement an analytics platform to analyze customer behavior and preferences for service improvement.

**Data Controller:** ${companyName}
**Data Processor:** Third-party analytics provider
**Processing Purpose:** Customer experience optimization, service personalization

## 2. Necessity and Proportionality
**Necessity:** The processing is necessary to improve service quality and customer satisfaction
**Proportionality:** Data collection is limited to what's necessary for the stated purposes
**Alternative Means:** Less intrusive methods were considered but deemed insufficient

## 3. Risks to Rights and Freedoms

### High Risk Factors Present:
- Systematic monitoring of data subjects
- Processing of personal data on a large scale
- Profiling with potential legal effects

### Identified Risks:
1. **Unauthorized Access** (High)
   - Risk to confidentiality
   - Potential identity theft or fraud

2. **Profiling Discrimination** (Medium)
   - Risk of unfair treatment based on profiles
   - Potential impact on service access

3. **Data Subject Rights Interference** (Medium)
   - Complexity may hinder rights exercise
   - Automated decision-making concerns

## 4. Measures to Address Risks

### Technical Measures:
- End-to-end encryption of personal data
- Multi-factor authentication for system access
- Regular security testing and monitoring
- Data minimization and pseudonymization

### Organizational Measures:
- Privacy by design implementation
- Staff training on data protection
- Clear data retention policies
- Regular compliance audits

### Rights Protection:
- Transparent privacy notices
- Easy-to-use rights request mechanisms
- Human review of automated decisions
- Regular consent renewal processes

## 5. Conclusion
With the proposed measures, residual risks are acceptable and the processing can proceed under GDPR compliance.

**DPO Consultation:** Completed
**Stakeholder Review:** Completed
**Next Review:** ${new Date(
          new Date().setMonth(new Date().getMonth() + 6)
        ).toLocaleDateString()}

---
*This DPIA will be reviewed if processing changes significantly or after any data breach.*`;
      default:
        return `# ${DOCUMENT_TEMPLATES[docType]?.title || "Document"}

**Organization:** ${companyName}
**Industry:** ${industry}
**Date:** ${date}

This document has been generated based on your organization's requirements and compliance goals.

*Generated by ArionComply Demo Platform*`;
    }
  }
}
// Enhanced LLM Service with live Claude integration
class LLMService {
  static async queryAssistantLive(sessionId, message, context = {}) {
    // Get conversation history
    const conversationHistory = await this.getConversationHistory(sessionId);
    // Build dynamic system prompt based on context
    const systemPrompt = this.buildDynamicSystemPrompt(
      context,
      conversationHistory
    );
    // Build conversation messages for Claude
    const messages = this.buildConversationMessages(
      conversationHistory,
      message,
      systemPrompt
    );
    try {
      const response = await fetch("https://api.anthropic.com/v1/messages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": CLAUDE_API_KEY,
          "anthropic-version": "2023-06-01",
        },
        body: JSON.stringify({
          model: "claude-3-5-sonnet-20241022",
          max_tokens: 1500,
          temperature: 0.7,
          messages: messages,
        }),
      });
      if (!response.ok) {
        throw new Error(`Claude API error: ${response.status}`);
      }
      const data = await response.json();
      const claudeResponse = data.content[0].text;
      // Store the conversation turn
      await this.storeConversationTurn(
        sessionId,
        message,
        claudeResponse,
        context
      );
      return claudeResponse;
    } catch (error) {
      console.error("Claude API failed:", error);
      return this.getFallbackResponse(message, context);
    }
  }
  static buildDynamicSystemPrompt(context, conversationHistory) {
    const { sessionData, detectedGoal, userProfile } = context;
    let systemPrompt = `You are ArionComply Guide, an expert AI assistant specializing in compliance automation. You help organizations achieve compliance with standards like ISO 27001, GDPR, AI governance, and other regulatory frameworks.

CURRENT CONTEXT:
`;
    // Add session context
    if (sessionData) {
      systemPrompt += `- Session Status: ${sessionData.status || "active"}
- Detected Goal: ${sessionData.detected_goal || "none"}
- Documents Generated: ${sessionData.document_count || 0}
- User Interactions: ${sessionData.interaction_count || 0}
`;
    }
    // Add user profile context
    if (userProfile) {
      systemPrompt += `- User Company: ${userProfile.company || "unknown"}
- User Industry: ${userProfile.industry || "unknown"}
- Company Size: ${userProfile.company_size || "unknown"}
`;
    }
    // Add conversation stage context
    const stage = this.determineConversationStage(conversationHistory, context);
    systemPrompt += `- Conversation Stage: ${stage}

`;
    // Stage-specific instructions
    switch (stage) {
      case "initial":
        systemPrompt += `STAGE: INITIAL ENGAGEMENT
Your goal is to:
1. Welcome the user warmly
2. Understand their compliance needs
3. Detect which compliance goal they're interested in
4. Guide them toward a specific compliance journey

Be conversational, ask clarifying questions, and suggest specific goals when patterns emerge.`;
        break;
      case "goal_detection":
        systemPrompt += `STAGE: GOAL DETECTION
A compliance goal has been detected: ${detectedGoal}
Your goal is to:
1. Confirm the detected goal
2. Explain what this compliance journey involves
3. Outline the benefits and timeline
4. Ask if they want to proceed with an assessment

Be enthusiastic about helping them achieve this specific goal.`;
        break;
      case "intake_process":
        systemPrompt += `STAGE: INTAKE PROCESS
You're gathering information for: ${detectedGoal}
Your goal is to:
1. Ask relevant questions about their organization
2. Gather information needed for document generation
3. Keep questions conversational and explain why you're asking
4. Progress toward document generation

Ask one question at a time and explain how the information will be used.`;
        break;
      case "document_generation":
        systemPrompt += `STAGE: DOCUMENT GENERATION
You're ready to generate compliance documents.
Your goal is to:
1. Confirm you have enough information
2. Explain what documents will be generated
3. Set expectations for the generation process
4. Initiate document creation

Be excited about creating their personalized compliance documents.`;
        break;
      case "document_review":
        systemPrompt += `STAGE: DOCUMENT REVIEW
Documents have been generated and are ready for review.
Your goal is to:
1. Guide them to view their documents
2. Explain what each document contains
3. Answer questions about the documents
4. Suggest next steps (consultation, implementation)

Help them understand the value of what they've received.`;
        break;
      case "lead_capture":
        systemPrompt += `STAGE: LEAD CAPTURE
The user is engaged and has seen the value.
Your goal is to:
1. Suggest scheduling a consultation
2. Offer full platform access
3. Guide them to provide contact information
4. Explain the benefits of connecting with experts

Be helpful in connecting them with human experts for next steps.`;
        break;
      default:
        systemPrompt += `STAGE: GENERAL SUPPORT
Provide helpful information about compliance automation and ArionComply's capabilities.`;
    }
    systemPrompt += `

RESPONSE GUIDELINES:
- Keep responses conversational and helpful
- Use bullet points sparingly, prefer natural conversation
- Ask follow-up questions when appropriate
- Suggest specific actions when relevant
- Stay focused on compliance topics
- Mention ArionComply's capabilities naturally
- Generate quick reply suggestions when helpful

AVAILABLE ACTIONS:
- Generate compliance documents (risk registers, policies, assessments)
- Provide compliance guidance and best practices
- Connect users with experts for consultation
- Explain compliance requirements and timelines

Remember: You're not just answering questions - you're guiding users through their compliance journey.`;
    return systemPrompt;
  }
  static buildConversationMessages(
    conversationHistory,
    currentMessage,
    systemPrompt
  ) {
    const messages = [];
    // Add system message as first user message (Claude doesn't have system role)
    messages.push({
      role: "user",
      content: `SYSTEM INSTRUCTIONS:\n${systemPrompt}\n\nPlease acknowledge these instructions and introduce yourself as ArionComply Guide.`,
    });
    messages.push({
      role: "assistant",
      content:
        "Hello! I'm ArionComply Guide, your AI assistant for compliance automation. I'm here to help you navigate ISO 27001, GDPR, AI governance, and other compliance requirements. What compliance challenge can I help you with today?",
    });
    // Add conversation history (last 10 exchanges to keep context manageable)
    const recentHistory = conversationHistory.slice(-10);
    for (const turn of recentHistory) {
      messages.push({
        role: "user",
        content: turn.user_message,
      });
      messages.push({
        role: "assistant",
        content: turn.assistant_response,
      });
    }
    // Add current message
    messages.push({
      role: "user",
      content: currentMessage,
    });
    return messages;
  }
  static determineConversationStage(conversationHistory, context) {
    const { sessionData, detectedGoal } = context;
    // Check session status first
    if (sessionData?.status === "documents_generated") {
      return "document_review";
    }
    if (sessionData?.contact_captured) {
      return "lead_capture";
    }
    // Check conversation flow
    if (conversationHistory.length === 0) {
      return "initial";
    }
    if (detectedGoal && !sessionData?.detected_goal) {
      return "goal_detection";
    }
    if (sessionData?.detected_goal && !sessionData?.preview_ready) {
      return "intake_process";
    }
    if (sessionData?.detected_goal && sessionData?.interaction_count > 3) {
      return "document_generation";
    }
    return "general_support";
  }
  static async getConversationHistory(sessionId) {
    try {
      const { data: interactions, error } = await supabase
        .from("demo_interactions")
        .select("event_data, created_at")
        .eq("session_id", sessionId)
        .eq("event_type", "conversation_turn")
        .order("created_at", {
          ascending: true,
        })
        .limit(20);
      if (error) throw error;
      return interactions?.map((i) => i.event_data) || [];
    } catch (error) {
      console.error("Failed to get conversation history:", error);
      return [];
    }
  }
  static async storeConversationTurn(
    sessionId,
    userMessage,
    assistantResponse,
    context
  ) {
    try {
      await InteractionLogger.logInteraction(sessionId, "conversation_turn", {
        user_message: userMessage,
        assistant_response: assistantResponse,
        context: {
          detected_goal: context.detectedGoal,
          conversation_stage: this.determineConversationStage([], context),
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      console.error("Failed to store conversation turn:", error);
    }
  }
  static getFallbackResponse(message, context) {
    const { detectedGoal } = context;
    if (detectedGoal) {
      const goalInfo = GOAL_INFO[detectedGoal];
      return `I'd be happy to help you with ${goalInfo.title}! This typically involves ${goalInfo.questions.length} assessment questions and generates ${goalInfo.documents.length} compliance documents. Would you like to get started with the assessment?`;
    }
    return "I'm here to help you with compliance automation. I can assist with ISO 27001 certification, GDPR compliance, AI governance, and more. What specific compliance challenge are you facing?";
  }
  // Legacy method for backward compatibility
  static async queryAssistant(sessionId, message, context = {}) {
    return await this.queryAssistantLive(sessionId, message, context);
  }
}
// RAG (Retrieval-Augmented Generation) Implementation
class RAGService {
  // Generate embeddings using OpenAI (you'll need to add OPENAI_API_KEY to your env)
  static async generateEmbedding(text) {
    const OPENAI_API_KEY = Deno.env.get("OPENAI_API_KEY");
    if (!OPENAI_API_KEY) {
      console.warn("OpenAI API key not found, using mock embedding");
      // Return mock embedding for development
      return new Array(1536).fill(0).map(() => Math.random() * 0.1);
    }
    try {
      const response = await fetch("https://api.openai.com/v1/embeddings", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${OPENAI_API_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model: "text-embedding-ada-002",
          input: text,
        }),
      });
      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
      }
      const data = await response.json();
      return data.data[0].embedding;
    } catch (error) {
      console.error("Embedding generation failed:", error);
      // Return mock embedding as fallback
      return new Array(1536).fill(0).map(() => Math.random() * 0.1);
    }
  }
  // Search knowledge base using vector similarity
  static async searchKnowledgeBase(query, category, limit = 5) {
    try {
      const embedding = await this.generateEmbedding(query);
      const { data, error } = await supabase.rpc("search_knowledge_base", {
        query_text: query,
        query_embedding: embedding,
        category_filter: category,
        limit_results: limit,
        similarity_threshold: 0.7,
      });
      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error("Knowledge base search failed:", error);
      return [];
    }
  }
  // Search FAQs using vector similarity
  static async searchFAQs(query, category, limit = 3) {
    try {
      const embedding = await this.generateEmbedding(query);
      const { data, error } = await supabase.rpc("search_faqs", {
        query_text: query,
        query_embedding: embedding,
        category_filter: category,
        limit_results: limit,
        similarity_threshold: 0.75,
      });
      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error("FAQ search failed:", error);
      return [];
    }
  }
  // Get response template by intent
  static async getResponseTemplate(intent, category) {
    try {
      const { data, error } = await supabase.rpc("get_response_template", {
        intent_name: intent,
        category_name: category,
      });
      if (error) throw error;
      return data?.[0] || null;
    } catch (error) {
      console.error("Template retrieval failed:", error);
      return null;
    }
  }
  // Classify query intent and category
  static classifyQuery(query) {
    const queryLower = query.toLowerCase();
    // Platform-related queries
    if (
      /\b(platform|arioncomply|demo|features|capabilities|what.*do|how.*work)\b/i.test(
        query
      )
    ) {
      return {
        intent: "learn_about_platform",
        category: "platform_features",
        confidence: 0.9,
      };
    }
    // ISO 27001 queries
    if (
      /\b(iso|27001|isms|information security|certification)\b/i.test(query)
    ) {
      return {
        intent: "learn_about_standard",
        category: "iso_27001",
        confidence: 0.9,
      };
    }
    // GDPR queries
    if (
      /\b(gdpr|privacy|data protection|personal data|consent)\b/i.test(query)
    ) {
      return {
        intent: "learn_about_standard",
        category: "gdpr_compliance",
        confidence: 0.9,
      };
    }
    // AI governance queries
    if (
      /\b(ai|artificial intelligence|algorithm|machine learning|ai act)\b/i.test(
        query
      )
    ) {
      return {
        intent: "learn_about_standard",
        category: "ai_governance",
        confidence: 0.8,
      };
    }
    // Implementation queries
    if (
      /\b(how.*implement|implementation|steps|guide|process)\b/i.test(query)
    ) {
      return {
        intent: "implementation_guidance",
        category: "implementation_guides",
        confidence: 0.8,
      };
    }
    // FAQ/Help queries
    if (/\b(help|faq|question|problem|issue|how.*to)\b/i.test(query)) {
      return {
        intent: "get_help",
        category: "faq",
        confidence: 0.7,
      };
    }
    // Default classification
    return {
      intent: "general_inquiry",
      category: "faq",
      confidence: 0.5,
    };
  }
  // Generate controlled response using retrieved content and templates
  static async generateControlledResponse(
    query,
    retrievedContent,
    faqs,
    template,
    sessionId
  ) {
    // If we have a template, use controlled generation
    if (template && retrievedContent.length > 0) {
      return this.generateTemplatedResponse(query, retrievedContent, template);
    }
    // If we have FAQs, use them directly
    if (faqs.length > 0) {
      return this.generateFAQResponse(faqs);
    }
    // If we have knowledge base content, generate from that
    if (retrievedContent.length > 0) {
      return this.generateKnowledgeResponse(retrievedContent, query);
    }
    // Fallback to LLM with guidance
    return this.generateFallbackResponse(query, sessionId);
  }
  // Generate templated response (CAG - Controlled Answer Generation)
  static generateTemplatedResponse(query, content, template) {
    let response = template.template_format;
    // Extract variables from content
    const variables = this.extractTemplateVariables(
      content,
      template.required_variables
    );
    // Replace template variables
    for (const [key, value] of Object.entries(variables)) {
      response = response.replace(new RegExp(`{{${key}}}`, "g"), value);
    }
    // Add follow-up suggestions
    if (
      template.followup_suggestions &&
      template.followup_suggestions.length > 0
    ) {
      const suggestions = template.followup_suggestions.slice(0, 3).join(", ");
      response += `\n\nWould you like to: ${suggestions}?`;
    }
    return response;
  }
  // Generate FAQ-based response
  static generateFAQResponse(faqs) {
    if (faqs.length === 1) {
      return faqs[0].answer;
    }
    // Multiple FAQs - provide structured response
    let response = "Here are some relevant answers:\n\n";
    faqs.forEach((faq, index) => {
      response += `**${faq.question}**\n${faq.answer}\n\n`;
    });
    return (
      response + "Is there a specific aspect you'd like me to elaborate on?"
    );
  }
  // Generate knowledge-based response
  static generateKnowledgeResponse(content, query) {
    if (content.length === 1) {
      const article = content[0];
      return `Based on our knowledge base:\n\n**${
        article.title
      }**\n\n${article.content.substring(0, 800)}${
        article.content.length > 800 ? "..." : ""
      }`;
    }
    // Multiple articles - provide summary
    let response = "Based on our documentation, here's what I found:\n\n";
    content.forEach((article, index) => {
      response += `**${article.title}** (${
        article.category
      })\n${article.content.substring(0, 200)}...\n\n`;
    });
    return (
      response + "Would you like me to dive deeper into any of these topics?"
    );
  }
  // Fallback LLM response with constraints
  static async generateFallbackResponse(query, sessionId) {
    const constrainedPrompt = `You are ArionComply's AI assistant. Answer this query professionally and concisely: "${query}"

Guidelines:
- Focus on compliance automation, ISO 27001, GDPR, and AI governance
- Keep responses under 300 words
- Always offer to help with specific compliance needs
- Mention ArionComply's capabilities when relevant
- End with a helpful follow-up question or suggestion

If you don't know something specific, acknowledge it and offer alternative help.`;
    try {
      const response = await fetch("https://api.anthropic.com/v1/messages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "x-api-key": CLAUDE_API_KEY,
          "anthropic-version": "2023-06-01",
        },
        body: JSON.stringify({
          model: "claude-3-5-sonnet-20241022",
          max_tokens: 400,
          temperature: 0.3,
          messages: [
            {
              role: "user",
              content: constrainedPrompt,
            },
          ],
        }),
      });
      if (!response.ok) {
        throw new Error(`Claude API error: ${response.status}`);
      }
      const data = await response.json();
      return data.content[0].text;
    } catch (error) {
      console.error("Fallback response generation failed:", error);
      return "I'd be happy to help you with compliance questions. Could you tell me more about your specific compliance needs or which standard you're interested in (ISO 27001, GDPR, AI governance)?";
    }
  }
  // Extract variables for template substitution
  static extractTemplateVariables(content, requiredVars) {
    const variables = {};
    if (content.length > 0) {
      const primaryContent = content[0];
      // Standard variable mappings
      variables.description = "an AI-powered compliance automation platform";
      variables.main_benefit =
        "achieve and maintain compliance with standards like ISO 27001, GDPR, and AI governance";
      variables.key_features =
        "automated document generation, risk assessments, and implementation guidance";
      variables.standard_name = this.extractStandardName(primaryContent);
      variables.definition =
        primaryContent.summary || primaryContent.content.substring(0, 200);
      variables.requirements = this.extractRequirements(primaryContent.content);
      variables.process_steps = this.extractProcessSteps(
        primaryContent.content
      );
      variables.timeline_info = this.extractTimelineInfo(
        primaryContent.content
      );
      variables.next_steps = "Would you like to start a compliance assessment?";
      variables.specific_area = this.inferSpecificArea(primaryContent.category);
      variables.additional_info = "";
    }
    return variables;
  }
  // Helper methods for content extraction
  static extractStandardName(content) {
    if (content.category === "iso_27001") return "ISO 27001";
    if (content.category === "gdpr_compliance") return "GDPR";
    if (content.category === "ai_governance") return "AI Governance";
    return content.title || "this standard";
  }
  static extractRequirements(content) {
    const sentences = content.split(/[.!?]+/);
    const requirementSentences = sentences
      .filter((s) => /\b(require|must|shall|need|obligation)\b/i.test(s))
      .slice(0, 3);
    return (
      requirementSentences.join(". ") || "specific regulatory requirements"
    );
  }
  static extractProcessSteps(content) {
    const stepPattern = /\d+\.\s*\*\*([^*]+)\*\*/g;
    const steps = [];
    let match;
    while ((match = stepPattern.exec(content)) !== null && steps.length < 4) {
      steps.push(match[1]);
    }
    return steps.length > 0
      ? steps.join(", ")
      : "assessment, planning, implementation, and ongoing monitoring";
  }
  static extractTimelineInfo(content) {
    const timelinePattern = /(\d+[-–]\d+\s*(months?|weeks?|days?))/i;
    const match = content.match(timelinePattern);
    return match ? `This typically takes ${match[1]}.` : "";
  }
  static inferSpecificArea(category) {
    const areaMap = {
      platform_features: "specific features or capabilities",
      iso_27001: "ISO 27001 implementation",
      gdpr_compliance: "GDPR compliance requirements",
      ai_governance: "AI governance frameworks",
      implementation_guides: "implementation strategies",
    };
    return areaMap[category] || "a specific area";
  }
  // Log query and response for improvement
  static async logQueryResponse(
    sessionId,
    query,
    classification,
    retrievalResults,
    response,
    responseTime
  ) {
    try {
      await supabase.from("user_queries_log").insert([
        {
          session_id: sessionId,
          query_text: query,
          query_intent: classification.intent,
          query_category: classification.category,
          retrieval_results: {
            knowledge_base: retrievalResults.knowledgeBase || [],
            faqs: retrievalResults.faqs || [],
            classification: classification,
          },
          response_generated: response,
          response_time_ms: responseTime,
          retrieval_confidence: classification.confidence,
          created_at: new Date().toISOString(),
        },
      ]);
    } catch (error) {
      console.error("Failed to log query response:", error);
    }
  }
}
// Email service class
class EmailService {
  static async sendVerificationEmail(
    userData,
    verificationToken,
    tempPassword
  ) {
    if (!RESEND_API_KEY) {
      console.warn("RESEND_API_KEY not configured, skipping email");
      return {
        success: false,
        error: "Email service not configured",
      };
    }
    const verificationUrl = `${SITE_URL}/verify.html?token=${verificationToken}`;
    const emailHtml = this.buildVerificationEmailHTML({
      firstName: userData.firstName,
      lastName: userData.lastName,
      company: userData.company,
      email: userData.email,
      verificationUrl,
      tempPassword,
      complianceInterest: userData.complianceInterest,
    });
    const emailText = this.buildVerificationEmailText({
      firstName: userData.firstName,
      verificationUrl,
      tempPassword,
      email: userData.email,
    });
    try {
      const response = await fetch("https://api.resend.com/emails", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${RESEND_API_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          from: FROM_EMAIL,
          to: userData.email,
          subject: "Verify your ArionComply demo access 🔐",
          html: emailHtml,
          text: emailText,
        }),
      });
      if (!response.ok) {
        const error = await response.text();
        throw new Error(`Resend API error: ${response.status} - ${error}`);
      }
      const result = await response.json();
      console.log("📧 Verification email sent successfully:", result.id);
      return {
        success: true,
        messageId: result.id,
      };
    } catch (error) {
      console.error("❌ Failed to send verification email:", error);
      return {
        success: false,
        error: error.message,
      };
    }
  }
  static buildVerificationEmailHTML(data) {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify your ArionComply demo access</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 0; background: #f8fafc; }
        .container { max-width: 600px; margin: 0 auto; background: white; }
        .header { background: linear-gradient(135deg, #059669, #047857); color: white; padding: 2rem; text-align: center; }
        .content { padding: 2rem; }
        .credentials-box { background: #f0fdf4; border: 2px solid #059669; border-radius: 12px; padding: 1.5rem; margin: 1.5rem 0; }
        .credential-item { background: white; padding: 1rem; border-radius: 8px; margin: 0.75rem 0; border: 1px solid #d1fae5; }
        .credential-label { font-weight: 600; color: #374151; margin-bottom: 0.5rem; font-size: 0.9rem; }
        .credential-value { font-family: monospace; font-size: 1.1rem; font-weight: bold; color: #059669; background: #f9fafb; padding: 0.5rem; border-radius: 4px; word-break: break-all; }
        .btn { display: inline-block; background: #059669; color: white; padding: 1rem 2rem; border-radius: 8px; text-decoration: none; font-weight: 600; margin: 1rem 0; }
        .footer { background: #f9fafb; padding: 2rem; text-align: center; color: #6b7280; font-size: 0.875rem; }
        .warning { background: #fef3c7; border: 1px solid #f59e0b; color: #92400e; padding: 1rem; border-radius: 8px; margin: 1rem 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Welcome to ArionComply!</h1>
            <p>Your AI-powered compliance automation platform</p>
        </div>
        
        <div class="content">
            <h2>Hi ${data.firstName}! 👋</h2>
            
            <p>Thank you for registering for ArionComply demo access! We're excited to help ${data.company} achieve compliance excellence with ${data.complianceInterest}.</p>
            
            <p><strong>Click below to verify your email and activate your demo account:</strong></p>
            
            <div style="text-align: center; margin: 2rem 0;">
                <a href="${data.verificationUrl}" class="btn">🔐 Verify Email Address</a>
            </div>
            
            <div class="credentials-box">
                <h3 style="color: #059669; margin-top: 0;">🔑 Your Login Credentials</h3>
                <p>After verifying your email, use these credentials with the <strong>"Try Demo Now"</strong> button on our website:</p>
                
                <div class="credential-item">
                    <div class="credential-label">Email:</div>
                    <div class="credential-value">${data.email}</div>
                </div>
                
                <div class="credential-item">
                    <div class="credential-label">Temporary Password:</div>
                    <div class="credential-value">${data.tempPassword}</div>
                </div>
            </div>
            
            <div class="warning">
                <strong>⚠️ Important:</strong> This verification link expires in 24 hours. Your temporary password will work immediately after email verification.
            </div>
            
            <h3>What's included in your demo:</h3>
            <ul>
                <li>🤖 AI-powered compliance assistant</li>
                <li>📄 Personalized document generation</li>
                <li>🎯 Goal-specific compliance roadmaps</li>
                <li>💡 Expert guidance and best practices</li>
                <li>📞 Optional consultation with our specialists</li>
            </ul>
            
            <p>Questions? Just reply to this email or contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
            
            <p>Best regards,<br>
            <strong>The ArionComply Team</strong></p>
        </div>
        
        <div class="footer">
            <p>This email was sent to ${data.email} because you requested demo access at ArionComply.</p>
            <p>ArionComply • AI-Powered Compliance Automation</p>
            <p>If you didn't request this, you can safely ignore this email.</p>
        </div>
    </div>
</body>
</html>`;
  }
  static buildVerificationEmailText(data) {
    return `
Hi ${data.firstName}!

Welcome to ArionComply - AI-Powered Compliance Automation

Thank you for registering for demo access! Click the link below to verify your email:

${data.verificationUrl}

Your Login Credentials:
Email: ${data.email}
Temporary Password: ${data.tempPassword}

After verifying your email, use these credentials with the "Try Demo Now" button on our website.

What's included in your demo:
• AI-powered compliance assistant
• Personalized document generation  
• Goal-specific compliance roadmaps
• Expert guidance and best practices
• Optional consultation with our specialists

This verification link expires in 24 hours.

Questions? Contact <NAME_EMAIL>

Best regards,
The ArionComply Team

---
This email was sent to ${data.email} because you requested demo access.
If you didn't request this, you can safely ignore this email.
`;
  }
  // Admin notification email
  static async sendAdminNotification(userData, leadScore) {
    if (!RESEND_API_KEY)
      return {
        success: false,
      };
    const adminEmailHtml = `
<h2>🎯 New Demo Registration</h2>
<p><strong>Lead Score: ${leadScore}/100</strong></p>

<h3>Contact Details:</h3>
<ul>
    <li><strong>Name:</strong> ${userData.firstName} ${userData.lastName}</li>
    <li><strong>Email:</strong> ${userData.email}</li>
    <li><strong>Company:</strong> ${userData.company}</li>
    <li><strong>Job Title:</strong> ${userData.jobTitle}</li>
    <li><strong>Company Size:</strong> ${userData.companySize}</li>
    <li><strong>Interest:</strong> ${userData.complianceInterest}</li>
</ul>

<h3>Next Steps:</h3>
<p>Monitor their demo activity and follow up within 24-48 hours if they're highly engaged.</p>
`;
    try {
      await fetch("https://api.resend.com/emails", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${RESEND_API_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          from: FROM_EMAIL,
          to: "<EMAIL>",
          subject: `🎯 New Demo Lead: ${userData.firstName} ${userData.lastName} (${leadScore} pts)`,
          html: adminEmailHtml,
        }),
      });
      return {
        success: true,
      };
    } catch (error) {
      console.error("Failed to send admin notification:", error);
      return {
        success: false,
      };
    }
  }
  // Admin MFA code email
  static async sendAdminMFACode(code, adminEmail, adminName = "") {
    if (!RESEND_API_KEY) {
      console.log(
        `📧 Admin MFA Code for ${adminEmail} (no email service):`,
        code
      );
      return {
        success: false,
      };
    }
    // Get admin name from email if not provided
    const displayName = adminName || adminEmail.split("@")[0];
    const emailHtml = `
<div style="font-family: monospace; text-align: center; padding: 2rem;">
    <h2>🔐 ArionComply Admin Access</h2>
    <p>Hello ${displayName},</p>
    <p>Your admin dashboard verification code is:</p>
    <div style="font-size: 2rem; font-weight: bold; background: #f0fdf4; border: 2px solid #059669; padding: 1rem; margin: 1rem; border-radius: 8px; color: #059669;">
        ${code}
    </div>
    <p>This code expires in 5 minutes.</p>
    <p><small>This code is for admin dashboard access. If you didn't request this, please secure your admin credentials immediately.</small></p>
</div>`;
    try {
      await fetch("https://api.resend.com/emails", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${RESEND_API_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          from: FROM_EMAIL,
          to: adminEmail,
          subject: `🔐 Admin Dashboard Code: ${code}`,
          html: emailHtml,
        }),
      });
      return {
        success: true,
      };
    } catch (error) {
      console.error("Failed to send admin MFA email:", error);
      return {
        success: false,
      };
    }
  }
}
// Helper functions for live Claude integration
async function getUserProfile(sessionId) {
  try {
    // Try to get user profile from demo_users table
    const { data: session1 } = await supabase
      .from("demo_sessions")
      .select("user_id, user_email")
      .eq("id", sessionId)
      .single();
    if (session1?.user_id) {
      const { data: user } = await supabase
        .from("demo_users")
        .select("*")
        .eq("id", session1.user_id)
        .single();
      return user;
    }
    return null;
  } catch (error) {
    console.error("Failed to get user profile:", error);
    return null;
  }
}
async function analyzeClaudeResponse(response, context) {
  // Analyze Claude's response to extract suggested actions and replies
  const analysis = {
    suggestedActions: [],
    suggestedReplies: [],
    intent: "general",
    confidence: 0.5,
  };
  const responseLower = response.toLowerCase();
  // Detect if Claude is suggesting document generation
  if (
    responseLower.includes("generate") &&
    (responseLower.includes("document") || responseLower.includes("assessment"))
  ) {
    analysis.suggestedActions.push("generate_documents");
  }
  // Detect if Claude is suggesting lead capture
  if (
    responseLower.includes("consultation") ||
    responseLower.includes("expert") ||
    responseLower.includes("contact")
  ) {
    analysis.suggestedActions.push("capture_lead");
  }
  // Detect if Claude is asking questions (intake process)
  if (responseLower.includes("?") && context.sessionData?.detected_goal) {
    analysis.suggestedActions.push("continue_intake");
  }
  // Extract potential quick replies from Claude's response
  const questionMatches = response.match(/Would you like to:([^?]*)\?/i);
  if (questionMatches) {
    const options = questionMatches[1]
      .split(/,|or/)
      .map((opt) => opt.trim())
      .filter((opt) => opt.length > 0);
    analysis.suggestedReplies = options.slice(0, 4); // Limit to 4 options
  }
  // Determine intent based on response content
  if (responseLower.includes("iso") || responseLower.includes("27001")) {
    analysis.intent = "iso_certification";
    analysis.confidence = 0.8;
  } else if (
    responseLower.includes("gdpr") ||
    responseLower.includes("privacy")
  ) {
    analysis.intent = "gdpr_compliance";
    analysis.confidence = 0.8;
  } else if (
    responseLower.includes("ai") ||
    responseLower.includes("artificial intelligence")
  ) {
    analysis.intent = "ai_governance";
    analysis.confidence = 0.8;
  }
  return analysis;
}
async function extractIntakeDataFromConversation(
  conversationHistory,
  goalType,
  userResponses = {}
) {
  // Build conversation context for Claude to analyze
  const conversationText = conversationHistory
    .map(
      (turn) =>
        `User: ${turn.user_message}\nAssistant: ${turn.assistant_response}`
    )
    .join("\n\n");
  const goalInfo = GOAL_INFO[goalType];
  const extractionPrompt = `Analyze this conversation and extract structured information for ${
    goalInfo.title
  } compliance assessment.

CONVERSATION:
${conversationText}

REQUIRED INFORMATION:
${goalInfo.questions.map((q, i) => `${i + 1}. ${q}`).join("\n")}

Please extract any answers or relevant information from the conversation and return a JSON object with these fields:
- company_name: string
- industry: string  
- company_size: string (1-10, 11-50, 51-250, 250+)
- system_type: string (cloud-based, on-premises, hybrid)
- key_assets: string
- existing_policies: string
- data_types: string (for GDPR)
- legal_basis: string (for GDPR)
- ai_systems: string (for AI governance)
- risk_level: string
- timeline: string
- additional_context: string

Return ONLY the JSON object with extracted information. If information is not mentioned, use "not specified".`;
  try {
    const response = await fetch("https://api.anthropic.com/v1/messages", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "x-api-key": CLAUDE_API_KEY,
        "anthropic-version": "2023-06-01",
      },
      body: JSON.stringify({
        model: "claude-3-5-sonnet-20241022",
        max_tokens: 1000,
        temperature: 0.3,
        messages: [
          {
            role: "user",
            content: extractionPrompt,
          },
        ],
      }),
    });
    if (!response.ok) {
      throw new Error(`Claude API error: ${response.status}`);
    }
    const data = await response.json();
    const extracted = JSON.parse(data.content[0].text);
    // Merge with any explicitly provided user responses
    return {
      ...extracted,
      ...userResponses,
    };
  } catch (error) {
    console.error("Failed to extract intake data:", error);
    // Fallback: return user responses or default values
    return {
      company_name: userResponses.company_name || "Your Organization",
      industry: userResponses.industry || "Technology",
      company_size: userResponses.company_size || "11-50",
      system_type: userResponses.system_type || "hybrid",
      key_assets: userResponses.key_assets || "customer data, business systems",
      existing_policies: userResponses.existing_policies || "basic",
      timeline: userResponses.timeline || "3-6 months",
      additional_context: "Information extracted from conversation",
      ...userResponses,
    };
  }
}
// Admin authentication handler
async function handleAdminAuthentication(email, password) {
  const adminInfo = getAdminUserInfo(email);
  // For admin users, we'll use a simple password system
  // You can make this more sophisticated with proper hashing
  const adminPassword = generateAdminPassword(email);
  if (password !== adminPassword) {
    console.log("❌ Invalid admin password for:", email);
    return {
      success: true,
      authenticated: false,
      error: "Invalid admin credentials",
    };
  }
  console.log("✅ Admin authenticated successfully:", email);
  try {
    // Create or get admin user record
    const { data: adminUser, error: userError } = await supabase
      .from("demo_users")
      .upsert(
        {
          email: email,
          first_name: adminInfo.name,
          last_name: "",
          full_name: adminInfo.name,
          company: adminInfo.company,
          job_title: adminInfo.jobTitle,
          company_size: "1000+",
          industry: "Technology",
          compliance_needs: "Platform Administration",
          email_verified: true,
          verified_at: new Date().toISOString(),
          agreed_to_terms: true,
          marketing_consent: false,
          registration_source: "admin_direct",
          lead_score: 100,
          user_type: "admin",
          admin_role: adminInfo.role,
          admin_permissions: adminInfo.permissions,
          temporary_password: adminPassword,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          onConflict: "email",
        }
      )
      .select()
      .single();
    if (userError) throw userError;
    // Create admin session
    const adminSession = await SessionManager.createSession({
      user_id: adminUser.id,
      user_email: email,
      user_type: "admin",
      admin_role: adminInfo.role,
      authentication_source: "admin_login",
      metadata: {
        permissions: adminInfo.permissions,
        loginTimestamp: new Date().toISOString(),
      },
    });
    // Update user with session reference
    await supabase
      .from("demo_users")
      .update({
        demo_session_id: adminSession.id,
        last_login: new Date().toISOString(),
      })
      .eq("id", adminUser.id);
    // Log admin access
    await InteractionLogger.logInteraction(adminSession.id, "admin_login", {
      adminEmail: email,
      adminRole: adminInfo.role,
      permissions: adminInfo.permissions,
    });
    return {
      success: true,
      authenticated: true,
      sessionId: adminSession.id,
      userId: adminUser.id,
      userEmail: email,
      userType: "admin",
      adminRole: adminInfo.role,
      permissions: adminInfo.permissions,
      demoUrl: `demo.html?session=${adminSession.id}&admin=true`,
    };
  } catch (error) {
    console.error("❌ Admin authentication failed:", error);
    return {
      success: false,
      error: "Admin authentication failed",
    };
  }
}
async function logAdminActivity(
  adminEmail,
  sessionToken,
  activityType,
  details = {}
) {
  try {
    const { error } = await supabase.from("admin_activity_log").insert({
      admin_email: adminEmail,
      session_token: sessionToken,
      activity_type: activityType,
      activity_details: details,
      created_at: new Date().toISOString(),
    });
    if (error) {
      console.error("Failed to log admin activity:", error);
    } else {
      console.log(
        `📝 Logged admin activity: ${activityType} for ${adminEmail}`
      );
    }
  } catch (error) {
    console.error("Error logging admin activity:", error);
  }
}
function generateQuickReplies(detectedGoal, session1) {
  if (detectedGoal && GOAL_INFO[detectedGoal]) {
    const goalInfo = GOAL_INFO[detectedGoal];
    return [
      `Yes, help me with ${goalInfo.title}`,
      "Tell me more about this",
      "What documents will I get?",
      "How long does this take?",
    ];
  }
  if (session1?.status === "documents_generated") {
    return [
      "View my documents",
      "Schedule consultation",
      "Get pricing information",
      "Contact sales team",
    ];
  }
  return [
    "Get ISO 27001 certified",
    "GDPR compliance help",
    "AI governance guidance",
    "What can this platform do?",
  ];
}
// Helper function to detect goal from message
function detectGoal(message) {
  for (const [goal, pattern] of Object.entries(GOAL_PATTERNS)) {
    if (pattern.test(message)) {
      return goal;
    }
  }
  return null;
}
// Helper function to calculate initial lead score
function calculateInitialLeadScore(data) {
  let score = 30; // Base score
  // Company size scoring
  const sizeScores = {
    "1-10": 10,
    "11-50": 15,
    "51-250": 20,
    "251-1000": 25,
    "1000+": 30,
  };
  score += sizeScores[data.companySize] || 10;
  // Job title scoring
  const jobTitle = data.jobTitle?.toLowerCase() || "";
  if (jobTitle.includes("ciso") || jobTitle.includes("chief")) {
    score += 25;
  } else if (jobTitle.includes("compliance") || jobTitle.includes("security")) {
    score += 20;
  } else if (jobTitle.includes("manager") || jobTitle.includes("director")) {
    score += 15;
  } else {
    score += 5;
  }
  // Compliance interest scoring
  const interest = data.complianceInterest?.toLowerCase() || "";
  if (interest.includes("iso") || interest.includes("multiple")) {
    score += 15;
  } else if (interest.includes("gdpr") || interest.includes("ai")) {
    score += 10;
  } else {
    score += 5;
  }
  return Math.min(score, 100); // Cap at 100
}
// Helper function to check if user is admin
function isAdminUser(email) {
  return ADMIN_USERS.hasOwnProperty(email.toLowerCase());
}
// Generate consistent admin password (you should make this more secure)
function generateAdminPassword(email) {
  // Simple password generation - replace with more secure method
  const emailPrefix = email.split("@")[0];
  return `Arion${emailPrefix}2025!`;
  // Examples:
  // <EMAIL> -> ArionYusuf2025!
  // <EMAIL> -> ArionLibor2025!
}
function getAdminUserInfo(email) {
  return ADMIN_USERS[email.toLowerCase()] || null;
}
// RAG action handlers
const ragActionHandlers = {
  // Main RAG query handler
  "query-rag": async (data, sessionId) => {
    const startTime = Date.now();
    console.log("🔍 Processing RAG query");
    const { message, category } = data;
    try {
      // 1. Classify the query
      const classification = RAGService.classifyQuery(message);
      console.log("Query classified as:", classification);
      // 2. Search relevant content
      const [knowledgeBaseResults, faqResults] = await Promise.all([
        RAGService.searchKnowledgeBase(message, classification.category, 3),
        RAGService.searchFAQs(message, classification.category, 2),
      ]);
      console.log(
        `Found ${knowledgeBaseResults.length} KB articles, ${faqResults.length} FAQs`
      );
      // 3. Get response template
      const template = await RAGService.getResponseTemplate(
        classification.intent,
        classification.category
      );
      console.log("Template found:", !!template);
      // 4. Generate controlled response
      const response = await RAGService.generateControlledResponse(
        message,
        knowledgeBaseResults,
        faqResults,
        template,
        sessionId
      );
      const responseTime = Date.now() - startTime;
      // 5. Log for analytics and improvement
      await RAGService.logQueryResponse(
        sessionId,
        message,
        classification,
        {
          knowledgeBase: knowledgeBaseResults,
          faqs: faqResults,
        },
        response,
        responseTime
      );
      // 6. Generate contextual quick replies
      const quickReplies = generateContextualQuickReplies(
        classification,
        knowledgeBaseResults,
        faqResults
      );
      return {
        success: true,
        response,
        quickReplies,
        metadata: {
          intent: classification.intent,
          category: classification.category,
          confidence: classification.confidence,
          sourcesFound: knowledgeBaseResults.length + faqResults.length,
          responseTime,
        },
      };
    } catch (error) {
      console.error("❌ RAG query failed:", error);
      // Fallback response
      return {
        success: true,
        response:
          "I'd be happy to help you with compliance questions. Could you tell me more about what you're looking for - perhaps information about ISO 27001, GDPR, AI governance, or our platform features?",
        quickReplies: [
          "Tell me about ISO 27001",
          "GDPR compliance help",
          "What does ArionComply do?",
          "AI governance guidance",
        ],
        metadata: {
          error: error.message,
          fallback: true,
        },
      };
    }
  },
  // Search knowledge base directly
  "search-knowledge": async (data, sessionId) => {
    console.log("📚 Processing knowledge search");
    const { query, category, limit = 5 } = data;
    try {
      const results = await RAGService.searchKnowledgeBase(
        query,
        category,
        limit
      );
      return {
        success: true,
        results: results.map((article) => ({
          id: article.id,
          title: article.title,
          content: article.content.substring(0, 300) + "...",
          category: article.category,
          similarity: article.similarity_score,
        })),
      };
    } catch (error) {
      console.error("❌ Knowledge search failed:", error);
      throw error;
    }
  },
  // Get FAQ suggestions
  "get-faq-suggestions": async (data, sessionId) => {
    console.log("❓ Processing FAQ suggestions");
    try {
      const { data: faqs, error } = await supabase
        .from("faq_items")
        .select("id, question, category, helpful_votes")
        .eq("status", "published")
        .order("helpful_votes", {
          ascending: false,
        })
        .limit(10);
      if (error) throw error;
      return {
        success: true,
        faqs: faqs.map((faq) => ({
          id: faq.id,
          question: faq.question,
          category: faq.category,
        })),
      };
    } catch (error) {
      console.error("❌ FAQ suggestions failed:", error);
      throw error;
    }
  },
  // Provide feedback on response quality
  "feedback-response": async (data, sessionId) => {
    console.log("👍 Processing response feedback");
    const { queryId, rating, helpful } = data;
    try {
      // Update query log with feedback
      await supabase
        .from("user_queries_log")
        .update({
          user_satisfaction: rating,
          conversation_resolved: helpful === true,
        })
        .eq("id", queryId);
      return {
        success: true,
        message: "Feedback recorded successfully",
      };
    } catch (error) {
      console.error("❌ Feedback recording failed:", error);
      throw error;
    }
  },
};
// Helper method for generating contextual quick replies
function generateContextualQuickReplies(classification, kbResults, faqResults) {
  const baseReplies = [];
  // Add category-specific replies
  if (classification.category === "platform_features") {
    baseReplies.push("See a demo", "Get pricing", "Start free assessment");
  } else if (classification.category === "iso_27001") {
    baseReplies.push(
      "ISO 27001 implementation guide",
      "Generate ISO documents",
      "Schedule consultation"
    );
  } else if (classification.category === "gdpr_compliance") {
    baseReplies.push(
      "GDPR assessment",
      "Generate GDPR documents",
      "Data protection help"
    );
  } else if (classification.category === "ai_governance") {
    baseReplies.push(
      "AI risk assessment",
      "AI governance framework",
      "EU AI Act compliance"
    );
  }
  // Add content-based suggestions
  if (kbResults.length > 1) {
    baseReplies.push("Tell me more about this");
  }
  if (faqResults.length > 0) {
    baseReplies.push("Other common questions");
  }
  // Default fallbacks
  if (baseReplies.length < 3) {
    baseReplies.push(
      "Talk to an expert",
      "What can ArionComply do?",
      "Help with compliance"
    );
  }
  return baseReplies.slice(0, 4); // Limit to 4 quick replies
}
// Helper function to generate temporary password
function generateTempPassword() {
  const chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let password = "";
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}
// Action handlers (main object combining all handlers)
const actionHandlers = {
  "init-session": async (data) => {
    console.log("🔄 Processing init-session");
    const session1 = await SessionManager.createSession(data);
    await InteractionLogger.logInteraction(
      session1.id,
      "session_started",
      data
    );
    return {
      success: true,
      sessionId: session1.id,
      message: "Session initialized successfully",
    };
  },
  // Enhanced authenticate-user handler with admin support
  "authenticate-user": async (data) => {
    console.log("🔐 Processing user authentication");
    const { email, password } = data;
    const emailLower = email.toLowerCase();
    try {
      // Check if this is an admin user
      if (isAdminUser(emailLower)) {
        console.log("👑 Admin user detected:", emailLower);
        return await handleAdminAuthentication(emailLower, password);
      }
      // Regular user authentication (existing logic)
      const { data: user, error } = await supabase
        .from("demo_users")
        .select("*")
        .eq("email", email)
        .eq("email_verified", true)
        .single();
      if (error || !user) {
        console.log("User not found or not verified:", email);
        return {
          success: true,
          authenticated: false,
          error: "Invalid credentials or email not verified",
        };
      }
      // Check if password matches temporary password
      if (user.temporary_password !== password) {
        console.log("Invalid password for user:", email);
        return {
          success: true,
          authenticated: false,
          error: "Invalid credentials",
        };
      }
      console.log("User authenticated successfully:", email);
      // Create or get existing session
      let sessionId;
      if (user.demo_session_id) {
        sessionId = user.demo_session_id;
      } else {
        const session1 = await SessionManager.createSession({
          user_id: user.id,
          user_email: email,
          authentication_source: "login",
        });
        sessionId = session1.id;
        // Update user with session reference
        await supabase
          .from("demo_users")
          .update({
            demo_session_id: sessionId,
          })
          .eq("id", user.id);
      }
      return {
        success: true,
        authenticated: true,
        sessionId: sessionId,
        userId: user.id,
        userEmail: email,
        userType: "regular",
        demoUrl: `demo.html?session=${sessionId}`,
      };
    } catch (error) {
      console.error("Authentication error:", error);
      return {
        success: false,
        error: "Authentication failed",
      };
    }
  },
  "query-agent": async (data, sessionId) => {
    console.log("🤖 Processing query-agent with live Claude integration");
    const { message } = data;
    try {
      // Get current session data
      const session1 = await SessionManager.getSession(sessionId);
      // Get user profile if available
      const userProfile = await getUserProfile(sessionId);
      // Detect goal from message
      const detectedGoal = detectGoal(message);
      // Build comprehensive context for Claude
      const context = {
        sessionData: session1,
        detectedGoal: detectedGoal || session1.detected_goal,
        userProfile,
        messageCount: session1.interaction_count || 0,
        hasDocuments: session1.document_count > 0,
        isLeadCaptured: session1.contact_captured,
      };
      // Log the incoming interaction
      await InteractionLogger.logInteraction(sessionId, "message_sent", {
        message,
        detectedGoal,
        context: {
          stage: LLMService.determineConversationStage([], context),
          session_status: session1.status,
        },
      });
      // Get live Claude response with full conversation context
      const claudeResponse = await LLMService.queryAssistantLive(
        sessionId,
        message,
        context
      );
      // Analyze Claude's response to extract actions and generate quick replies
      const responseAnalysis = await analyzeClaudeResponse(
        claudeResponse,
        context
      );
      // Generate contextual quick replies
      let quickReplies = responseAnalysis.suggestedReplies || [];
      if (quickReplies.length === 0) {
        quickReplies = generateQuickReplies(detectedGoal, session1);
      }
      // Update session based on response analysis
      const sessionUpdates = {
        last_interaction: new Date().toISOString(),
        interaction_count: (session1.interaction_count || 0) + 1,
      };
      // Update detected goal if new one found
      if (detectedGoal && !session1.detected_goal) {
        sessionUpdates.detected_goal = detectedGoal;
      }
      // Check if Claude suggested document generation
      if (responseAnalysis.suggestedActions?.includes("generate_documents")) {
        sessionUpdates.status = "ready_for_documents";
      }
      // Check if Claude suggested lead capture
      if (responseAnalysis.suggestedActions?.includes("capture_lead")) {
        sessionUpdates.status = "ready_for_lead_capture";
      }
      await SessionManager.updateSession(sessionId, sessionUpdates);
      // Log the response
      await InteractionLogger.logInteraction(sessionId, "assistant_response", {
        response: claudeResponse,
        quickReplies,
        detectedGoal,
        suggestedActions: responseAnalysis.suggestedActions || [],
      });
      return {
        success: true,
        response: claudeResponse,
        quickReplies,
        detectedGoal: detectedGoal || session1.detected_goal,
        conversationStage: LLMService.determineConversationStage([], context),
        suggestedActions: responseAnalysis.suggestedActions || [],
        metadata: {
          responseAnalysis,
          sessionUpdates,
        },
      };
    } catch (error) {
      console.error("❌ Query agent failed:", error);
      // Fallback response
      const fallbackResponse = LLMService.getFallbackResponse(message, {
        detectedGoal: detectGoal(message),
      });
      const fallbackReplies = generateQuickReplies(detectGoal(message), null);
      return {
        success: true,
        response: fallbackResponse,
        quickReplies: fallbackReplies,
        detectedGoal: detectGoal(message),
        error: "Used fallback response due to Claude API error",
      };
    }
  },
  "generate-documents": async (data, sessionId) => {
    console.log("📄 Processing generate-documents");
    const { goalType, intakeData = {} } = data;
    const goalInfo = GOAL_INFO[goalType];
    if (!goalInfo) {
      throw new Error(`Unknown goal type: ${goalType}`);
    }
    const documents = [];
    // Generate documents
    for (const docType of goalInfo.documents) {
      const document = DocumentGenerator.generateDocument(
        docType,
        intakeData,
        goalType
      );
      // Store in database
      const { data: savedDoc, error } = await supabase
        .from("demo_documents")
        .insert([
          {
            session_id: sessionId,
            doc_type: docType,
            title: document.title,
            content: document.content,
            metadata: document.metadata,
            icon: document.icon,
            description: document.description,
            created_at: new Date().toISOString(),
          },
        ])
        .select()
        .single();
      if (error) throw error;
      documents.push(savedDoc);
    }
    // Update session
    await SessionManager.updateSession(sessionId, {
      status: "documents_generated",
      preview_ready: true,
      document_count: documents.length,
      intake_data: intakeData,
    });
    // Log document generation
    await InteractionLogger.logInteraction(sessionId, "documents_generated", {
      goalType,
      documentCount: documents.length,
      documentTypes: goalInfo.documents,
    });
    return {
      success: true,
      documents: documents.map((doc) => ({
        id: doc.id,
        type: doc.doc_type,
        title: doc.title,
        description: doc.description,
        icon: doc.icon,
        createdAt: doc.created_at,
      })),
      message: `Generated ${documents.length} documents for ${goalInfo.title}`,
    };
  },
  "get-documents": async (data, sessionId) => {
    const { data: documents, error } = await supabase
      .from("demo_documents")
      .select("id, doc_type, title, description, icon, created_at, metadata")
      .eq("session_id", sessionId)
      .order("created_at", {
        ascending: false,
      });
    if (error) throw error;
    return {
      success: true,
      documents: documents.map((doc) => ({
        id: doc.id,
        type: doc.doc_type,
        title: doc.title,
        description: doc.description,
        icon: doc.icon,
        createdAt: doc.created_at,
        metadata: doc.metadata,
      })),
    };
  },
  "get-document": async (data, sessionId) => {
    const { documentId } = data;
    const { data: document, error } = await supabase
      .from("demo_documents")
      .select("*")
      .eq("id", documentId)
      .eq("session_id", sessionId)
      .single();
    if (error) throw error;
    // Log document view
    await InteractionLogger.logInteraction(sessionId, "document_viewed", {
      documentId,
    });
    return {
      success: true,
      document: {
        id: document.id,
        type: document.doc_type,
        title: document.title,
        content: document.content,
        description: document.description,
        icon: document.icon,
        metadata: document.metadata,
        createdAt: document.created_at,
      },
    };
  },
  "intelligent-document-generation": async (data, sessionId) => {
    console.log("🧠 Processing intelligent document generation");
    const { conversationContext, userResponses } = data;
    try {
      const session1 = await SessionManager.getSession(sessionId);
      const detectedGoal = session1.detected_goal;
      if (!detectedGoal) {
        throw new Error("No compliance goal detected for document generation");
      }
      // Get conversation history to extract intake information
      const conversationHistory = await LLMService.getConversationHistory(
        sessionId
      );
      // Use Claude to extract structured intake data from conversation
      const intakeData = await extractIntakeDataFromConversation(
        conversationHistory,
        detectedGoal,
        userResponses || {}
      );
      // Generate documents using the extracted intake data
      const response = await actionHandlers["generate-documents"](
        {
          goalType: detectedGoal,
          intakeData,
        },
        sessionId
      );
      // Update session to indicate documents are ready
      await SessionManager.updateSession(sessionId, {
        status: "documents_generated",
        preview_ready: true,
        intake_data: intakeData,
      });
      return {
        success: true,
        documents: response.documents,
        intakeData,
        message: `I've generated ${response.documents.length} personalized compliance documents based on our conversation!`,
        nextAction: "show_documents",
      };
    } catch (error) {
      console.error("❌ Intelligent document generation failed:", error);
      throw error;
    }
  },
  "save-contact": async (data, sessionId) => {
    const { name, email, company, phone, message } = data;
    if (!name || !email) {
      throw new Error("Name and email are required");
    }
    // Save contact
    const { data: contact, error } = await supabase
      .from("demo_contacts")
      .insert([
        {
          session_id: sessionId,
          name,
          email,
          company,
          phone,
          message,
          created_at: new Date().toISOString(),
        },
      ])
      .select()
      .single();
    if (error) throw error;
    // Update session
    await SessionManager.updateSession(sessionId, {
      status: "lead_captured",
      contact_captured: true,
    });
    // Log lead capture
    await InteractionLogger.logInteraction(sessionId, "lead_captured", {
      name,
      email,
      company,
    });
    return {
      success: true,
      contactId: contact.id,
      message: "Contact information saved successfully",
    };
  },
  "log-interaction": async (data, sessionId) => {
    const { eventType, data: eventData } = data;
    await InteractionLogger.logInteraction(sessionId, eventType, eventData);
    return {
      success: true,
      message: "Interaction logged",
    };
  },
  "request-demo-access": async (data) => {
    console.log("📧 Processing demo access request with email");
    // Generate verification token AND temporary password
    const verificationToken = crypto.randomUUID();
    const tempPassword = generateTempPassword();
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
    try {
      // Save verification request with temp password
      const { data: verificationData, error } = await supabase
        .from("demo_verification_requests")
        .insert({
          email: data.workEmail,
          verification_token: verificationToken,
          temporary_password: tempPassword,
          user_data: {
            firstName: data.firstName,
            lastName: data.lastName,
            email: data.workEmail,
            company: data.company,
            jobTitle: data.jobTitle,
            companySize: data.companySize,
            complianceInterest: data.complianceInterest,
            requestSource: data.requestSource || "landing_page",
            userAgent: data.userAgent,
            referrer: data.referrer,
            timestamp: data.timestamp,
            tempPassword: tempPassword,
          },
          expires_at: expiresAt.toISOString(),
        })
        .select()
        .single();
      if (error) throw error;
      // Send verification email
      const emailResult = await EmailService.sendVerificationEmail(
        data,
        verificationToken,
        tempPassword
      );
      if (emailResult.success) {
        console.log("✅ Verification email sent successfully");
        // Send admin notification (optional)
        const leadScore = calculateInitialLeadScore(data);
        await EmailService.sendAdminNotification(data, leadScore);
        return {
          success: true,
          message: "Verification email sent successfully",
          emailSent: true,
          // Remove these in production
          verificationToken: verificationToken,
          tempPassword: tempPassword,
        };
      } else {
        console.warn("⚠️ Email sending failed, but registration saved");
        return {
          success: true,
          message:
            "Registration saved. Please contact support for verification.",
          emailSent: false,
          error: emailResult.error,
        };
      }
    } catch (error) {
      console.error("❌ Demo access request failed:", error);
      throw new Error(
        `Failed to process demo access request: ${error.message}`
      );
    }
  },
  "verify-email": async (data) => {
    console.log("✅ Processing email verification");
    const { token } = data;
    try {
      // Get or create session for verification
      let sessionId = crypto.randomUUID();
      const session1 = await SessionManager.createSession({
        source: "email_verification",
        timestamp: new Date().toISOString(),
      });
      sessionId = session1.id;
      // Complete verification
      const { data: verificationResult, error } = await supabase.rpc(
        "complete_email_verification",
        {
          verification_token: token,
          verified_session_id: sessionId,
        }
      );
      if (error) throw error;
      const result = verificationResult[0];
      if (!result.success) {
        throw new Error(result.error_message || "Verification failed");
      }
      return {
        success: true,
        sessionId: sessionId,
        userId: result.user_id,
        userEmail: result.email,
        redirectUrl: `/demo.html?session=${sessionId}`,
        userData: {
          name: `User`,
          email: result.email,
          company: "Company", // You'd get this from the verification data
        },
      };
    } catch (error) {
      console.error("❌ Email verification failed:", error);
      if (error.message.includes("expired")) {
        return {
          success: false,
          code: "VERIFICATION_FAILED",
          error: "Verification link has expired. Please request a new one.",
        };
      }
      return {
        success: false,
        code: "VERIFICATION_FAILED",
        error: error.message || "Email verification failed",
      };
    }
  },
  "resend-verification": async (data) => {
    console.log("🔄 Processing verification resend");
    const { email } = data;
    try {
      // Check verification status
      const { data: statusResult, error: statusError } = await supabase.rpc(
        "get_verification_status",
        {
          user_email: email,
        }
      );
      if (statusError) throw statusError;
      const status = statusResult[0];
      if (status.has_verified_user) {
        return {
          success: false,
          error: "Email already verified. You can access the demo directly.",
        };
      }
      if (!status.can_resend) {
        return {
          success: false,
          error: "Please wait before requesting another verification email.",
        };
      }
      // Generate new verification token
      const verificationToken = crypto.randomUUID();
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000);
      // Create new verification request
      const { error: insertError } = await supabase
        .from("demo_verification_requests")
        .insert({
          email: email,
          verification_token: verificationToken,
          user_data: {
            email: email,
            requestSource: "resend_verification",
            timestamp: new Date().toISOString(),
          },
          expires_at: expiresAt.toISOString(),
        });
      if (insertError) throw insertError;
      // TODO: Send verification email here
      console.log(`📧 Resend verification email to: ${email}`);
      console.log(
        `🔗 Verification link: ${
          Deno.env.get("SITE_URL") || "http://localhost:3000"
        }/verify.html?token=${verificationToken}`
      );
      return {
        success: true,
        message: "Verification email sent successfully",
      };
    } catch (error) {
      console.error("❌ Resend verification failed:", error);
      throw new Error(`Failed to resend verification: ${error.message}`);
    }
  },
  "submit-pilot-application": async (data) => {
    console.log("🚀 Processing pilot application");
    try {
      // Extract form data
      const {
        first_name,
        last_name,
        email,
        phone_number,
        company_name,
        job_title,
        company_size,
        industry,
        annual_revenue,
        primary_geography,
        compliance_frameworks,
        implementation_timeline,
        current_compliance_state,
        pilot_goals,
        resource_commitment,
        case_study_interest,
        additional_info,
        data_processing_consented,
      } = data;
      // Validation
      if (!first_name || !last_name || !email || !company_name || !job_title) {
        throw new Error("Missing required fields");
      }
      if (
        !compliance_frameworks ||
        !Array.isArray(compliance_frameworks) ||
        compliance_frameworks.length === 0
      ) {
        throw new Error("At least one compliance framework must be selected");
      }
      // Calculate lead score for pilot applications
      let leadScore = 60; // Base score for pilot applications
      // Company size scoring
      const sizeScoring = {
        "11-50": 10,
        "51-200": 15,
        "201-500": 20,
        "501-1000": 25,
        "1000+": 30,
      };
      leadScore += sizeScoring[company_size] || 5;
      // Job title scoring
      if (
        job_title.toLowerCase().includes("ciso") ||
        job_title.toLowerCase().includes("chief")
      ) {
        leadScore += 25;
      } else if (
        job_title.toLowerCase().includes("compliance") ||
        job_title.toLowerCase().includes("security")
      ) {
        leadScore += 20;
      } else if (
        job_title.toLowerCase().includes("manager") ||
        job_title.toLowerCase().includes("director")
      ) {
        leadScore += 15;
      }
      // Timeline urgency scoring
      const timelineScoring = {
        immediate: 20,
        "short-term": 15,
        "medium-term": 10,
        "long-term": 5,
      };
      leadScore += timelineScoring[implementation_timeline] || 0;
      // Determine qualification status
      let qualificationStatus = "lead";
      if (leadScore >= 80) qualificationStatus = "sql";
      else if (leadScore >= 60) qualificationStatus = "mql";
      else if (leadScore >= 40) qualificationStatus = "lead";
      else qualificationStatus = "suspect";
      // Store in demo_contacts table
      const { data: contact, error } = await supabase
        .from("demo_contacts")
        .insert([
          {
            name: `${first_name} ${last_name}`,
            email: email,
            company: company_name,
            phone: phone_number,
            message: `Pilot Application - ${pilot_goals}`,
            session_id: null,
            created_at: new Date().toISOString(),
          },
        ])
        .select()
        .single();
      if (error) throw error;
      // Log the pilot application
      console.log("📝 Pilot application submitted:", {
        name: `${first_name} ${last_name}`,
        email,
        company: company_name,
        frameworks: compliance_frameworks,
        timeline: implementation_timeline,
        leadScore,
        qualification: qualificationStatus,
      });
      return {
        success: true,
        contactId: contact.id,
        leadScore,
        qualification: qualificationStatus,
        message: "Pilot application submitted successfully",
      };
    } catch (error) {
      console.error("❌ Pilot application failed:", error);
      throw error;
    }
  },
  // Admin action handlers
  // Enhanced admin-request-code handler
  // Updated admin-request-code handler in index.ts
  "admin-request-code": async (data) => {
    console.log("🔐 Processing admin MFA request");
    const { email, password } = data;
    try {
      const emailLower = email.toLowerCase();
      // Use existing admin validation functions
      if (!isAdminUser(emailLower)) {
        return {
          success: false,
          error: "Invalid admin credentials",
        };
      }
      // Use existing password generation logic
      const expectedPassword = generateAdminPassword(emailLower);
      if (password !== expectedPassword) {
        return {
          success: false,
          error: "Invalid admin credentials",
        };
      }
      // Get admin info using existing function
      const adminInfo = getAdminUserInfo(emailLower);
      // Generate MFA code
      const code = Math.floor(100000 + Math.random() * 900000).toString();
      const expiresAt = new Date(Date.now() + 5 * 60 * 1000);
      // Store code with admin email reference
      const { data: codeRecord, error } = await supabase
        .from("admin_verification_codes")
        .insert({
          code: code,
          password_hash: password,
          admin_email: emailLower,
          admin_info: adminInfo,
          expires_at: expiresAt.toISOString(),
        })
        .select()
        .single();
      if (error) throw error;
      // Send MFA code to individual admin's email
      const emailResult = await EmailService.sendAdminMFACode(
        emailLower,
        code,
        adminInfo.name
      );
      return {
        success: true,
        message: `Verification code sent to ${emailLower}`,
        emailSent: emailResult.success,
        // For testing only - remove in production:
        _testCode: code,
      };
    } catch (error) {
      console.error("Failed to process admin MFA request:", error);
      return {
        success: false,
        error: "Failed to send verification code",
      };
    }
  },
  "admin-verify-code": async (data) => {
    console.log("🔓 Processing individual admin MFA verification");
    const { code } = data;
    console.log("🔍 Debug - Received code:", code);
    try {
      // Look up valid, unused codes with admin info
      const { data: codeRecords, error } = await supabase
        .from("admin_verification_codes")
        .select("*")
        .eq("code", code)
        .eq("is_used", false)
        .gt("expires_at", new Date().toISOString())
        .order("created_at", {
          ascending: false,
        })
        .limit(1);
      if (error) {
        console.error("Database error looking up code:", error);
        throw error;
      }
      console.log("📋 Found code records:", codeRecords?.length || 0);
      if (!codeRecords || codeRecords.length === 0) {
        console.log("❌ No valid code found in database");
        return {
          success: false,
          error: "Invalid or expired verification code",
        };
      }
      const validCodeRecord = codeRecords[0];
      const adminEmail = validCodeRecord.admin_email;
      // Get admin info from stored data or lookup if missing
      let adminInfo = validCodeRecord.admin_info;
      if (!adminInfo && adminEmail) {
        adminInfo = getAdminUserInfo(adminEmail);
      }
      console.log("✅ Found valid code record for admin:", adminEmail);
      // Mark code as used
      const { error: updateError } = await supabase
        .from("admin_verification_codes")
        .update({
          is_used: true,
          used_at: new Date().toISOString(),
        })
        .eq("id", validCodeRecord.id);
      if (updateError) {
        console.error("Failed to mark code as used:", updateError);
        throw updateError;
      }
      // Generate admin session token
      const adminToken = crypto.randomUUID();
      const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
      // Store admin session with individual admin tracking
      const { data: sessionData, error: sessionError } = await supabase
        .from("admin_sessions")
        .insert({
          token: adminToken,
          admin_email: adminEmail,
          admin_info: adminInfo,
          created_at: new Date().toISOString(),
          expires_at: expiresAt.toISOString(),
          is_active: true,
        })
        .select()
        .single();
      if (sessionError) {
        console.error("Failed to create admin session:", sessionError);
        throw sessionError;
      }
      console.log(
        `✅ Individual admin session created: ${adminToken} for ${adminEmail}`
      );
      // Log individual admin access
      await logAdminActivity(adminEmail, adminToken, "admin_dashboard_login", {
        adminEmail: adminEmail,
        adminRole: adminInfo?.role || "Admin",
        permissions: adminInfo?.permissions || [],
        loginTimestamp: new Date().toISOString(),
      });
      return {
        success: true,
        data: {
          token: adminToken,
          adminInfo: adminInfo,
        },
        message: "Admin dashboard access granted",
      };
    } catch (error) {
      console.error("❌ Individual admin MFA verification failed:", error);
      return {
        success: false,
        error: "Code verification failed",
      };
    }
  },
  "verify-admin-session": async (data) => {
    console.log("🔍 Verifying individual admin session");
    const { token } = data;
    if (!token) {
      return {
        success: false,
        error: "No admin token provided",
      };
    }
    try {
      // Check individual admin session in database
      const { data: session1, error } = await supabase
        .from("admin_sessions")
        .select("*")
        .eq("token", token)
        .eq("is_active", true)
        .gt("expires_at", new Date().toISOString())
        .single();
      if (error || !session1) {
        console.log("❌ Invalid or expired individual admin session");
        return {
          success: false,
          error: "Invalid or expired admin session",
        };
      }
      console.log(
        "✅ Valid individual admin session found for:",
        session1.email
      );
      // Update last activity
      await supabase
        .from("admin_sessions")
        .update({
          updated_at: new Date().toISOString(),
        })
        .eq("token", token);
      return {
        success: true,
        data: {
          adminEmail: session1.email,
          adminInfo: session1.admin_info,
        },
        message: "Admin session verified",
      };
    } catch (error) {
      console.error("❌ Admin session verification failed:", error);
      return {
        success: false,
        error: "Session verification failed",
      };
    }
  },
  "get-leads": async (data) => {
    console.log("📊 Processing get leads request");
    try {
      // Get all demo contacts (leads from contact forms)
      const { data: contacts, error } = await supabase
        .from("demo_contacts")
        .select(
          `
          id,
          name,
          email,
          company,
          phone,
          message,
          created_at,
          session_id,
          demo_sessions(
            detected_goal,
            document_count,
            interaction_count,
            status
          )
        `
        )
        .order("created_at", {
          ascending: false,
        });
      if (error) throw error;
      // Get demo users (from email verification)
      const { data: users, error: usersError } = await supabase
        .from("demo_users")
        .select(
          `
          id,
          first_name,
          last_name,
          email,
          company,
          job_title,
          company_size,
          industry,
          compliance_needs,
          lead_score,
          email_verified,
          marketing_consent,
          created_at
        `
        )
        .order("created_at", {
          ascending: false,
        });
      if (usersError) console.warn("Users query failed:", usersError);
      // Get verification requests (pending email verifications)
      const { data: verifications, error: verifyError } = await supabase
        .from("demo_verification_requests")
        .select(
          `
          id,
          email,
          user_data,
          is_used,
          expires_at,
          created_at
        `
        )
        .order("created_at", {
          ascending: false,
        });
      if (verifyError) console.warn("Verifications query failed:", verifyError);
      // Combine and format leads
      const leads = [];
      // Add contacts as leads
      contacts?.forEach((contact) => {
        const nameParts = contact.name.split(" ");
        const firstName = nameParts[0] || "";
        const lastName = nameParts.slice(1).join(" ") || "";
        leads.push({
          id: contact.id,
          type: "contact_form",
          source: "Contact Form",
          first_name: firstName,
          last_name: lastName,
          full_name: contact.name,
          email: contact.email,
          company_name: contact.company || "",
          phone_number: contact.phone || "",
          job_title: "",
          lead_score: 50,
          lead_qualification_status: "lead",
          compliance_frameworks:
            contact.demo_sessions?.detected_goal || "Unknown",
          company_size: "",
          industry: "",
          decision_timeline: "",
          budget_range: "",
          notes: contact.message || "",
          session_interactions: contact.demo_sessions?.interaction_count || 0,
          documents_generated: contact.demo_sessions?.document_count || 0,
          session_status: contact.demo_sessions?.status || "unknown",
          created_at: contact.created_at,
          last_activity: contact.created_at,
        });
      });
      // Add verified users as leads
      users?.forEach((user) => {
        let qualificationStatus = "suspect";
        if (user.lead_score >= 80) qualificationStatus = "sql";
        else if (user.lead_score >= 60) qualificationStatus = "mql";
        else if (user.lead_score >= 40) qualificationStatus = "lead";
        leads.push({
          id: user.id,
          type: "email_verified",
          source: "Email Verification",
          first_name: user.first_name || "",
          last_name: user.last_name || "",
          full_name: `${user.first_name || ""} ${user.last_name || ""}`.trim(),
          email: user.email,
          company_name: user.company || "",
          phone_number: "",
          job_title: user.job_title || "",
          lead_score: user.lead_score || 0,
          lead_qualification_status: qualificationStatus,
          compliance_frameworks: user.compliance_needs || "Unknown",
          company_size: user.company_size || "",
          industry: user.industry || "",
          decision_timeline: "",
          budget_range: "",
          notes: `Verified user. Marketing consent: ${
            user.marketing_consent ? "Yes" : "No"
          }`,
          session_interactions: 0,
          documents_generated: 0,
          session_status: user.email_verified ? "verified" : "pending",
          created_at: user.created_at,
          last_activity: user.created_at,
        });
      });
      // Add pending verifications as prospects
      verifications?.forEach((verify) => {
        if (!verify.is_used && new Date(verify.expires_at) > new Date()) {
          const userData = verify.user_data || {};
          leads.push({
            id: verify.id,
            type: "pending_verification",
            source: "Pending Verification",
            first_name: userData.firstName || "",
            last_name: userData.lastName || "",
            full_name: `${userData.firstName || ""} ${
              userData.lastName || ""
            }`.trim(),
            email: verify.email,
            company_name: userData.company || "",
            phone_number: "",
            job_title: userData.jobTitle || "",
            lead_score: 25,
            lead_qualification_status: "suspect",
            compliance_frameworks: userData.complianceInterest || "Unknown",
            company_size: userData.companySize || "",
            industry: userData.industry || "",
            decision_timeline: "",
            budget_range: "",
            notes: `Pending email verification. Expires: ${new Date(
              verify.expires_at
            ).toLocaleDateString()}`,
            session_interactions: 0,
            documents_generated: 0,
            session_status: "pending_verification",
            created_at: verify.created_at,
            last_activity: verify.created_at,
          });
        }
      });
      // Sort by creation date (newest first)
      leads.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
      return {
        success: true,
        data: {
          leads,
          summary: {
            total: leads.length,
            sql: leads.filter((l) => l.lead_qualification_status === "sql")
              .length,
            mql: leads.filter((l) => l.lead_qualification_status === "mql")
              .length,
            lead: leads.filter((l) => l.lead_qualification_status === "lead")
              .length,
            suspect: leads.filter(
              (l) => l.lead_qualification_status === "suspect"
            ).length,
            today: leads.filter((l) => {
              const today = new Date().toDateString();
              return new Date(l.created_at).toDateString() === today;
            }).length,
            thisWeek: leads.filter((l) => {
              const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
              return new Date(l.created_at) >= weekAgo;
            }).length,
            verified: leads.filter((l) => l.type === "email_verified").length,
            pending: leads.filter((l) => l.type === "pending_verification")
              .length,
          },
        },
      };
    } catch (error) {
      console.error("❌ Get leads failed:", error);
      throw error;
    }
  },
  "get-admin-dashboard": async (data) => {
    console.log("📈 Processing admin dashboard request");
    try {
      // Get session statistics
      const { data: sessionStats, error: sessionError } = await supabase
        .from("demo_sessions")
        .select(
          "id, status, detected_goal, document_count, interaction_count, created_at"
        )
        .order("created_at", {
          ascending: false,
        })
        .limit(100);
      if (sessionError) throw sessionError;
      // Get interaction statistics
      const { data: interactions, error: interactionError } = await supabase
        .from("demo_interactions")
        .select("event_type, created_at")
        .gte(
          "created_at",
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
        ) // Last 30 days
        .order("created_at", {
          ascending: false,
        });
      if (interactionError) throw interactionError;
      // Get document generation stats
      const { data: documents, error: docError } = await supabase
        .from("demo_documents")
        .select("doc_type, created_at")
        .gte(
          "created_at",
          new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
        )
        .order("created_at", {
          ascending: false,
        });
      if (docError) throw docError;
      // Calculate metrics
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      const metrics = {
        sessions: {
          total: sessionStats?.length || 0,
          today:
            sessionStats?.filter((s) => new Date(s.created_at) >= today)
              .length || 0,
          yesterday:
            sessionStats?.filter((s) => {
              const created = new Date(s.created_at);
              return created >= yesterday && created < today;
            }).length || 0,
          thisWeek:
            sessionStats?.filter((s) => new Date(s.created_at) >= weekAgo)
              .length || 0,
          thisMonth:
            sessionStats?.filter((s) => new Date(s.created_at) >= monthAgo)
              .length || 0,
        },
        goals: {
          iso_certification:
            sessionStats?.filter((s) => s.detected_goal === "iso_certification")
              .length || 0,
          gdpr_audit:
            sessionStats?.filter((s) => s.detected_goal === "gdpr_audit")
              .length || 0,
          ai_risk_management:
            sessionStats?.filter(
              (s) => s.detected_goal === "ai_risk_management"
            ).length || 0,
          soa_generation:
            sessionStats?.filter((s) => s.detected_goal === "soa_generation")
              .length || 0,
          capa_flow:
            sessionStats?.filter((s) => s.detected_goal === "capa_flow")
              .length || 0,
        },
        interactions: {
          total: interactions?.length || 0,
          messages:
            interactions?.filter((i) => i.event_type === "message_sent")
              .length || 0,
          documents:
            interactions?.filter((i) => i.event_type === "documents_generated")
              .length || 0,
          leads:
            interactions?.filter((i) => i.event_type === "lead_captured")
              .length || 0,
        },
        documents: {
          total: documents?.length || 0,
          risk_register:
            documents?.filter((d) => d.doc_type === "risk_register").length ||
            0,
          soa: documents?.filter((d) => d.doc_type === "soa").length || 0,
          policy: documents?.filter((d) => d.doc_type === "policy").length || 0,
          assessment:
            documents?.filter((d) => d.doc_type === "assessment").length || 0,
          ropa: documents?.filter((d) => d.doc_type === "ropa").length || 0,
          dpia: documents?.filter((d) => d.doc_type === "dpia").length || 0,
        },
      };
      return {
        success: true,
        data: {
          metrics,
          recentSessions: sessionStats?.slice(0, 10) || [],
          recentInteractions: interactions?.slice(0, 20) || [],
        },
      };
    } catch (error) {
      console.error("❌ Admin dashboard failed:", error);
      throw error;
    }
  },
  "admin-manage-lead": async (data) => {
    console.log("👤 Processing admin lead management");
    const { leadId, action, updates } = data;
    try {
      switch (action) {
        case "update_qualification":
          // Update lead qualification status
          if (updates.type === "contact_form") {
            await supabase
              .from("demo_contacts")
              .update({
                message: `${updates.qualification} - ${updates.notes || ""}`,
              })
              .eq("id", leadId);
          } else if (updates.type === "email_verified") {
            await supabase
              .from("demo_users")
              .update({
                lead_score: updates.lead_score || 0,
              })
              .eq("id", leadId);
          }
          break;
        case "add_notes":
          // Add notes to lead
          if (updates.type === "contact_form") {
            const { data: existing } = await supabase
              .from("demo_contacts")
              .select("message")
              .eq("id", leadId)
              .single();
            const newMessage = `${existing?.message || ""}\n\nAdmin Note: ${
              updates.notes
            }`;
            await supabase
              .from("demo_contacts")
              .update({
                message: newMessage,
              })
              .eq("id", leadId);
          }
          break;
        case "delete_lead":
          // Delete lead (be careful with this)
          if (updates.type === "contact_form") {
            await supabase.from("demo_contacts").delete().eq("id", leadId);
          } else if (updates.type === "email_verified") {
            await supabase.from("demo_users").delete().eq("id", leadId);
          } else if (updates.type === "pending_verification") {
            await supabase
              .from("demo_verification_requests")
              .delete()
              .eq("id", leadId);
          }
          break;
      }
      return {
        success: true,
        message: `Lead ${action} completed successfully`,
      };
    } catch (error) {
      console.error("❌ Admin lead management failed:", error);
      throw error;
    }
  },
  // USER ACCESS MANAGEMENT ENDPOINTS (add to actionHandlers)
  "get-users": async (data) => {
    console.log("👥 Processing get users request");
    try {
      // Get verified users
      const { data: verifiedUsers, error: verifiedError } = await supabase
        .from("demo_users")
        .select(
          `
        id,
        first_name,
        last_name,
        email,
        company,
        job_title,
        company_size,
        email_verified,
        created_at,
        last_login
      `
        )
        .order("created_at", {
          ascending: false,
        });
      if (verifiedError) throw verifiedError;
      // Get pending verification requests
      const { data: pendingUsers, error: pendingError } = await supabase
        .from("demo_verification_requests")
        .select(
          `
        id,
        email,
        user_data,
        is_used,
        expires_at,
        created_at
      `
        )
        .eq("is_used", false)
        .order("created_at", {
          ascending: false,
        });
      if (pendingError) throw pendingError;
      const users = [];
      // Add verified users
      verifiedUsers?.forEach((user) => {
        users.push({
          id: user.id,
          type: "verified",
          first_name: user.first_name || "",
          last_name: user.last_name || "",
          email: user.email,
          company_name: user.company || "",
          job_title: user.job_title || "",
          company_size: user.company_size || "",
          status: user.email_verified ? "active" : "suspended",
          created_at: user.created_at,
          last_activity: user.last_login || null,
        });
      });
      // Add pending users
      pendingUsers?.forEach((pending) => {
        const userData = pending.user_data || {};
        const isExpired = new Date(pending.expires_at) < new Date();
        users.push({
          id: pending.id,
          type: "pending",
          first_name: userData.firstName || "",
          last_name: userData.lastName || "",
          email: pending.email,
          company_name: userData.company || "",
          job_title: userData.jobTitle || "",
          company_size: userData.companySize || "",
          status: isExpired ? "expired" : "pending",
          created_at: pending.created_at,
          last_activity: null,
        });
      });
      // Sort by creation date (newest first)
      users.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
      return {
        success: true,
        data: {
          users,
          summary: {
            total: users.length,
            active: users.filter((u) => u.status === "active").length,
            pending: users.filter((u) => u.status === "pending").length,
            suspended: users.filter((u) => u.status === "suspended").length,
            expired: users.filter((u) => u.status === "expired").length,
          },
        },
      };
    } catch (error) {
      console.error("❌ Get users failed:", error);
      throw error;
    }
  },
  "admin-manage-user": async (data) => {
    console.log("🔧 Processing admin user management");
    console.log("📨 Received data:", JSON.stringify(data, null, 2));
    const { userId, userAction, userType, adminToken } = data;
    console.log("🔍 Admin token check:", {
      hasToken: !!adminToken,
      tokenLength: adminToken ? adminToken.length : 0,
    });
    if (!adminToken) {
      console.log("❌ Missing admin token");
      return {
        success: false,
        error: "Unauthorized: Missing admin token",
      };
    }
    try {
      // Verify admin session in database
      const { data: session1, error } = await supabase
        .from("admin_sessions")
        .select("*")
        .eq("token", adminToken)
        .eq("is_active", true)
        .gt("expires_at", new Date().toISOString())
        .single();
      if (error || !session1) {
        console.log("❌ Invalid or expired admin session in database");
        return {
          success: false,
          error: "Unauthorized: Invalid or expired admin session",
        };
      }
      console.log("✅ Admin token validated successfully from database");
      // Rest of your existing admin-manage-user logic here...
      switch (userAction) {
        case "activate":
          console.log(`🟢 Activating user ${userId} (type: ${userType})`);
          if (userType === "pending") {
            // For pending verification requests
            const { data: pendingUser } = await supabase
              .from("demo_verification_requests")
              .select("*")
              .eq("id", userId)
              .single();
            if (pendingUser) {
              // Create verified user directly
              const userData = pendingUser.user_data || {};
              const tempPassword =
                userData.tempPassword || generateTempPassword();
              console.log("📝 Creating verified user from pending request");
              const { error: userError } = await supabase
                .from("demo_users")
                .insert({
                  email: pendingUser.email,
                  first_name: userData.firstName || "",
                  last_name: userData.lastName || "",
                  full_name: `${userData.firstName || ""} ${
                    userData.lastName || ""
                  }`.trim(),
                  company: userData.company || "",
                  job_title: userData.jobTitle || "",
                  company_size: userData.companySize || "",
                  industry: userData.industry || "",
                  compliance_needs: userData.complianceInterest || "",
                  email_verified: true,
                  verified_at: new Date().toISOString(),
                  temporary_password: tempPassword,
                  agreed_to_terms: true,
                  marketing_consent: userData.marketingConsent || false,
                  registration_source: "admin_activation",
                  lead_score: calculateInitialLeadScore(userData),
                  user_type: "regular",
                  created_at: pendingUser.created_at,
                  updated_at: new Date().toISOString(),
                });
              if (userError) {
                console.error("❌ Failed to create user:", userError);
                throw userError;
              }
              // Mark verification request as used
              await supabase
                .from("demo_verification_requests")
                .update({
                  is_used: true,
                  used_at: new Date().toISOString(),
                })
                .eq("id", userId);
              console.log("✅ User activated from pending verification");
            } else {
              throw new Error("Pending verification request not found");
            }
          } else {
            // For existing users, just activate them
            console.log("📝 Activating existing user");
            await supabase
              .from("demo_users")
              .update({
                email_verified: true,
                updated_at: new Date().toISOString(),
              })
              .eq("id", userId);
            console.log("✅ Existing user activated");
          }
          break;
        case "suspend":
          console.log(`🟡 Suspending user ${userId}`);
          await supabase
            .from("demo_users")
            .update({
              email_verified: false,
              updated_at: new Date().toISOString(),
            })
            .eq("id", userId);
          console.log("✅ User suspended");
          break;
        case "delete":
          console.log(`🔴 Deleting user ${userId} (type: ${userType})`);
          // Check if it's a pending verification
          if (userType === "pending") {
            await supabase
              .from("demo_verification_requests")
              .delete()
              .eq("id", userId);
          } else {
            await supabase.from("demo_users").delete().eq("id", userId);
          }
          console.log("✅ User deleted");
          break;
        default:
          throw new Error(`Unknown action: ${userAction}`);
      }
      console.log(
        `✅ Admin user management completed: ${userAction} for ${userId}`
      );
      return {
        success: true,
        message: `User ${userAction} completed successfully`,
      };
    } catch (error) {
      console.error("❌ Admin user management failed:", error);
      return {
        success: false,
        error: `Failed to ${userAction} user: ${error.message}`,
      };
    }
  },
  "cleanup-expired-data": async (data) => {
    console.log("🧹 Processing data cleanup");
    try {
      const { daysOld = 30 } = data;
      const cutoffDate = new Date(
        Date.now() - daysOld * 24 * 60 * 60 * 1000
      ).toISOString();
      // Clean up expired verification requests
      const { data: expiredVerifications, error: verifyError } = await supabase
        .from("demo_verification_requests")
        .delete()
        .lt("expires_at", new Date().toISOString())
        .eq("is_used", false);
      // Clean up old sessions (keep those with contacts)
      const { data: oldSessions, error: sessionError } = await supabase
        .from("demo_sessions")
        .delete()
        .lt("created_at", cutoffDate)
        .eq("contact_captured", false);
      return {
        success: true,
        data: {
          expiredVerifications: expiredVerifications?.length || 0,
          oldSessions: oldSessions?.length || 0,
        },
        message: "Cleanup completed successfully",
      };
    } catch (error) {
      console.error("❌ Data cleanup failed:", error);
      throw error;
    }
  },
  "get-user-profile": async (data, sessionId) => {
    try {
      // Query demo_users where demo_session_id matches our sessionId
      const { data: user, error } = await supabase
        .from("demo_users")
        .select("first_name, last_name, email")
        .eq("demo_session_id", sessionId) // Direct match on session ID
        .single();
      if (error || !user) {
        return {
          error: "User not found by session_id",
        };
      }
      return user; // Returns the real first_name from demo_users
    } catch (error) {
      return {
        error: error.message,
      };
    }
  },
  // Include RAG handlers
  ...ragActionHandlers,
};
// Main handler
serve(async (req) => {
  console.log(`🌐 ${req.method} request to ${req.url}`);
  // Handle CORS preflight
  if (req.method === "OPTIONS") {
    console.log("✅ CORS preflight request");
    return new Response("ok", {
      headers: corsHeaders,
    });
  }
  try {
    if (req.method !== "POST") {
      throw new Error("Only POST method allowed");
    }
    const body = await req.json();
    const { action, sessionId, ...data } = body;
    console.log(
      `🎯 Action: ${action}, SessionId: ${sessionId ? "present" : "missing"}`
    );
    if (!action) {
      throw new Error("Action is required");
    }
    // Admin actions don't require sessionId validation
    const adminActions = [
      "admin-request-code",
      "admin-verify-code",
      "verify-admin-session",
      "get-leads",
      "get-admin-dashboard",
      "admin-manage-lead",
      "admin-manage-user",
      "cleanup-expired-data",
    ];
    const noSessionActions = [
      "init-session",
      "request-demo-access",
      "verify-email",
      "resend-verification",
      "submit-pilot-application",
      "authenticate-user",
      "get-users",
    ];
    console.log(
      `🔍 Validation check: admin=${adminActions.includes(
        action
      )}, noSession=${noSessionActions.includes(
        action
      )}, hasSession=${!!sessionId}`
    );
    if (
      !adminActions.includes(action) &&
      !noSessionActions.includes(action) &&
      !sessionId
    ) {
      throw new Error("Session ID is required");
    }
    // Only validate sessionId format if it's provided and not an admin action
    if (
      sessionId &&
      !adminActions.includes(action) &&
      !/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(
        sessionId
      )
    ) {
      throw new Error("Invalid session ID format");
    }
    const handler = actionHandlers[action];
    if (!handler) {
      throw new Error(`Unknown action: ${action}`);
    }
    console.log(`🚀 Executing handler for: ${action}`);
    const result = await handler(data, sessionId);
    console.log(`✅ Handler completed for: ${action}`);
    return new Response(JSON.stringify(result), {
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("❌ Error processing request:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message || "Internal server error",
        timestamp: new Date().toISOString(),
        debug: {
          stack: error.stack?.split("\n").slice(0, 3), // First 3 lines of stack trace
        },
      }),
      {
        status: error.message.includes("required") ? 400 : 500,
        headers: {
          ...corsHeaders,
          "Content-Type": "application/json",
        },
      }
    );
  }
});
