-- =====================================================================================
-- ArionComply Demo Database Migration
-- Version: 1.0
-- Date: 2025-07-14
-- Status: ✅ COMPLETED - Applied to oyvbuuugbfuvupruecvp.supabase.co on 2025-01-14
-- Tables Created: 6 tables, 15+ indexes, 8 functions, 2 materialized views
-- =====================================================================================

-- This migration has been successfully applied to the database
-- To verify, run: SELECT COUNT(*) FROM demo_sessions;
-- Expected result: Tables exist and contain sample data

-- For fresh deployments, copy and paste this entire file into Supabase SQL Editor


-- =====================================================================================
-- ArionComply Demo Database Migration
-- Version: 1.0
-- Date: 2025-07-14
-- Purpose: Complete database schema for ArionComply demo application
-- 
-- Run these in order in your Supabase SQL editor
-- =====================================================================================

-- =====================================================================================
-- MIGRATION 1: Create Core Tables
-- =====================================================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. Demo Sessions Table (Enhanced with security tracking)
CREATE TABLE demo_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Client Information (Security & Analytics)
    user_agent TEXT,
    referrer TEXT,
    ip_address_hash TEXT, -- SHA-256 hash of IP for privacy
    client_fingerprint TEXT, -- Browser fingerprint for session validation
    
    -- Goal Detection & Progress
    goal_detected TEXT,
    goal_confidence DECIMAL(3,2) CHECK (goal_confidence >= 0 AND goal_confidence <= 1),
    intake_completed BOOLEAN NOT NULL DEFAULT FALSE,
    documents_generated BOOLEAN NOT NULL DEFAULT FALSE,
    preview_ready BOOLEAN NOT NULL DEFAULT FALSE,
    lead_captured BOOLEAN NOT NULL DEFAULT FALSE,
    
    -- Session Management
    session_tags TEXT[] DEFAULT '{}',
    metadata JSONB,
    
    -- Security & Compliance
    expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '24 hours'), -- Session expiry
    is_flagged BOOLEAN DEFAULT FALSE, -- For suspicious activity
    last_activity_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Demo Interactions Table
CREATE TABLE demo_interactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES demo_sessions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    message_type TEXT NOT NULL CHECK (message_type IN ('user', 'assistant', 'system')),
    content TEXT NOT NULL,
    intent_detected TEXT,
    goal_context TEXT,
    quick_replies TEXT[] DEFAULT '{}',
    action_triggered TEXT,
    response_time_ms INTEGER,
    tokens_used INTEGER,
    metadata JSONB
);

-- 3. Demo Intake Data Table  
CREATE TABLE demo_intake_data (
    session_id UUID PRIMARY KEY REFERENCES demo_sessions(id) ON DELETE CASCADE,
    goal_type TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    company_size TEXT,
    industry TEXT,
    infrastructure_type TEXT,
    key_assets TEXT[] DEFAULT '{}',
    existing_policies TEXT[] DEFAULT '{}',
    data_processing_types TEXT[] DEFAULT '{}',
    legal_basis TEXT[] DEFAULT '{}',
    data_transfers TEXT,
    ai_systems TEXT[] DEFAULT '{}',
    risk_appetite TEXT,
    custom_responses JSONB,
    completion_percentage DECIMAL(3,2) DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 1)
);

-- 4. Demo Documents Table
CREATE TABLE demo_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES demo_sessions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    document_type TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    sections JSONB,
    metadata JSONB,
    page_count INTEGER DEFAULT 1,
    word_count INTEGER,
    generation_method TEXT NOT NULL,
    generation_time_ms INTEGER,
    preview_count INTEGER DEFAULT 0,
    last_viewed_at TIMESTAMPTZ
);

-- 5. Demo Contacts Table (Enhanced with security and complete contact fields)
CREATE TABLE demo_contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES demo_sessions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    company TEXT,
    job_title TEXT,
    interest_area TEXT,
    message TEXT,
    documents_viewed TEXT[] DEFAULT '{}',
    engagement_score DECIMAL(3,2) CHECK (engagement_score >= 0 AND engagement_score <= 1),
    follow_up_consented BOOLEAN DEFAULT TRUE,
    marketing_consented BOOLEAN DEFAULT FALSE,
    lead_source TEXT,
    utm_campaign TEXT,
    crm_synced BOOLEAN DEFAULT FALSE,
    crm_sync_at TIMESTAMPTZ
);


-- 6. Demo Browsing Logs Table
CREATE TABLE demo_browsing_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES demo_sessions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    event_type TEXT NOT NULL,
    page_url TEXT,
    document_id UUID REFERENCES demo_documents(id) ON DELETE SET NULL,
    time_spent_seconds INTEGER,
    scroll_percentage DECIMAL(3,2) CHECK (scroll_percentage >= 0 AND scroll_percentage <= 1),
    referrer_url TEXT,
    user_agent TEXT,
    viewport_width INTEGER,
    viewport_height INTEGER,
    metadata JSONB
);

-- =====================================================================================
-- MIGRATION 2: Create Performance Indexes
-- =====================================================================================

-- Demo Sessions Indexes
CREATE INDEX idx_sessions_created_at ON demo_sessions(created_at);
CREATE INDEX idx_sessions_goal ON demo_sessions(goal_detected);
CREATE INDEX idx_sessions_status ON demo_sessions(intake_completed, documents_generated, preview_ready);
CREATE INDEX idx_sessions_updated_at ON demo_sessions(updated_at);

-- Demo Interactions Indexes
CREATE INDEX idx_interactions_session ON demo_interactions(session_id, created_at);
CREATE INDEX idx_interactions_type ON demo_interactions(message_type);
CREATE INDEX idx_interactions_intent ON demo_interactions(intent_detected);

-- Demo Intake Data Indexes
CREATE INDEX idx_intake_goal_type ON demo_intake_data(goal_type);
CREATE INDEX idx_intake_completion ON demo_intake_data(completion_percentage);

-- Demo Documents Indexes
CREATE INDEX idx_documents_session ON demo_documents(session_id, document_type);
CREATE INDEX idx_documents_type ON demo_documents(document_type);
CREATE INDEX idx_documents_created ON demo_documents(created_at);

-- Demo Contacts Indexes (Updated for new structure)
CREATE INDEX idx_contacts_email ON demo_contacts(email);
CREATE INDEX idx_contacts_company_name ON demo_contacts(company_name);
CREATE INDEX idx_contacts_full_name ON demo_contacts(first_name, last_name);
CREATE INDEX idx_contacts_phone ON demo_contacts(phone_number) WHERE phone_number IS NOT NULL;
CREATE INDEX idx_contacts_created ON demo_contacts(created_at);
CREATE INDEX idx_contacts_engagement ON demo_contacts(engagement_score);
CREATE INDEX idx_contacts_retention ON demo_contacts(data_retention_until);
CREATE INDEX idx_contacts_consent ON demo_contacts(data_processing_consented, consent_timestamp);
CREATE INDEX idx_contacts_crm_sync ON demo_contacts(crm_synced, crm_sync_at);

-- Security-related indexes
CREATE INDEX idx_sessions_ip_hash ON demo_sessions(ip_address_hash);
CREATE INDEX idx_sessions_expires ON demo_sessions(expires_at);
CREATE INDEX idx_sessions_flagged ON demo_sessions(is_flagged) WHERE is_flagged = TRUE;
CREATE INDEX idx_sessions_activity ON demo_sessions(last_activity_at);

-- Demo Browsing Logs Indexes
CREATE INDEX idx_browsing_session ON demo_browsing_logs(session_id, created_at);
CREATE INDEX idx_browsing_event_type ON demo_browsing_logs(event_type);
CREATE INDEX idx_browsing_document ON demo_browsing_logs(document_id);

-- =====================================================================================
-- MIGRATION 3: Create Utility Functions
-- =====================================================================================

-- Function to update session timestamp automatically
CREATE OR REPLACE FUNCTION update_session_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE demo_sessions 
    SET updated_at = NOW() 
    WHERE id = NEW.session_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update session timestamp on any related table activity
CREATE TRIGGER trigger_update_session_on_interaction
    AFTER INSERT OR UPDATE ON demo_interactions
    FOR EACH ROW EXECUTE FUNCTION update_session_timestamp();

CREATE TRIGGER trigger_update_session_on_intake
    AFTER INSERT OR UPDATE ON demo_intake_data
    FOR EACH ROW EXECUTE FUNCTION update_session_timestamp();

CREATE TRIGGER trigger_update_session_on_document
    AFTER INSERT OR UPDATE ON demo_documents
    FOR EACH ROW EXECUTE FUNCTION update_session_timestamp();

CREATE TRIGGER trigger_update_session_on_browsing
    AFTER INSERT ON demo_browsing_logs
    FOR EACH ROW EXECUTE FUNCTION update_session_timestamp();

-- Function to calculate intake completion percentage
CREATE OR REPLACE FUNCTION calculate_intake_completion(
    p_goal_type TEXT,
    p_company_size TEXT,
    p_industry TEXT,
    p_infrastructure_type TEXT,
    p_key_assets TEXT[],
    p_existing_policies TEXT[],
    p_custom_responses JSONB
)
RETURNS DECIMAL(3,2) AS $$
DECLARE
    completion_score DECIMAL(3,2) := 0;
    total_fields INTEGER := 0;
    filled_fields INTEGER := 0;
BEGIN
    -- Base required fields for all goal types
    total_fields := 4; -- company_size, industry, infrastructure_type, key_assets
    
    IF p_company_size IS NOT NULL THEN filled_fields := filled_fields + 1; END IF;
    IF p_industry IS NOT NULL THEN filled_fields := filled_fields + 1; END IF;
    IF p_infrastructure_type IS NOT NULL THEN filled_fields := filled_fields + 1; END IF;
    IF p_key_assets IS NOT NULL AND array_length(p_key_assets, 1) > 0 THEN filled_fields := filled_fields + 1; END IF;
    
    -- Additional fields based on goal type
    CASE p_goal_type
        WHEN 'iso_certification' THEN
            total_fields := total_fields + 1; -- existing_policies
            IF p_existing_policies IS NOT NULL AND array_length(p_existing_policies, 1) > 0 THEN
                filled_fields := filled_fields + 1;
            END IF;
            
        WHEN 'gdpr_audit' THEN
            total_fields := total_fields + 2; -- data_processing_types, legal_basis (from custom_responses)
            IF p_custom_responses ? 'data_processing_types' THEN filled_fields := filled_fields + 1; END IF;
            IF p_custom_responses ? 'legal_basis' THEN filled_fields := filled_fields + 1; END IF;
            
        WHEN 'ai_risk_management' THEN
            total_fields := total_fields + 1; -- ai_systems (from custom_responses)
            IF p_custom_responses ? 'ai_systems' THEN filled_fields := filled_fields + 1; END IF;
    END CASE;
    
    -- Calculate percentage
    IF total_fields > 0 THEN
        completion_score := ROUND((filled_fields::DECIMAL / total_fields::DECIMAL), 2);
    END IF;
    
    RETURN completion_score;
END;
$$ LANGUAGE plpgsql;

-- Function to generate engagement score for contacts (Updated for new schema)
CREATE OR REPLACE FUNCTION calculate_engagement_score(
    p_session_id UUID
)
RETURNS DECIMAL(3,2) AS $
DECLARE
    score DECIMAL(3,2) := 0;
    interaction_count INTEGER;
    document_count INTEGER;
    avg_time_spent DECIMAL;
    intake_completion DECIMAL;
BEGIN
    -- Count interactions (each message adds to engagement)
    SELECT COUNT(*) INTO interaction_count
    FROM demo_interactions 
    WHERE session_id = p_session_id AND message_type = 'user';
    
    -- Count documents viewed
    SELECT COUNT(DISTINCT document_id) INTO document_count
    FROM demo_browsing_logs
    WHERE session_id = p_session_id AND event_type = 'document_view';
    
    -- Average time spent on documents
    SELECT AVG(time_spent_seconds) INTO avg_time_spent
    FROM demo_browsing_logs
    WHERE session_id = p_session_id AND time_spent_seconds IS NOT NULL;
    
    -- Get intake completion percentage
    SELECT completion_percentage INTO intake_completion
    FROM demo_intake_data
    WHERE session_id = p_session_id;
    
    -- Calculate score (0-1 scale) - Updated weights
    score := LEAST(1.0, 
        (interaction_count * 0.05) +  -- Each interaction worth 0.05
        (document_count * 0.25) +     -- Each document view worth 0.25  
        COALESCE(LEAST(0.3, avg_time_spent / 300.0), 0) + -- Time spent (max 5 min = 0.3)
        COALESCE(intake_completion * 0.4, 0) -- Intake completion worth up to 0.4
    );
    
    RETURN ROUND(score, 2);
END;
$ LANGUAGE plpgsql;

-- Security function to hash IP addresses
CREATE OR REPLACE FUNCTION hash_ip_address(ip_address TEXT)
RETURNS TEXT AS $
BEGIN
    -- Use SHA-256 hash with a salt for privacy
    RETURN encode(digest(ip_address || 'arion-demo-salt-2025', 'sha256'), 'hex');
END;
$ LANGUAGE plpgsql;

-- Security function to validate session
CREATE OR REPLACE FUNCTION is_session_valid(p_session_id UUID)
RETURNS BOOLEAN AS $
DECLARE
    session_record RECORD;
BEGIN
    SELECT expires_at, is_flagged INTO session_record
    FROM demo_sessions
    WHERE id = p_session_id;
    
    -- Return false if session doesn't exist, is expired, or is flagged
    IF session_record IS NULL 
       OR session_record.expires_at < NOW() 
       OR session_record.is_flagged = TRUE THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$ LANGUAGE plpgsql;

-- Data retention compliance function (GDPR)
CREATE OR REPLACE FUNCTION cleanup_expired_data()
RETURNS INTEGER AS $
DECLARE
    deleted_sessions INTEGER := 0;
    deleted_contacts INTEGER := 0;
    anonymized_logs INTEGER := 0;
BEGIN
    -- Delete expired contact data (respecting retention periods)
    DELETE FROM demo_contacts 
    WHERE data_retention_until < CURRENT_DATE;
    GET DIAGNOSTICS deleted_contacts = ROW_COUNT;
    
    -- Delete old sessions without contact capture (30 days)
    DELETE FROM demo_sessions 
    WHERE created_at < NOW() - INTERVAL '30 days' 
      AND lead_captured = FALSE;
    GET DIAGNOSTICS deleted_sessions = ROW_COUNT;
    
    -- Anonymize old browsing logs (remove PII after 6 months)
    UPDATE demo_browsing_logs 
    SET user_agent = 'anonymized',
        metadata = NULL
    WHERE created_at < NOW() - INTERVAL '6 months'
      AND user_agent != 'anonymized';
    GET DIAGNOSTICS anonymized_logs = ROW_COUNT;
    
    -- Log cleanup activity
    INSERT INTO demo_browsing_logs (session_id, event_type, metadata, created_at)
    VALUES (
        '00000000-0000-0000-0000-000000000000',
        'data_cleanup',
        jsonb_build_object(
            'deleted_sessions', deleted_sessions,
            'deleted_contacts', deleted_contacts, 
            'anonymized_logs', anonymized_logs
        ),
        NOW()
    );
    
    RETURN deleted_sessions + deleted_contacts;
END;
$ LANGUAGE plpgsql;

-- =====================================================================================
-- MIGRATION 4: Enhanced Row Level Security (RLS) Policies
-- =====================================================================================

-- Enable RLS on all tables
ALTER TABLE demo_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE demo_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE demo_intake_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE demo_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE demo_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE demo_browsing_logs ENABLE ROW LEVEL SECURITY;

-- Enhanced policies for production security

-- Demo Sessions: Session-based access with validation
CREATE POLICY "Users can access valid sessions" ON demo_sessions
    FOR SELECT USING (
        id::text = current_setting('request.headers', true)::json->>'x-session-id'
        AND is_session_valid(id)
    );

CREATE POLICY "Users can update their own sessions" ON demo_sessions
    FOR UPDATE USING (
        id::text = current_setting('request.headers', true)::json->>'x-session-id'
        AND is_session_valid(id)
    );

CREATE POLICY "Anyone can create sessions" ON demo_sessions
    FOR INSERT WITH CHECK (true);

-- Demo Interactions: Users can only access interactions from their valid sessions
CREATE POLICY "Users can access interactions from their sessions" ON demo_interactions
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM demo_sessions 
            WHERE id = session_id 
            AND id::text = current_setting('request.headers', true)::json->>'x-session-id'
            AND is_session_valid(id)
        )
    );

-- Demo Intake Data: Users can only access their own intake data
CREATE POLICY "Users can access their own intake data" ON demo_intake_data
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM demo_sessions 
            WHERE id = session_id 
            AND id::text = current_setting('request.headers', true)::json->>'x-session-id'
            AND is_session_valid(id)
        )
    );

-- Demo Documents: Users can only access documents from their sessions
CREATE POLICY "Users can access documents from their sessions" ON demo_documents
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM demo_sessions 
            WHERE id = session_id 
            AND id::text = current_setting('request.headers', true)::json->>'x-session-id'
            AND is_session_valid(id)
        )
    );

-- Demo Contacts: Users can only create/access their own contact submissions
CREATE POLICY "Users can manage their own contact data" ON demo_contacts
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM demo_sessions 
            WHERE id = session_id 
            AND id::text = current_setting('request.headers', true)::json->>'x-session-id'
            AND is_session_valid(id)
        )
    );

-- Demo Browsing Logs: Users can only access their own browsing logs
CREATE POLICY "Users can access their own browsing logs" ON demo_browsing_logs
    FOR ALL USING (
        session_id::text = current_setting('request.headers', true)::json->>'x-session-id'
        OR EXISTS (
            SELECT 1 FROM demo_sessions 
            WHERE id = session_id 
            AND id::text = current_setting('request.headers', true)::json->>'x-session-id'
            AND is_session_valid(id)
        )
    );

-- Admin access policy (for analytics and support)
CREATE ROLE demo_admin;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO demo_admin;

-- Create policy for admin analytics access
CREATE POLICY "Admin can access aggregated data" ON demo_sessions
    FOR SELECT TO demo_admin USING (true);

CREATE POLICY "Admin can access aggregated interactions" ON demo_interactions  
    FOR SELECT TO demo_admin USING (true);

CREATE POLICY "Admin can access aggregated contacts" ON demo_contacts
    FOR SELECT TO demo_admin USING (true);

-- =====================================================================================
-- MIGRATION 5: Analytics Views and Functions
-- =====================================================================================

-- Materialized view for session analytics
CREATE MATERIALIZED VIEW session_analytics AS
SELECT 
    DATE(created_at) as date,
    goal_detected,
    COUNT(*) as total_sessions,
    COUNT(*) FILTER (WHERE intake_completed) as completed_intake,
    COUNT(*) FILTER (WHERE documents_generated) as generated_docs,
    COUNT(*) FILTER (WHERE preview_ready) as preview_ready,
    COUNT(*) FILTER (WHERE lead_captured) as captured_leads,
    AVG(EXTRACT(EPOCH FROM (updated_at - created_at))) as avg_session_duration_seconds,
    COUNT(DISTINCT CASE WHEN goal_detected IS NOT NULL THEN id END) as goal_detected_count
FROM demo_sessions
WHERE created_at >= CURRENT_DATE - INTERVAL '90 days' -- Last 90 days
GROUP BY DATE(created_at), goal_detected
ORDER BY date DESC, goal_detected;

-- Create index on the materialized view
CREATE INDEX idx_session_analytics_date ON session_analytics(date);
CREATE INDEX idx_session_analytics_goal ON session_analytics(goal_detected);

-- Document analytics view
CREATE MATERIALIZED VIEW document_analytics AS
SELECT 
    document_type,
    COUNT(*) as total_generated,
    AVG(preview_count) as avg_preview_count,
    AVG(word_count) as avg_word_count,
    AVG(generation_time_ms) as avg_generation_time_ms,
    COUNT(*) FILTER (WHERE preview_count > 0) as documents_viewed,
    DATE(created_at) as date
FROM demo_documents
WHERE created_at >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY document_type, DATE(created_at)
ORDER BY date DESC, document_type;

-- Function to refresh analytics (call this daily via cron or scheduler)
CREATE OR REPLACE FUNCTION refresh_analytics()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW session_analytics;
    REFRESH MATERIALIZED VIEW document_analytics;
END;
$$ LANGUAGE plpgsql;

-- =====================================================================================
-- MIGRATION 6: Data Cleanup and Maintenance Functions
-- =====================================================================================

-- Function to clean up old sessions (run daily)
CREATE OR REPLACE FUNCTION cleanup_old_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete sessions older than 30 days with no lead capture
    DELETE FROM demo_sessions 
    WHERE created_at < NOW() - INTERVAL '30 days' 
      AND lead_captured = FALSE;
      
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Archive old sessions with leads (mark as archived instead of deleting)
    UPDATE demo_sessions 
    SET session_tags = array_append(session_tags, 'archived')
    WHERE created_at < NOW() - INTERVAL '2 years'
      AND lead_captured = TRUE
      AND NOT ('archived' = ANY(session_tags));
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to update intake completion percentages
CREATE OR REPLACE FUNCTION update_intake_completion_percentages()
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER;
    rec RECORD;
BEGIN
    updated_count := 0;
    
    FOR rec IN 
        SELECT session_id, goal_type, company_size, industry, infrastructure_type, 
               key_assets, existing_policies, custom_responses
        FROM demo_intake_data
        WHERE completed_at IS NULL
    LOOP
        UPDATE demo_intake_data
        SET completion_percentage = calculate_intake_completion(
            rec.goal_type, rec.company_size, rec.industry, rec.infrastructure_type,
            rec.key_assets, rec.existing_policies, rec.custom_responses
        )
        WHERE session_id = rec.session_id;
        
        updated_count := updated_count + 1;
    END LOOP;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================================================
-- MIGRATION 7: Sample Data (Updated for new schema)
-- =====================================================================================

-- Insert sample sessions with enhanced security fields
INSERT INTO demo_sessions (
    id, 
    goal_detected, 
    goal_confidence, 
    session_tags,
    ip_address_hash,
    client_fingerprint,
    expires_at
) VALUES
    (
        '00000000-0000-0000-0000-000000000001', 
        'iso_certification', 
        0.95, 
        ARRAY['sample', 'test'],
        hash_ip_address('*************'),
        'sample_fingerprint_1',
        NOW() + INTERVAL '24 hours'
    ),
    (
        '00000000-0000-0000-0000-000000000002', 
        'gdpr_audit', 
        0.88, 
        ARRAY['sample', 'test'],
        hash_ip_address('*************'),
        'sample_fingerprint_2',
        NOW() + INTERVAL '24 hours'
    ),
    (
        '00000000-0000-0000-0000-00**********', 
        'ai_risk_management', 
        0.92, 
        ARRAY['sample', 'test'],
        hash_ip_address('*************'),
        'sample_fingerprint_3',
        NOW() + INTERVAL '24 hours'
    );

-- Insert sample intake data
INSERT INTO demo_intake_data (session_id, goal_type, company_size, industry, completion_percentage) VALUES
    ('00000000-0000-0000-0000-000000000001', 'iso_certification', '11-50', 'Technology', 0.8),
    ('00000000-0000-0000-0000-000000000002', 'gdpr_audit', '51-200', 'Healthcare', 0.6),
    ('00000000-0000-0000-0000-00**********', 'ai_risk_management', '201-500', 'Financial Services', 0.9);

-- Insert sample contact data (new structure)
INSERT INTO demo_contacts (
    session_id,
    first_name,
    last_name,
    email,
    company_name,
    phone_number,
    job_title,
    interest_area,
    data_processing_consented,
    marketing_consented,
    consent_ip_address
) VALUES
    (
        '00000000-0000-0000-0000-000000000001',
        'John',
        'Smith',
        '<EMAIL>',
        'TechCorp Solutions',
        '+**********',
        'CISO',
        'ISO 27001 Certification',
        true,
        true,
        '*************'::inet
    ),
    (
        '00000000-0000-0000-0000-000000000002',
        'Sarah',
        'Johnson',
        '<EMAIL>',
        'HealthSystem Inc',
        '+**********',
        'Privacy Officer',
        'GDPR Compliance',
        true,
        false,
        '*************'::inet
    );

-- Insert sample interactions
INSERT INTO demo_interactions (session_id, message_type, content, intent_detected) VALUES
    ('00000000-0000-0000-0000-000000000001', 'user', 'I need help with ISO 27001 certification', 'iso_certification'),
    ('00000000-0000-0000-0000-000000000001', 'assistant', 'I can help you get ISO 27001 certified. Let me ask you some questions.', null),
    ('00000000-0000-0000-0000-000000000002', 'user', 'We need GDPR compliance assistance', 'gdpr_audit'),
    ('00000000-0000-0000-0000-000000000002', 'assistant', 'Great! I can help with GDPR compliance. What type of personal data do you process?', null);

-- Insert sample documents
INSERT INTO demo_documents (
    session_id,
    document_type,
    title,
    content,
    generation_method,
    word_count,
    page_count
) VALUES
    (
        '00000000-0000-0000-0000-000000000001',
        'risk_register',
        'Risk Register - TechCorp Solutions',
        'SAMPLE RISK REGISTER\n\n**Risk ID:** R001\n**Risk Description:** Unauthorized access to customer data\n**Impact:** High\n**Likelihood:** Medium\n**Risk Level:** High\n\n**Mitigation Controls:**\n- Multi-factor authentication\n- Access controls\n- Regular access reviews',
        'template',
        150,
        2
    ),
    (
        '00000000-0000-0000-0000-000000000002',
        'ropa',
        'Record of Processing Activities - HealthSystem',
        'SAMPLE ROPA\n\n**Processing Activity:** Patient Management System\n**Purpose:** Healthcare service delivery\n**Data Subjects:** Patients\n**Categories of Data:** Health data, contact information\n**Legal Basis:** Consent, Vital interests\n**Retention Period:** 10 years\n**International Transfers:** None',
        'template',
        200,
        3
    );

-- =====================================================================================
-- MIGRATION COMPLETE
-- =====================================================================================

-- Verify the migration
SELECT 
    'demo_sessions' as table_name, 
    COUNT(*) as row_count 
FROM demo_sessions
UNION ALL
SELECT 'demo_interactions', COUNT(*) FROM demo_interactions
UNION ALL  
SELECT 'demo_intake_data', COUNT(*) FROM demo_intake_data
UNION ALL
SELECT 'demo_documents', COUNT(*) FROM demo_documents
UNION ALL
SELECT 'demo_contacts', COUNT(*) FROM demo_contacts
UNION ALL
SELECT 'demo_browsing_logs', COUNT(*) FROM demo_browsing_logs;

-- Show available functions
SELECT routine_name, routine_type 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND routine_name LIKE '%demo%' OR routine_name IN ('calculate_intake_completion', 'calculate_engagement_score', 'cleanup_old_sessions', 'refresh_analytics');

COMMENT ON DATABASE postgres IS 'ArionComply Demo Database - Migration v1.0 completed successfully';),
    company_name TEXT NOT NULL CHECK (length(trim(company_name)) >= 1),
    
    -- Optional Contact Information
    phone_number TEXT CHECK (phone_number IS NULL OR phone_number ~* '^\+?[1-9]\d{1,14}

-- 6. Demo Browsing Logs Table
CREATE TABLE demo_browsing_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES demo_sessions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    event_type TEXT NOT NULL,
    page_url TEXT,
    document_id UUID REFERENCES demo_documents(id) ON DELETE SET NULL,
    time_spent_seconds INTEGER,
    scroll_percentage DECIMAL(3,2) CHECK (scroll_percentage >= 0 AND scroll_percentage <= 1),
    referrer_url TEXT,
    user_agent TEXT,
    viewport_width INTEGER,
    viewport_height INTEGER,
    metadata JSONB
);

-- =====================================================================================
-- MIGRATION 2: Create Performance Indexes
-- =====================================================================================

-- Demo Sessions Indexes
CREATE INDEX idx_sessions_created_at ON demo_sessions(created_at);
CREATE INDEX idx_sessions_goal ON demo_sessions(goal_detected);
CREATE INDEX idx_sessions_status ON demo_sessions(intake_completed, documents_generated, preview_ready);
CREATE INDEX idx_sessions_updated_at ON demo_sessions(updated_at);

-- Demo Interactions Indexes
CREATE INDEX idx_interactions_session ON demo_interactions(session_id, created_at);
CREATE INDEX idx_interactions_type ON demo_interactions(message_type);
CREATE INDEX idx_interactions_intent ON demo_interactions(intent_detected);

-- Demo Intake Data Indexes
CREATE INDEX idx_intake_goal_type ON demo_intake_data(goal_type);
CREATE INDEX idx_intake_completion ON demo_intake_data(completion_percentage);

-- Demo Documents Indexes
CREATE INDEX idx_documents_session ON demo_documents(session_id, document_type);
CREATE INDEX idx_documents_type ON demo_documents(document_type);
CREATE INDEX idx_documents_created ON demo_documents(created_at);

-- Demo Contacts Indexes
CREATE INDEX idx_contacts_email ON demo_contacts(email);
CREATE INDEX idx_contacts_company ON demo_contacts(company);
CREATE INDEX idx_contacts_created ON demo_contacts(created_at);
CREATE INDEX idx_contacts_engagement ON demo_contacts(engagement_score);

-- Demo Browsing Logs Indexes
CREATE INDEX idx_browsing_session ON demo_browsing_logs(session_id, created_at);
CREATE INDEX idx_browsing_event_type ON demo_browsing_logs(event_type);
CREATE INDEX idx_browsing_document ON demo_browsing_logs(document_id);

-- =====================================================================================
-- MIGRATION 3: Create Utility Functions
-- =====================================================================================

-- Function to update session timestamp automatically
CREATE OR REPLACE FUNCTION update_session_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE demo_sessions 
    SET updated_at = NOW() 
    WHERE id = NEW.session_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update session timestamp on any related table activity
CREATE TRIGGER trigger_update_session_on_interaction
    AFTER INSERT OR UPDATE ON demo_interactions
    FOR EACH ROW EXECUTE FUNCTION update_session_timestamp();

CREATE TRIGGER trigger_update_session_on_intake
    AFTER INSERT OR UPDATE ON demo_intake_data
    FOR EACH ROW EXECUTE FUNCTION update_session_timestamp();

CREATE TRIGGER trigger_update_session_on_document
    AFTER INSERT OR UPDATE ON demo_documents
    FOR EACH ROW EXECUTE FUNCTION update_session_timestamp();

CREATE TRIGGER trigger_update_session_on_browsing
    AFTER INSERT ON demo_browsing_logs
    FOR EACH ROW EXECUTE FUNCTION update_session_timestamp();

-- Function to calculate intake completion percentage
CREATE OR REPLACE FUNCTION calculate_intake_completion(
    p_goal_type TEXT,
    p_company_size TEXT,
    p_industry TEXT,
    p_infrastructure_type TEXT,
    p_key_assets TEXT[],
    p_existing_policies TEXT[],
    p_custom_responses JSONB
)
RETURNS DECIMAL(3,2) AS $$
DECLARE
    completion_score DECIMAL(3,2) := 0;
    total_fields INTEGER := 0;
    filled_fields INTEGER := 0;
BEGIN
    -- Base required fields for all goal types
    total_fields := 4; -- company_size, industry, infrastructure_type, key_assets
    
    IF p_company_size IS NOT NULL THEN filled_fields := filled_fields + 1; END IF;
    IF p_industry IS NOT NULL THEN filled_fields := filled_fields + 1; END IF;
    IF p_infrastructure_type IS NOT NULL THEN filled_fields := filled_fields + 1; END IF;
    IF p_key_assets IS NOT NULL AND array_length(p_key_assets, 1) > 0 THEN filled_fields := filled_fields + 1; END IF;
    
    -- Additional fields based on goal type
    CASE p_goal_type
        WHEN 'iso_certification' THEN
            total_fields := total_fields + 1; -- existing_policies
            IF p_existing_policies IS NOT NULL AND array_length(p_existing_policies, 1) > 0 THEN
                filled_fields := filled_fields + 1;
            END IF;
            
        WHEN 'gdpr_audit' THEN
            total_fields := total_fields + 2; -- data_processing_types, legal_basis (from custom_responses)
            IF p_custom_responses ? 'data_processing_types' THEN filled_fields := filled_fields + 1; END IF;
            IF p_custom_responses ? 'legal_basis' THEN filled_fields := filled_fields + 1; END IF;
            
        WHEN 'ai_risk_management' THEN
            total_fields := total_fields + 1; -- ai_systems (from custom_responses)
            IF p_custom_responses ? 'ai_systems' THEN filled_fields := filled_fields + 1; END IF;
    END CASE;
    
    -- Calculate percentage
    IF total_fields > 0 THEN
        completion_score := ROUND((filled_fields::DECIMAL / total_fields::DECIMAL), 2);
    END IF;
    
    RETURN completion_score;
END;
$$ LANGUAGE plpgsql;

-- Function to generate engagement score for contacts
CREATE OR REPLACE FUNCTION calculate_engagement_score(
    p_session_id UUID
)
RETURNS DECIMAL(3,2) AS $$
DECLARE
    score DECIMAL(3,2) := 0;
    interaction_count INTEGER;
    document_count INTEGER;
    avg_time_spent DECIMAL;
BEGIN
    -- Count interactions (each message adds to engagement)
    SELECT COUNT(*) INTO interaction_count
    FROM demo_interactions 
    WHERE session_id = p_session_id AND message_type = 'user';
    
    -- Count documents viewed
    SELECT COUNT(DISTINCT document_id) INTO document_count
    FROM demo_browsing_logs
    WHERE session_id = p_session_id AND event_type = 'document_view';
    
    -- Average time spent on documents
    SELECT AVG(time_spent_seconds) INTO avg_time_spent
    FROM demo_browsing_logs
    WHERE session_id = p_session_id AND time_spent_seconds IS NOT NULL;
    
    -- Calculate score (0-1 scale)
    score := LEAST(1.0, 
        (interaction_count * 0.1) +  -- Each interaction worth 0.1
        (document_count * 0.3) +     -- Each document view worth 0.3
        COALESCE(LEAST(0.4, avg_time_spent / 300.0), 0) -- Time spent (max 5 min = 0.4)
    );
    
    RETURN ROUND(score, 2);
END;
$$ LANGUAGE plpgsql;

-- =====================================================================================
-- MIGRATION 4: Row Level Security (RLS) Policies
-- =====================================================================================

-- Enable RLS on all tables
ALTER TABLE demo_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE demo_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE demo_intake_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE demo_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE demo_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE demo_browsing_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for anonymous access (demo users)
-- Note: In production, you might want more restrictive policies

-- Demo Sessions: Allow anonymous users to access their own sessions
CREATE POLICY "Anonymous users can manage their own sessions" ON demo_sessions
    FOR ALL USING (true); -- For demo, allow all access

-- Demo Interactions: Users can only access interactions from their sessions
CREATE POLICY "Users can access interactions from their sessions" ON demo_interactions
    FOR ALL USING (true); -- For demo, allow all access

-- Demo Intake Data: Users can only access their own intake data
CREATE POLICY "Users can access their own intake data" ON demo_intake_data
    FOR ALL USING (true); -- For demo, allow all access

-- Demo Documents: Users can only access documents from their sessions
CREATE POLICY "Users can access documents from their sessions" ON demo_documents
    FOR ALL USING (true); -- For demo, allow all access

-- Demo Contacts: Users can only access their own contact submissions
CREATE POLICY "Users can access their own contact data" ON demo_contacts
    FOR ALL USING (true); -- For demo, allow all access

-- Demo Browsing Logs: Users can only access their own browsing logs
CREATE POLICY "Users can access their own browsing logs" ON demo_browsing_logs
    FOR ALL USING (true); -- For demo, allow all access

-- =====================================================================================
-- MIGRATION 5: Analytics Views and Functions
-- =====================================================================================

-- Materialized view for session analytics
CREATE MATERIALIZED VIEW session_analytics AS
SELECT 
    DATE(created_at) as date,
    goal_detected,
    COUNT(*) as total_sessions,
    COUNT(*) FILTER (WHERE intake_completed) as completed_intake,
    COUNT(*) FILTER (WHERE documents_generated) as generated_docs,
    COUNT(*) FILTER (WHERE preview_ready) as preview_ready,
    COUNT(*) FILTER (WHERE lead_captured) as captured_leads,
    AVG(EXTRACT(EPOCH FROM (updated_at - created_at))) as avg_session_duration_seconds,
    COUNT(DISTINCT CASE WHEN goal_detected IS NOT NULL THEN id END) as goal_detected_count
FROM demo_sessions
WHERE created_at >= CURRENT_DATE - INTERVAL '90 days' -- Last 90 days
GROUP BY DATE(created_at), goal_detected
ORDER BY date DESC, goal_detected;

-- Create index on the materialized view
CREATE INDEX idx_session_analytics_date ON session_analytics(date);
CREATE INDEX idx_session_analytics_goal ON session_analytics(goal_detected);

-- Document analytics view
CREATE MATERIALIZED VIEW document_analytics AS
SELECT 
    document_type,
    COUNT(*) as total_generated,
    AVG(preview_count) as avg_preview_count,
    AVG(word_count) as avg_word_count,
    AVG(generation_time_ms) as avg_generation_time_ms,
    COUNT(*) FILTER (WHERE preview_count > 0) as documents_viewed,
    DATE(created_at) as date
FROM demo_documents
WHERE created_at >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY document_type, DATE(created_at)
ORDER BY date DESC, document_type;

-- Function to refresh analytics (call this daily via cron or scheduler)
CREATE OR REPLACE FUNCTION refresh_analytics()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW session_analytics;
    REFRESH MATERIALIZED VIEW document_analytics;
END;
$$ LANGUAGE plpgsql;

-- =====================================================================================
-- MIGRATION 6: Data Cleanup and Maintenance Functions
-- =====================================================================================

-- Function to clean up old sessions (run daily)
CREATE OR REPLACE FUNCTION cleanup_old_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete sessions older than 30 days with no lead capture
    DELETE FROM demo_sessions 
    WHERE created_at < NOW() - INTERVAL '30 days' 
      AND lead_captured = FALSE;
      
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Archive old sessions with leads (mark as archived instead of deleting)
    UPDATE demo_sessions 
    SET session_tags = array_append(session_tags, 'archived')
    WHERE created_at < NOW() - INTERVAL '2 years'
      AND lead_captured = TRUE
      AND NOT ('archived' = ANY(session_tags));
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to update intake completion percentages
CREATE OR REPLACE FUNCTION update_intake_completion_percentages()
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER;
    rec RECORD;
BEGIN
    updated_count := 0;
    
    FOR rec IN 
        SELECT session_id, goal_type, company_size, industry, infrastructure_type, 
               key_assets, existing_policies, custom_responses
        FROM demo_intake_data
        WHERE completed_at IS NULL
    LOOP
        UPDATE demo_intake_data
        SET completion_percentage = calculate_intake_completion(
            rec.goal_type, rec.company_size, rec.industry, rec.infrastructure_type,
            rec.key_assets, rec.existing_policies, rec.custom_responses
        )
        WHERE session_id = rec.session_id;
        
        updated_count := updated_count + 1;
    END LOOP;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================================================
-- MIGRATION 7: Sample Data (Optional - for testing)
-- =====================================================================================

-- Insert sample goal detection patterns (for the vector database)
-- This would typically be done through the application, but including here for reference

INSERT INTO demo_sessions (id, goal_detected, goal_confidence, session_tags) VALUES
    ('00000000-0000-0000-0000-000000000001', 'iso_certification', 0.95, ARRAY['sample', 'test']),
    ('00000000-0000-0000-0000-000000000002', 'gdpr_audit', 0.88, ARRAY['sample', 'test']),
    ('00000000-0000-0000-0000-00**********', 'ai_risk_management', 0.92, ARRAY['sample', 'test']);

-- Insert sample intake data
INSERT INTO demo_intake_data (session_id, goal_type, company_size, industry, completion_percentage) VALUES
    ('00000000-0000-0000-0000-000000000001', 'iso_certification', '11-50', 'Technology', 0.8),
    ('00000000-0000-0000-0000-000000000002', 'gdpr_audit', '51-200', 'Healthcare', 0.6),
    ('00000000-0000-0000-0000-00**********', 'ai_risk_management', '201-500', 'Financial Services', 0.9);

-- =====================================================================================
-- MIGRATION COMPLETE
-- =====================================================================================

-- Verify the migration
SELECT 
    'demo_sessions' as table_name, 
    COUNT(*) as row_count 
FROM demo_sessions
UNION ALL
SELECT 'demo_interactions', COUNT(*) FROM demo_interactions
UNION ALL  
SELECT 'demo_intake_data', COUNT(*) FROM demo_intake_data
UNION ALL
SELECT 'demo_documents', COUNT(*) FROM demo_documents
UNION ALL
SELECT 'demo_contacts', COUNT(*) FROM demo_contacts
UNION ALL
SELECT 'demo_browsing_logs', COUNT(*) FROM demo_browsing_logs;

-- Show available functions
SELECT routine_name, routine_type 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND routine_name LIKE '%demo%' OR routine_name IN ('calculate_intake_completion', 'calculate_engagement_score', 'cleanup_old_sessions', 'refresh_analytics');

COMMENT ON DATABASE postgres IS 'ArionComply Demo Database - Migration v1.0 completed successfully';),
    job_title TEXT,
    
    -- Engagement Data
    interest_area TEXT,
    message TEXT,
    documents_viewed TEXT[] DEFAULT '{}',
    engagement_score DECIMAL(3,2) CHECK (engagement_score >= 0 AND engagement_score <= 1),
    
    -- Consent Management (GDPR Compliance)
    follow_up_consented BOOLEAN DEFAULT TRUE,
    marketing_consented BOOLEAN DEFAULT FALSE,
    data_processing_consented BOOLEAN NOT NULL DEFAULT TRUE,
    consent_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    consent_ip_address INET,
    
    -- Marketing Attribution
    lead_source TEXT,
    utm_campaign TEXT,
    utm_source TEXT,
    utm_medium TEXT,
    
    -- CRM Integration
    crm_synced BOOLEAN DEFAULT FALSE,
    crm_sync_at TIMESTAMPTZ,
    
    -- Data Retention (GDPR Compliance)
    data_retention_until DATE NOT NULL DEFAULT (CURRENT_DATE + INTERVAL '2 years'),
    
    -- Security Audit Fields
    created_by_ip INET,
    last_modified_at TIMESTAMPTZ DEFAULT NOW()
);

-- 6. Demo Browsing Logs Table
CREATE TABLE demo_browsing_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES demo_sessions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    event_type TEXT NOT NULL,
    page_url TEXT,
    document_id UUID REFERENCES demo_documents(id) ON DELETE SET NULL,
    time_spent_seconds INTEGER,
    scroll_percentage DECIMAL(3,2) CHECK (scroll_percentage >= 0 AND scroll_percentage <= 1),
    referrer_url TEXT,
    user_agent TEXT,
    viewport_width INTEGER,
    viewport_height INTEGER,
    metadata JSONB
);

-- =====================================================================================
-- MIGRATION 2: Create Performance Indexes
-- =====================================================================================

-- Demo Sessions Indexes
CREATE INDEX idx_sessions_created_at ON demo_sessions(created_at);
CREATE INDEX idx_sessions_goal ON demo_sessions(goal_detected);
CREATE INDEX idx_sessions_status ON demo_sessions(intake_completed, documents_generated, preview_ready);
CREATE INDEX idx_sessions_updated_at ON demo_sessions(updated_at);

-- Demo Interactions Indexes
CREATE INDEX idx_interactions_session ON demo_interactions(session_id, created_at);
CREATE INDEX idx_interactions_type ON demo_interactions(message_type);
CREATE INDEX idx_interactions_intent ON demo_interactions(intent_detected);

-- Demo Intake Data Indexes
CREATE INDEX idx_intake_goal_type ON demo_intake_data(goal_type);
CREATE INDEX idx_intake_completion ON demo_intake_data(completion_percentage);

-- Demo Documents Indexes
CREATE INDEX idx_documents_session ON demo_documents(session_id, document_type);
CREATE INDEX idx_documents_type ON demo_documents(document_type);
CREATE INDEX idx_documents_created ON demo_documents(created_at);

-- Demo Contacts Indexes
CREATE INDEX idx_contacts_email ON demo_contacts(email);
CREATE INDEX idx_contacts_company ON demo_contacts(company);
CREATE INDEX idx_contacts_created ON demo_contacts(created_at);
CREATE INDEX idx_contacts_engagement ON demo_contacts(engagement_score);

-- Demo Browsing Logs Indexes
CREATE INDEX idx_browsing_session ON demo_browsing_logs(session_id, created_at);
CREATE INDEX idx_browsing_event_type ON demo_browsing_logs(event_type);
CREATE INDEX idx_browsing_document ON demo_browsing_logs(document_id);

-- =====================================================================================
-- MIGRATION 3: Create Utility Functions
-- =====================================================================================

-- Function to update session timestamp automatically
CREATE OR REPLACE FUNCTION update_session_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE demo_sessions 
    SET updated_at = NOW() 
    WHERE id = NEW.session_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update session timestamp on any related table activity
CREATE TRIGGER trigger_update_session_on_interaction
    AFTER INSERT OR UPDATE ON demo_interactions
    FOR EACH ROW EXECUTE FUNCTION update_session_timestamp();

CREATE TRIGGER trigger_update_session_on_intake
    AFTER INSERT OR UPDATE ON demo_intake_data
    FOR EACH ROW EXECUTE FUNCTION update_session_timestamp();

CREATE TRIGGER trigger_update_session_on_document
    AFTER INSERT OR UPDATE ON demo_documents
    FOR EACH ROW EXECUTE FUNCTION update_session_timestamp();

CREATE TRIGGER trigger_update_session_on_browsing
    AFTER INSERT ON demo_browsing_logs
    FOR EACH ROW EXECUTE FUNCTION update_session_timestamp();

-- Function to calculate intake completion percentage
CREATE OR REPLACE FUNCTION calculate_intake_completion(
    p_goal_type TEXT,
    p_company_size TEXT,
    p_industry TEXT,
    p_infrastructure_type TEXT,
    p_key_assets TEXT[],
    p_existing_policies TEXT[],
    p_custom_responses JSONB
)
RETURNS DECIMAL(3,2) AS $$
DECLARE
    completion_score DECIMAL(3,2) := 0;
    total_fields INTEGER := 0;
    filled_fields INTEGER := 0;
BEGIN
    -- Base required fields for all goal types
    total_fields := 4; -- company_size, industry, infrastructure_type, key_assets
    
    IF p_company_size IS NOT NULL THEN filled_fields := filled_fields + 1; END IF;
    IF p_industry IS NOT NULL THEN filled_fields := filled_fields + 1; END IF;
    IF p_infrastructure_type IS NOT NULL THEN filled_fields := filled_fields + 1; END IF;
    IF p_key_assets IS NOT NULL AND array_length(p_key_assets, 1) > 0 THEN filled_fields := filled_fields + 1; END IF;
    
    -- Additional fields based on goal type
    CASE p_goal_type
        WHEN 'iso_certification' THEN
            total_fields := total_fields + 1; -- existing_policies
            IF p_existing_policies IS NOT NULL AND array_length(p_existing_policies, 1) > 0 THEN
                filled_fields := filled_fields + 1;
            END IF;
            
        WHEN 'gdpr_audit' THEN
            total_fields := total_fields + 2; -- data_processing_types, legal_basis (from custom_responses)
            IF p_custom_responses ? 'data_processing_types' THEN filled_fields := filled_fields + 1; END IF;
            IF p_custom_responses ? 'legal_basis' THEN filled_fields := filled_fields + 1; END IF;
            
        WHEN 'ai_risk_management' THEN
            total_fields := total_fields + 1; -- ai_systems (from custom_responses)
            IF p_custom_responses ? 'ai_systems' THEN filled_fields := filled_fields + 1; END IF;
    END CASE;
    
    -- Calculate percentage
    IF total_fields > 0 THEN
        completion_score := ROUND((filled_fields::DECIMAL / total_fields::DECIMAL), 2);
    END IF;
    
    RETURN completion_score;
END;
$$ LANGUAGE plpgsql;

-- Function to generate engagement score for contacts
CREATE OR REPLACE FUNCTION calculate_engagement_score(
    p_session_id UUID
)
RETURNS DECIMAL(3,2) AS $$
DECLARE
    score DECIMAL(3,2) := 0;
    interaction_count INTEGER;
    document_count INTEGER;
    avg_time_spent DECIMAL;
BEGIN
    -- Count interactions (each message adds to engagement)
    SELECT COUNT(*) INTO interaction_count
    FROM demo_interactions 
    WHERE session_id = p_session_id AND message_type = 'user';
    
    -- Count documents viewed
    SELECT COUNT(DISTINCT document_id) INTO document_count
    FROM demo_browsing_logs
    WHERE session_id = p_session_id AND event_type = 'document_view';
    
    -- Average time spent on documents
    SELECT AVG(time_spent_seconds) INTO avg_time_spent
    FROM demo_browsing_logs
    WHERE session_id = p_session_id AND time_spent_seconds IS NOT NULL;
    
    -- Calculate score (0-1 scale)
    score := LEAST(1.0, 
        (interaction_count * 0.1) +  -- Each interaction worth 0.1
        (document_count * 0.3) +     -- Each document view worth 0.3
        COALESCE(LEAST(0.4, avg_time_spent / 300.0), 0) -- Time spent (max 5 min = 0.4)
    );
    
    RETURN ROUND(score, 2);
END;
$$ LANGUAGE plpgsql;

-- =====================================================================================
-- MIGRATION 4: Row Level Security (RLS) Policies
-- =====================================================================================

-- Enable RLS on all tables
ALTER TABLE demo_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE demo_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE demo_intake_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE demo_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE demo_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE demo_browsing_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for anonymous access (demo users)
-- Note: In production, you might want more restrictive policies

-- Demo Sessions: Allow anonymous users to access their own sessions
CREATE POLICY "Anonymous users can manage their own sessions" ON demo_sessions
    FOR ALL USING (true); -- For demo, allow all access

-- Demo Interactions: Users can only access interactions from their sessions
CREATE POLICY "Users can access interactions from their sessions" ON demo_interactions
    FOR ALL USING (true); -- For demo, allow all access

-- Demo Intake Data: Users can only access their own intake data
CREATE POLICY "Users can access their own intake data" ON demo_intake_data
    FOR ALL USING (true); -- For demo, allow all access

-- Demo Documents: Users can only access documents from their sessions
CREATE POLICY "Users can access documents from their sessions" ON demo_documents
    FOR ALL USING (true); -- For demo, allow all access

-- Demo Contacts: Users can only access their own contact submissions
CREATE POLICY "Users can access their own contact data" ON demo_contacts
    FOR ALL USING (true); -- For demo, allow all access

-- Demo Browsing Logs: Users can only access their own browsing logs
CREATE POLICY "Users can access their own browsing logs" ON demo_browsing_logs
    FOR ALL USING (true); -- For demo, allow all access

-- =====================================================================================
-- MIGRATION 5: Analytics Views and Functions
-- =====================================================================================

-- Materialized view for session analytics
CREATE MATERIALIZED VIEW session_analytics AS
SELECT 
    DATE(created_at) as date,
    goal_detected,
    COUNT(*) as total_sessions,
    COUNT(*) FILTER (WHERE intake_completed) as completed_intake,
    COUNT(*) FILTER (WHERE documents_generated) as generated_docs,
    COUNT(*) FILTER (WHERE preview_ready) as preview_ready,
    COUNT(*) FILTER (WHERE lead_captured) as captured_leads,
    AVG(EXTRACT(EPOCH FROM (updated_at - created_at))) as avg_session_duration_seconds,
    COUNT(DISTINCT CASE WHEN goal_detected IS NOT NULL THEN id END) as goal_detected_count
FROM demo_sessions
WHERE created_at >= CURRENT_DATE - INTERVAL '90 days' -- Last 90 days
GROUP BY DATE(created_at), goal_detected
ORDER BY date DESC, goal_detected;

-- Create index on the materialized view
CREATE INDEX idx_session_analytics_date ON session_analytics(date);
CREATE INDEX idx_session_analytics_goal ON session_analytics(goal_detected);

-- Document analytics view
CREATE MATERIALIZED VIEW document_analytics AS
SELECT 
    document_type,
    COUNT(*) as total_generated,
    AVG(preview_count) as avg_preview_count,
    AVG(word_count) as avg_word_count,
    AVG(generation_time_ms) as avg_generation_time_ms,
    COUNT(*) FILTER (WHERE preview_count > 0) as documents_viewed,
    DATE(created_at) as date
FROM demo_documents
WHERE created_at >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY document_type, DATE(created_at)
ORDER BY date DESC, document_type;

-- Function to refresh analytics (call this daily via cron or scheduler)
CREATE OR REPLACE FUNCTION refresh_analytics()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW session_analytics;
    REFRESH MATERIALIZED VIEW document_analytics;
END;
$$ LANGUAGE plpgsql;

-- =====================================================================================
-- MIGRATION 6: Data Cleanup and Maintenance Functions
-- =====================================================================================

-- Function to clean up old sessions (run daily)
CREATE OR REPLACE FUNCTION cleanup_old_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete sessions older than 30 days with no lead capture
    DELETE FROM demo_sessions 
    WHERE created_at < NOW() - INTERVAL '30 days' 
      AND lead_captured = FALSE;
      
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Archive old sessions with leads (mark as archived instead of deleting)
    UPDATE demo_sessions 
    SET session_tags = array_append(session_tags, 'archived')
    WHERE created_at < NOW() - INTERVAL '2 years'
      AND lead_captured = TRUE
      AND NOT ('archived' = ANY(session_tags));
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to update intake completion percentages
CREATE OR REPLACE FUNCTION update_intake_completion_percentages()
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER;
    rec RECORD;
BEGIN
    updated_count := 0;
    
    FOR rec IN 
        SELECT session_id, goal_type, company_size, industry, infrastructure_type, 
               key_assets, existing_policies, custom_responses
        FROM demo_intake_data
        WHERE completed_at IS NULL
    LOOP
        UPDATE demo_intake_data
        SET completion_percentage = calculate_intake_completion(
            rec.goal_type, rec.company_size, rec.industry, rec.infrastructure_type,
            rec.key_assets, rec.existing_policies, rec.custom_responses
        )
        WHERE session_id = rec.session_id;
        
        updated_count := updated_count + 1;
    END LOOP;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================================================
-- MIGRATION 7: Sample Data (Optional - for testing)
-- =====================================================================================

-- Insert sample goal detection patterns (for the vector database)
-- This would typically be done through the application, but including here for reference

INSERT INTO demo_sessions (id, goal_detected, goal_confidence, session_tags) VALUES
    ('00000000-0000-0000-0000-000000000001', 'iso_certification', 0.95, ARRAY['sample', 'test']),
    ('00000000-0000-0000-0000-000000000002', 'gdpr_audit', 0.88, ARRAY['sample', 'test']),
    ('00000000-0000-0000-0000-00**********', 'ai_risk_management', 0.92, ARRAY['sample', 'test']);

-- Insert sample intake data
INSERT INTO demo_intake_data (session_id, goal_type, company_size, industry, completion_percentage) VALUES
    ('00000000-0000-0000-0000-000000000001', 'iso_certification', '11-50', 'Technology', 0.8),
    ('00000000-0000-0000-0000-000000000002', 'gdpr_audit', '51-200', 'Healthcare', 0.6),
    ('00000000-0000-0000-0000-00**********', 'ai_risk_management', '201-500', 'Financial Services', 0.9);

-- =====================================================================================
-- MIGRATION COMPLETE
-- =====================================================================================

-- Verify the migration
SELECT 
    'demo_sessions' as table_name, 
    COUNT(*) as row_count 
FROM demo_sessions
UNION ALL
SELECT 'demo_interactions', COUNT(*) FROM demo_interactions
UNION ALL  
SELECT 'demo_intake_data', COUNT(*) FROM demo_intake_data
UNION ALL
SELECT 'demo_documents', COUNT(*) FROM demo_documents
UNION ALL
SELECT 'demo_contacts', COUNT(*) FROM demo_contacts
UNION ALL
SELECT 'demo_browsing_logs', COUNT(*) FROM demo_browsing_logs;

-- Show available functions
SELECT routine_name, routine_type 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND routine_name LIKE '%demo%' OR routine_name IN ('calculate_intake_completion', 'calculate_engagement_score', 'cleanup_old_sessions', 'refresh_analytics');

COMMENT ON DATABASE postgres IS 'ArionComply Demo Database - Migration v1.0 completed successfully';