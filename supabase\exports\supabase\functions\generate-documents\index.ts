// supabase/functions/generate-documents/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-api-key, x-session-id, accept',
  'Access-Control-Allow-Methods': 'POST, OPTIONS, GET'
};
// Document templates for READ-ONLY preview generation
const DOCUMENT_TEMPLATES = {
  contract: {
    type: 'contract',
    name: 'Service Agreement Contract',
    description: 'Professional services contract template',
    is_preview_only: true,
    template: `SERVICE AGREEMENT

This Service Agreement ("Agreement") is entered into on {date} between:

CLIENT: {client_name}
Address: {client_address}
Email: {client_email}

SERVICE PROVIDER: {provider_name}
Address: {provider_address}
Email: {provider_email}

1. SERVICES
The Service Provider agrees to provide the following services:
{services_description}

2. PAYMENT TERMS
- Total Contract Value: {contract_value}
- Payment Schedule: {payment_schedule}
- Payment Method: {payment_method}

3. TIMELINE
Project Start Date: {start_date}
Project End Date: {end_date}
Key Milestones: {milestones}

4. TERMS AND CONDITIONS
{terms_and_conditions}

Signatures:
Client: _________________ Date: _________
Service Provider: _________________ Date: _________`,
    required_fields: [
      'client_name',
      'services_description',
      'contract_value'
    ]
  },
  proposal: {
    type: 'proposal',
    name: 'Business Proposal',
    description: 'Professional business proposal template',
    is_preview_only: true,
    template: `BUSINESS PROPOSAL

Prepared for: {client_name}
Prepared by: {company_name}
Date: {date}

EXECUTIVE SUMMARY
{executive_summary}

PROJECT OVERVIEW
{project_overview}

SCOPE OF WORK
{scope_of_work}

TIMELINE
{timeline}

INVESTMENT
{investment_details}

WHY CHOOSE US
{why_choose_us}

NEXT STEPS
{next_steps}

Contact Information:
{contact_info}`,
    required_fields: [
      'client_name',
      'company_name',
      'project_overview'
    ]
  },
  invoice: {
    type: 'invoice',
    name: 'Professional Invoice',
    description: 'Invoice template for services',
    is_preview_only: true,
    template: `INVOICE

Invoice #: {invoice_number}
Date: {date}
Due Date: {due_date}

Bill To:
{client_name}
{client_address}

From:
{company_name}
{company_address}

DESCRIPTION OF SERVICES
{service_description}

Amount Due: {amount_due}

Payment Terms: {payment_terms}
Payment Methods: {payment_methods}

Thank you for your business!`,
    required_fields: [
      'client_name',
      'service_description',
      'amount_due'
    ]
  },
  terms_of_service: {
    type: 'terms_of_service',
    name: 'Terms of Service',
    description: 'Website/service terms of service',
    is_preview_only: true,
    template: `TERMS OF SERVICE

Last Updated: {date}

1. ACCEPTANCE OF TERMS
By using {service_name}, you agree to these terms.

2. DESCRIPTION OF SERVICE
{service_description}

3. USER RESPONSIBILITIES
{user_responsibilities}

4. PRIVACY POLICY
{privacy_policy_summary}

5. LIMITATION OF LIABILITY
{liability_limitations}

6. TERMINATION
{termination_conditions}

7. GOVERNING LAW
These terms are governed by {governing_law}.

Contact: {contact_email}`,
    required_fields: [
      'service_name',
      'service_description'
    ]
  }
};
async function generatePreviewDocumentWithClaude(template, intakeData) {
  const CLAUDE_API_KEY = Deno.env.get('ANTHROPIC_API_KEY');
  if (!CLAUDE_API_KEY) {
    throw new Error('ANTHROPIC_API_KEY not configured');
  }
  const systemPrompt = `You are a professional document generator creating PREVIEW-ONLY documents for demonstration purposes.
  
  Fill out the provided template with realistic, professional content based on the intake data.
  Replace all {field_name} placeholders with appropriate content.
  If required fields are missing, generate realistic placeholder content.
  
  IMPORTANT: This is a READ-ONLY PREVIEW for demonstration purposes only.
  - Use professional, realistic content
  - Make it look authentic but clearly indicate it's a demo
  - Keep content appropriate for business use
  - Maintain legal structure but add "DEMO PREVIEW" watermarks where appropriate`;
  const userPrompt = `Template Type: ${template.type}
Template: ${template.template}

Intake Data: ${JSON.stringify(intakeData, null, 2)}

Generate a professional ${template.type} document using the template. Fill in all placeholders with realistic content based on the intake data. This is for demo preview purposes only.`;
  const requestBody = {
    model: 'claude-3-5-sonnet-20241022',
    max_tokens: 1500,
    temperature: 0.3,
    system: systemPrompt,
    messages: [
      {
        role: 'user',
        content: userPrompt
      }
    ]
  };
  const response = await fetch('https://api.anthropic.com/v1/messages', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': CLAUDE_API_KEY,
      'anthropic-version': '2023-06-01'
    },
    body: JSON.stringify(requestBody)
  });
  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Claude API error: ${response.status} - ${errorText}`);
  }
  const data = await response.json();
  return data.content[0].text;
}
function generateDocumentPreview(content, maxLength = 150) {
  const cleanContent = content.replace(/\n+/g, ' ').trim();
  return cleanContent.length > maxLength ? cleanContent.substring(0, maxLength) + '...' : cleanContent;
}
function calculateWordCount(content) {
  return content.trim().split(/\s+/).filter((word)=>word.length > 0).length;
}
serve(async (req)=>{
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  try {
    const supabase = createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_ANON_KEY') ?? '');
    // Parse request body with error handling
    let requestBody;
    try {
      requestBody = await req.json();
    } catch (jsonError) {
      console.error('JSON parsing error:', jsonError);
      return new Response(JSON.stringify({
        error: 'Invalid JSON in request body',
        details: jsonError.message
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    const { session_id, document_type, intake_data, custom_requirements } = requestBody;
    if (!session_id || !document_type) {
      return new Response(JSON.stringify({
        error: 'session_id and document_type required'
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Get session data for additional context
    const { data: session, error: sessionError } = await supabase.from('demo_sessions').select('*').eq('session_id', session_id).single();
    if (sessionError && sessionError.code !== 'PGRST116') {
      console.error('Session error:', sessionError);
    // Continue without session data for demo purposes
    }
    // Select appropriate template
    const template = DOCUMENT_TEMPLATES[document_type];
    if (!template) {
      return new Response(JSON.stringify({
        error: `Document type '${document_type}' not supported`,
        available_types: Object.keys(DOCUMENT_TEMPLATES)
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Combine intake data with demo data
    const combinedData = {
      // Default demo values
      date: new Date().toLocaleDateString(),
      company_name: 'ArionComply Demo',
      provider_name: 'Professional Services LLC',
      provider_address: '123 Business Avenue, Suite 100\nDemo City, DC 12345',
      provider_email: '<EMAIL>',
      payment_schedule: 'Net 30 days',
      payment_method: 'Wire transfer or check',
      payment_terms: 'Payment due within 30 days of invoice date',
      terms_and_conditions: 'Standard professional services terms apply. This is a demo document.',
      governing_law: 'Delaware',
      // User provided data
      ...intake_data,
      // Additional context
      session_context: session?.context || {},
      custom_requirements,
      timestamp: new Date().toISOString(),
      is_demo: true
    };
    // Generate document content with Claude
    const documentContent = await generatePreviewDocumentWithClaude(template, combinedData);
    // Add demo watermark
    const watermarkedContent = `${documentContent}

---
DEMO PREVIEW DOCUMENT
Generated by ArionComply Platform
This is a demonstration document for preview purposes only.`;
    // Generate preview and word count
    const preview = generateDocumentPreview(documentContent);
    const wordCount = calculateWordCount(documentContent);
    // Store document in database as read-only preview
    const { data: document, error: docError } = await supabase.from('demo_documents').insert({
      session_id,
      document_type,
      title: `${template.name} (Preview)`,
      content: watermarkedContent,
      preview,
      metadata: {
        template_used: template.type,
        intake_data: combinedData,
        generated_at: new Date().toISOString(),
        word_count: wordCount,
        required_fields: template.required_fields,
        is_preview_only: true,
        is_demo: true,
        protection_level: 'read_only'
      },
      status: 'preview_ready'
    }).select().single();
    if (docError) {
      console.error('Database error:', docError);
      throw docError;
    }
    // Update session to mark as preview_ready
    if (session) {
      await supabase.from('demo_sessions').update({
        preview_ready: true,
        context: {
          ...session.context || {},
          last_document_generated: document.id,
          document_type,
          generated_at: new Date().toISOString(),
          documents_generated: (session.context?.documents_generated || 0) + 1
        }
      }).eq('session_id', session_id);
    }
    // Log the document generation
    await supabase.from('demo_interactions').insert({
      session_id,
      user_message: `Generate ${document_type} document preview`,
      assistant_response: `Generated read-only preview: ${template.name}`,
      detected_goal: 'document_generation',
      context: {
        document_id: document.id,
        document_type,
        template_used: template.type,
        is_preview_only: true,
        word_count: wordCount
      },
      created_at: new Date().toISOString()
    });
    const response = {
      success: true,
      document: {
        id: document.id,
        type: document_type,
        title: template.name,
        preview,
        status: 'preview_ready',
        protection: {
          is_read_only: true,
          is_demo: true,
          copy_disabled: true,
          download_disabled: true,
          print_disabled: true
        },
        metadata: {
          word_count: wordCount,
          generated_at: document.created_at,
          template_used: template.type,
          is_preview_only: true
        }
      },
      session_updated: true,
      message: "Document preview generated successfully. This is a read-only demonstration version."
    };
    return new Response(JSON.stringify(response), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Error in generate-documents:', error);
    return new Response(JSON.stringify({
      error: 'Document preview generation failed',
      details: error.message,
      is_demo_error: true
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});
