
<!--
  ArionComply Demo - Email Verification Page
  Version: 1.0
  Purpose: Email verification handler for demo access requests
  
  Features:
  - Secure token-based email verification
  - Multi-state UI (loading, success, error)
  - Automatic session initialization upon successful verification
  - User-friendly error handling with specific guidance
  - Responsive design for all devices
  - Integration with ArionComply analytics and tracking
  
  User Flow:
  1. User clicks verification link from email
  2. Token extracted from URL parameters
  3. API call to verify-email endpoint
  4. Success: Session created, user redirected to demo
  5. Error: Clear messaging with retry options
  
  Security:
  - Tokens expire after 24 hours
  - One-time use verification tokens
  - Session-based access control
  - CSRF protection through token validation
  
  Dependencies:
  - config.js (API configuration)
  - utils.js (API utilities and analytics)
  - Supabase backend for verification processing
-->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Verify Email - ArionComply Demo</title>
  <style>
    :root {
      --brand-primary: #059669;
      --brand-secondary: #047857;
      --brand-light: #ecfdf5;
      --neutral-50: #f8fafc;
      --neutral-100: #f1f5f9;
      --neutral-200: #e2e8f0;
      --neutral-600: #475569;
      --neutral-700: #334155;
      --error: #ef4444;
      --success: #10b981;
    }

    * {
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--neutral-50);
      color: var(--neutral-700);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .verification-container {
      background: white;
      padding: 3rem;
      border-radius: 16px;
      box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
      max-width: 500px;
      width: 90%;
      text-align: center;
    }

    .logo {
      font-size: 2rem;
      font-weight: bold;
      color: var(--brand-primary);
      margin-bottom: 2rem;
    }

    .verification-icon {
      font-size: 4rem;
      margin-bottom: 1.5rem;
    }

    .verification-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--neutral-700);
    }

    .verification-message {
      font-size: 1rem;
      line-height: 1.6;
      margin-bottom: 2rem;
      color: var(--neutral-600);
    }

    .loading-state {
      display: block;
    }

    .success-state, .error-state {
      display: none;
    }

    .success-state.show, .error-state.show {
      display: block;
    }

    .spinner {
      border: 3px solid var(--neutral-200);
      border-top: 3px solid var(--brand-primary);
      border-radius: 50%;
      width: 50px;
      height: 50px;
      animation: spin 1s linear infinite;
      margin: 0 auto 2rem;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .btn {
      padding: 0.875rem 2rem;
      border: none;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      text-decoration: none;
      display: inline-block;
      transition: all 0.2s ease;
      margin: 0.5rem;
    }

    .btn-primary {
      background: var(--brand-primary);
      color: white;
    }

    .btn-primary:hover {
      background: var(--brand-secondary);
      transform: translateY(-1px);
    }

    .btn-secondary {
      background: var(--neutral-200);
      color: var(--neutral-700);
    }

    .btn-secondary:hover {
      background: var(--neutral-300);
    }

    .error-details {
      background: #fef2f2;
      border: 1px solid #fecaca;
      border-radius: 8px;
      padding: 1rem;
      margin: 1.5rem 0;
      color: var(--error);
      font-size: 0.875rem;
      text-align: left;
    }

    .success-details {
      background: var(--brand-light);
      border: 1px solid #a7f3d0;
      border-radius: 8px;
      padding: 1rem;
      margin: 1.5rem 0;
      color: var(--brand-primary);
      font-size: 0.875rem;
    }

    .user-info {
      background: var(--neutral-100);
      border-radius: 8px;
      padding: 1rem;
      margin: 1.5rem 0;
    }

    .user-info strong {
      color: var(--neutral-800);
    }

    @media (max-width: 768px) {
      .verification-container {
        padding: 2rem 1.5rem;
        margin: 1rem;
      }

      .verification-title {
        font-size: 1.25rem;
      }
    }
  </style>
</head>
<body>
  <div class="verification-container">
    <div class="logo">ArionComply</div>

    <!-- Loading State -->
    <div class="loading-state" id="loadingState">
      <div class="verification-icon">📧</div>
      <h1 class="verification-title">Verifying Your Email</h1>
      <div class="spinner"></div>
      <p class="verification-message">Please wait while we verify your email address...</p>
    </div>

    <!-- Success State -->
    <div class="success-state" id="successState">
      <div class="verification-icon">✅</div>
      <h1 class="verification-title">Email Verified Successfully!</h1>
      <p class="verification-message">Welcome to ArionComply! Your email has been verified and your demo session is ready.</p>
      
      <div class="user-info" id="userInfo">
        <!-- User information will be populated here -->
      </div>

      <div class="success-details">
        <strong>What's Next:</strong>
        <ul style="text-align: left; margin: 0.5rem 0 0 1rem;">
          <li>Explore AI-powered compliance automation</li>
          <li>Generate personalized compliance documents</li>
          <li>Get expert guidance on your compliance journey</li>
          <li>Schedule a consultation with our team</li>
        </ul>
      </div>

      <div style="margin-top: 2rem;">
        <a href="#" class="btn btn-primary" id="accessDemoBtn">Access Demo Platform</a>
        <a href="index.html" class="btn btn-secondary">Return to Home</a>
      </div>
    </div>

    <!-- Error State -->
    <div class="error-state" id="errorState">
      <div class="verification-icon">❌</div>
      <h1 class="verification-title">Verification Failed</h1>
      <p class="verification-message">We couldn't verify your email address. This could be due to several reasons:</p>

      <div class="error-details" id="errorDetails">
        <!-- Error details will be populated here -->
      </div>

      <div style="margin-top: 2rem;">
        <button class="btn btn-primary" onclick="retryVerification()">Try Again</button>
        <a href="index.html" class="btn btn-secondary">Request New Link</a>
        <a href="mailto:<EMAIL>" class="btn btn-secondary">Contact Support</a>
      </div>
    </div>
  </div>

  <script>
    let verificationToken = null;
    let verificationData = null;

    document.addEventListener('DOMContentLoaded', initializeVerification);

    function initializeVerification() {
      console.log('🔍 Initializing email verification');
      
      // Get token from URL
      const urlParams = new URLSearchParams(window.location.search);
      verificationToken = urlParams.get('token');

      if (!verificationToken) {
        showError('No verification token found in URL', 'MISSING_TOKEN');
        return;
      }

      console.log('📧 Token found, starting verification...');
      
      // Start verification process
      setTimeout(verifyEmail, 1000); // Small delay for better UX
    }

    async function verifyEmail() {
      try {
        console.log('🔄 Calling verification API...');
        
        const response = await ArionUtils.api.call('verify-email', {
          token: verificationToken
        });

        console.log('📥 Verification response:', response);

        if (response.success) {
          verificationData = response;
          showSuccess(response);
        } else {
          throw new Error(response.error || 'Verification failed');
        }

      } catch (error) {
        console.error('❌ Verification failed:', error);
        
        let errorMessage = 'An unexpected error occurred during verification.';
        let errorCode = 'UNKNOWN_ERROR';

        if (error.message.includes('expired')) {
          errorMessage = 'Your verification link has expired. Please request a new one.';
          errorCode = 'EXPIRED_TOKEN';
        } else if (error.message.includes('invalid')) {
          errorMessage = 'The verification link is invalid or has already been used.';
          errorCode = 'INVALID_TOKEN';
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          errorMessage = 'Network error. Please check your connection and try again.';
          errorCode = 'NETWORK_ERROR';
        } else if (error.message) {
          errorMessage = error.message;
        }

        showError(errorMessage, errorCode);
      }
    }

    function showSuccess(data) {
      console.log('✅ Showing success state');
      
      // Hide loading, show success
      document.getElementById('loadingState').style.display = 'none';
      document.getElementById('successState').classList.add('show');

      // Populate user info if available
      if (data.userData) {
        const userInfoDiv = document.getElementById('userInfo');
        userInfoDiv.innerHTML = `
          <strong>Account Details:</strong><br>
          Email: ${data.userEmail}<br>
          ${data.userData.name ? `Name: ${data.userData.name}<br>` : ''}
          ${data.userData.company ? `Company: ${data.userData.company}` : ''}
        `;
      }

      // Set up demo access button
      const accessBtn = document.getElementById('accessDemoBtn');
      if (data.sessionId) {
        accessBtn.href = `demo.html?session=${data.sessionId}`;
      } else {
        accessBtn.href = 'demo.html';
      }

      // Track successful verification
      if (data.sessionId) {
        ArionUtils.analytics.track('email_verified', {
          userId: data.userId,
          email: data.userEmail
        }, data.sessionId);
      }
    }

    function showError(message, code) {
      console.log('❌ Showing error state:', code);
      
      // Hide loading, show error
      document.getElementById('loadingState').style.display = 'none';
      document.getElementById('errorState').classList.add('show');

      // Populate error details
      const errorDetailsDiv = document.getElementById('errorDetails');
      
      let errorHtml = `<strong>Error:</strong> ${message}<br><br>`;
      
      switch (code) {
        case 'EXPIRED_TOKEN':
          errorHtml += `
            <strong>What happened:</strong> Verification links expire after 24 hours for security.<br>
            <strong>What to do:</strong> Request a new verification link from our homepage.
          `;
          break;
          
        case 'INVALID_TOKEN':
          errorHtml += `
            <strong>What happened:</strong> This link may have already been used or is malformed.<br>
            <strong>What to do:</strong> Request a new verification link if you haven't verified yet.
          `;
          break;
          
        case 'NETWORK_ERROR':
          errorHtml += `
            <strong>What happened:</strong> We couldn't connect to our servers.<br>
            <strong>What to do:</strong> Check your internet connection and try again.
          `;
          break;
          
        case 'MISSING_TOKEN':
          errorHtml += `
            <strong>What happened:</strong> The verification link appears to be incomplete.<br>
            <strong>What to do:</strong> Make sure you clicked the full link from your email.
          `;
          break;
          
        default:
          errorHtml += `
            <strong>What to do:</strong> Try the verification again, or contact our support team if the problem persists.
          `;
      }

      errorDetailsDiv.innerHTML = errorHtml;

      // Track verification failure
      ArionUtils.analytics.track('email_verification_failed', {
        error: code,
        token: verificationToken ? 'present' : 'missing'
      });
    }

    function retryVerification() {
      console.log('🔄 Retrying verification...');
      
      // Hide error, show loading
      document.getElementById('errorState').classList.remove('show');
      document.getElementById('loadingState').style.display = 'block';

      // Retry verification after a short delay
      setTimeout(verifyEmail, 1000);
    }

    // Handle browser back/forward buttons
    window.addEventListener('popstate', (event) => {
      if (verificationData && verificationData.sessionId) {
        window.location.href = `demo.html?session=${verificationData.sessionId}`;
      }
    });

    // Auto-redirect after successful verification (optional)
    // Uncomment if you want automatic redirect after 5 seconds
    /*
    function autoRedirect() {
      if (verificationData && verificationData.sessionId) {
        setTimeout(() => {
          window.location.href = `demo.html?session=${verificationData.sessionId}`;
        }, 5000);
      }
    }
    */
  </script>
  <script src="src/config.js"></script>
  <script src="src/utils.js"></script>

</body>
</html>
