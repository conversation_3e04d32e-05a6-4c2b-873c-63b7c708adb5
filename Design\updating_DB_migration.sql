-- =====================================================================================
-- ArionComply Demo Database Migration
-- Version: 1.0
-- Date: 2025-07-14
-- Status: ✅ COMPLETED - Applied to oyvbuuugbfuvupruecvp.supabase.co on 2025-01-14
-- Tables Created: 6 tables, 15+ indexes, 8 functions, 2 materialized views
-- =====================================================================================

-- This migration has been successfully applied to the database
-- To verify, run: SELECT COUNT(*) FROM demo_sessions;
-- Expected result: Tables exist and contain sample data

-- For fresh deployments, copy and paste this entire file into Supabase SQL Editor


-- =====================================================================================
-- ArionComply Demo Database Migration - Column Updates
-- Version: 1.1
-- Date: 2025-07-14
-- Purpose: Add missing columns to demo_sessions table for router compatibility
-- =====================================================================================

-- Add missing columns to demo_sessions table
ALTER TABLE demo_sessions 
ADD COLUMN IF NOT EXISTS client_fingerprint TEXT,
ADD COLUMN IF NOT EXISTS expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '24 hours'),
ADD COLUMN IF NOT EXISTS ip_address_hash TEXT;

-- Update existing sessions to have expiry dates if they don't
UPDATE demo_sessions 
SET expires_at = (created_at + INTERVAL '24 hours')
WHERE expires_at IS NULL;

-- Add indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_sessions_fingerprint ON demo_sessions(client_fingerprint);
CREATE INDEX IF NOT EXISTS idx_sessions_expires ON demo_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_sessions_ip_hash ON demo_sessions(ip_address_hash);

-- Add is_flagged column if it doesn't exist (for security)
ALTER TABLE demo_sessions 
ADD COLUMN IF NOT EXISTS is_flagged BOOLEAN DEFAULT FALSE;

CREATE INDEX IF NOT EXISTS idx_sessions_flagged ON demo_sessions(is_flagged) WHERE is_flagged = TRUE;

-- Add last_activity_at column for session management
ALTER TABLE demo_sessions 
ADD COLUMN IF NOT EXISTS last_activity_at TIMESTAMPTZ DEFAULT NOW();

CREATE INDEX IF NOT EXISTS idx_sessions_activity ON demo_sessions(last_activity_at);

-- Update the session validation function to work with new columns
CREATE OR REPLACE FUNCTION is_session_valid(p_session_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    session_record RECORD;
BEGIN
    SELECT expires_at, is_flagged INTO session_record
    FROM demo_sessions
    WHERE id = p_session_id;
    
    -- Return false if session doesn't exist, is expired, or is flagged
    IF session_record IS NULL 
       OR session_record.expires_at < NOW() 
       OR session_record.is_flagged = TRUE THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Enhanced contact table for full compliance with router expectations
ALTER TABLE demo_contacts 
ADD COLUMN IF NOT EXISTS first_name TEXT,
ADD COLUMN IF NOT EXISTS last_name TEXT,
ADD COLUMN IF NOT EXISTS phone_number TEXT CHECK (phone_number IS NULL OR phone_number ~* '^\+?[1-9]\d{1,14}$'),
ADD COLUMN IF NOT EXISTS job_title TEXT,
ADD COLUMN IF NOT EXISTS interest_area TEXT,
ADD COLUMN IF NOT EXISTS data_processing_consented BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS marketing_consented BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS consent_timestamp TIMESTAMPTZ DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS consent_ip_address INET,
ADD COLUMN IF NOT EXISTS lead_source TEXT,
ADD COLUMN IF NOT EXISTS utm_campaign TEXT,
ADD COLUMN IF NOT EXISTS utm_source TEXT,
ADD COLUMN IF NOT EXISTS utm_medium TEXT,
ADD COLUMN IF NOT EXISTS crm_synced BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS crm_sync_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS data_retention_until DATE DEFAULT (CURRENT_DATE + INTERVAL '2 years');

-- For existing contacts that only have name/email/company, split name if needed
UPDATE demo_contacts 
SET first_name = COALESCE(first_name, split_part(name, ' ', 1)),
    last_name = COALESCE(last_name, split_part(name, ' ', 2))
WHERE first_name IS NULL AND name IS NOT NULL;

-- Add indexes for new contact columns
CREATE INDEX IF NOT EXISTS idx_contacts_phone ON demo_contacts(phone_number) WHERE phone_number IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_contacts_retention ON demo_contacts(data_retention_until);
CREATE INDEX IF NOT EXISTS idx_contacts_consent ON demo_contacts(data_processing_consented, consent_timestamp);
CREATE INDEX IF NOT EXISTS idx_contacts_crm_sync ON demo_contacts(crm_synced, crm_sync_at);

-- Update RLS policies to work with session validation
DROP POLICY IF EXISTS "Users can access valid sessions" ON demo_sessions;
CREATE POLICY "Users can access valid sessions" ON demo_sessions
    FOR SELECT USING (
        id::text = current_setting('request.headers', true)::json->>'x-session-id'
        AND is_session_valid(id)
    );

-- Verify the updates
SELECT 'Migration completed successfully' as status,
       COUNT(*) as total_sessions
FROM demo_sessions;

-- Show updated table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'demo_sessions' 
  AND table_schema = 'public'
ORDER BY ordinal_position;