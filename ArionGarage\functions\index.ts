// supabase/functions/chat-proxy/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
// Model configuration with tunnel URLs for your lab models
const MODEL_CONFIGS = {
  'smollm3': {
    tunnel_url: 'https://messaging-commons-fixed-annex.trycloudflare.com',
    endpoint: '/v1/chat/completions',
    headers: {
      'Content-Type': 'application/json'
    }
  },
  'mistral': {
    tunnel_url: 'https://differ-after-residential-interference.trycloudflare.com',
    endpoint: '/v1/chat/completions',
    headers: {
      'Content-Type': 'application/json'
    }
  }
};
// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, GET, OPTIONS'
};
// Format request based on model type
function formatRequestForModel(model, message, options = {}) {
  const { temperature = 0.7, max_tokens = 500 } = options;
  switch(model){
    case 'smollm3':
      // Updated system prompt for SmolLM3
      const smollm3SystemPrompt = "You are a helpful assistant. Respond directly and concisely to the user's message. Do not continue conversations beyond what the user asks. When creating lists, use simple, clean formatting with consistent numbering (1., 2., 3.) and clear line breaks.";
      return {
        model: model,
        messages: [
          {
            role: 'system',
            content: smollm3SystemPrompt
          },
          {
            role: 'user',
            content: message
          }
        ],
        temperature: 0.7,
        max_tokens: 500,
        stream: false,
        // Balanced parameters
        top_p: 0.9,
        frequency_penalty: 0.1,
        presence_penalty: 0.1
      };
    case 'mistral':
      // Same system prompt for consistency
      const mistralSystemPrompt = "You are a helpful assistant. Respond directly and concisely to the user's message. Do not continue conversations beyond what the user asks. Do not make up additional questions or responses.";
      return {
        model: model,
        messages: [
          {
            role: 'system',
            content: mistralSystemPrompt
          },
          {
            role: 'user',
            content: message
          }
        ],
        temperature: 0.7,
        max_tokens: 500,
        stream: false,
        // Balanced parameters
        top_p: 0.9,
        frequency_penalty: 0.1,
        presence_penalty: 0.1
      };
    default:
      throw new Error(`Unsupported model: ${model}`);
  }
}
// Parse response based on model type
function parseModelResponse(model, response) {
  console.log(`=== PARSING RESPONSE FOR ${model.toUpperCase()} ===`);
  // Simple, direct extraction
  let content = '';
  if (response && response.choices && response.choices.length > 0) {
    const choice = response.choices[0];
    if (choice.message && typeof choice.message.content === 'string') {
      content = choice.message.content;
      if (content.length === 0) {
        console.log('WARNING: Content is empty string!');
        content = choice.text || choice.message.text || '';
      }
    }
  }
  // If still empty, try other response formats
  if (!content) {
    console.log('Trying fallback response formats...');
    content = response.response || response.generated_text || response.content || response.text || '';
  }
  console.log('Raw content before cleaning:', content?.substring(0, 200));
  // Clean up the response (remove thinking tags, etc.)
  if (content && content.length > 0) {
    content = cleanModelResponse(content);
    console.log('Cleaned content:', content?.substring(0, 200));
  }
  // Return something meaningful even if empty
  if (!content || content.trim().length === 0) {
    return `Model returned empty response. Raw response: ${JSON.stringify(response).substring(0, 200)}...`;
  }
  return content;
}
// Clean up model response by removing thinking tags and fixing formatting
function cleanModelResponse(content) {
  if (!content || typeof content !== 'string') {
    return '';
  }
  let cleaned = content;
  // Remove <think> </think> tags and their content
  cleaned = cleaned.replace(/<think>[\s\S]*?<\/think>/gi, '');
  // Remove any other XML-style reasoning tags
  cleaned = cleaned.replace(/<thinking>[\s\S]*?<\/thinking>/gi, '');
  cleaned = cleaned.replace(/<reason>[\s\S]*?<\/reason>/gi, '');
  cleaned = cleaned.replace(/<analysis>[\s\S]*?<\/analysis>/gi, '');
  // More aggressive fix for inline numbered lists
  // Find any occurrence of number followed by dot and make sure it starts on a new line
  cleaned = cleaned.replace(/(\S)\s+(\d+\.\s+)/g, '$1\n\n$2');
  // Handle cases where there's punctuation before the number
  cleaned = cleaned.replace(/([.!?:;])\s*(\d+\.\s+)/g, '$1\n\n$2');
  // Handle cases where text flows directly into numbers
  cleaned = cleaned.replace(/([a-zA-Z])\s*(\d+\.\s+)/g, '$1\n\n$2');
  // Clean up excessive newlines but preserve intentional spacing
  cleaned = cleaned.replace(/\n{4,}/g, '\n\n\n');
  cleaned = cleaned.replace(/\n{3}/g, '\n\n');
  // Ensure there's space before numbered items if they don't have it
  cleaned = cleaned.replace(/([^\n])\n(\d+\.\s+)/g, '$1\n\n$2');
  cleaned = cleaned.trim();
  console.log('Cleaned content preview:', cleaned.substring(0, 300));
  return cleaned;
}
// Log conversation to Supabase (optional)
async function logConversation(supabase, conversationId, model, userMessage, aiResponse) {
  try {
    const { error } = await supabase.from('chat_logs').insert({
      conversation_id: conversationId,
      model: model,
      user_message: userMessage,
      ai_response: aiResponse,
      timestamp: new Date().toISOString()
    });
    if (error) {
      console.error('Error logging conversation:', error);
    }
  } catch (error) {
    console.error('Error in logConversation:', error);
  }
}
// Rate limiting function (optional)
async function checkRateLimit(supabase, userId) {
  try {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    const { data, error } = await supabase.from('rate_limits').select('count').eq('user_id', userId).gte('created_at', oneHourAgo).single();
    if (error && error.code !== 'PGRST116') {
      console.error('Rate limit check error:', error);
      return true; // Allow on error
    }
    const requestCount = data?.count || 0;
    const maxRequestsPerHour = 100; // Adjust as needed
    return requestCount < maxRequestsPerHour;
  } catch (error) {
    console.error('Rate limit error:', error);
    return true; // Allow on error
  }
}
serve(async (req)=>{
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  try {
    // Initialize Supabase client
    const supabase = createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '');
    // Parse request
    const { model, message, conversation_id, temperature, max_tokens } = await req.json();
    // Validate input
    if (!model || !message) {
      return new Response(JSON.stringify({
        error: 'Model and message are required'
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Check if model is supported
    if (!MODEL_CONFIGS[model]) {
      return new Response(JSON.stringify({
        error: `Unsupported model: ${model}`
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Optional: Rate limiting
    const authHeader = req.headers.get('authorization');
    if (authHeader) {
      const token = authHeader.replace('Bearer ', '');
      const { data: { user } } = await supabase.auth.getUser(token);
      if (user) {
        const canProceed = await checkRateLimit(supabase, user.id);
        if (!canProceed) {
          return new Response(JSON.stringify({
            error: 'Rate limit exceeded. Please try again later.'
          }), {
            status: 429,
            headers: {
              ...corsHeaders,
              'Content-Type': 'application/json'
            }
          });
        }
      }
    }
    const config = MODEL_CONFIGS[model];
    const requestBody = formatRequestForModel(model, message, {
      temperature,
      max_tokens
    });
    // Make request to tunnel
    console.log(`Making request to ${config.tunnel_url}${config.endpoint} for model ${model}`);
    console.log(`Request body:`, JSON.stringify(requestBody, null, 2));
    const response = await fetch(`${config.tunnel_url}${config.endpoint}`, {
      method: 'POST',
      headers: config.headers,
      body: JSON.stringify(requestBody)
    });
    console.log(`Response status: ${response.status} ${response.statusText}`);
    console.log(`Response headers:`, Object.fromEntries(response.headers.entries()));
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Model API error (${response.status}):`, errorText);
      return new Response(JSON.stringify({
        error: `Model API error: ${response.status} ${response.statusText}`,
        details: errorText
      }), {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    const modelResponse = await response.json();
    console.log(`Raw model response:`, JSON.stringify(modelResponse, null, 2));
    const aiResponse = parseModelResponse(model, modelResponse);
    console.log(`Parsed AI response:`, aiResponse);
    // Optional: Log the conversation
    if (conversation_id) {
      await logConversation(supabase, conversation_id, model, message, aiResponse);
    }
    // Return response
    const result = {
      response: aiResponse
    };
    return new Response(JSON.stringify(result), {
      status: 200,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Edge function error:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: error.message
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
}); /* 
To deploy this function:

1. Create the function:
   supabase functions new chat-proxy

2. Replace the content of supabase/functions/chat-proxy/index.ts with this code

3. Deploy:
   supabase functions deploy chat-proxy

4. Set environment variables:
   supabase secrets set SUPABASE_URL=your_supabase_url
   supabase secrets set SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

5. Optional: Create database tables for logging and rate limiting:

-- Chat logs table
create table chat_logs (
  id uuid default gen_random_uuid() primary key,
  conversation_id text not null,
  model text not null,
  user_message text not null,
  ai_response text not null,
  timestamp timestamptz default now()
);

-- Rate limiting table
create table rate_limits (
  id uuid default gen_random_uuid() primary key,
  user_id uuid references auth.users(id),
  count integer default 1,
  created_at timestamptz default now()
);

-- Enable RLS
alter table chat_logs enable row level security;
alter table rate_limits enable row level security;
*/ 
