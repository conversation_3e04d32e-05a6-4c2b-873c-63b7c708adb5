import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter/services.dart' show rootBundle;

class PrivacyPolicyDialog extends StatefulWidget {
  const PrivacyPolicyDialog({super.key});

  @override
  State<PrivacyPolicyDialog> createState() => _PrivacyPolicyDialogState();
}

class _PrivacyPolicyDialogState extends State<PrivacyPolicyDialog> {
  String? _html;

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    final html = await rootBundle.loadString('assets/privacy_policy.html');
    if (mounted) setState(() => _html = html);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 800, maxHeight: 600),
        child: Column(
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                gradient: LinearGradient(colors: [Color(0xFF059669), Color(0xFF047857)]),
                borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('Arion Networks Data Privacy Policy', style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.w700)),
                  IconButton(onPressed: () => Navigator.of(context).pop(), icon: const Icon(Icons.close, color: Colors.white))
                ],
              ),
            ),
            Expanded(
              child: _html == null
                  ? const Center(child: CircularProgressIndicator())
                  : SingleChildScrollView(child: Padding(padding: const EdgeInsets.all(16), child: Html(data: _html!))),
            ),
            Padding(
              padding: const EdgeInsets.all(12),
              child: ElevatedButton(onPressed: () => Navigator.of(context).pop(), child: const Text('I Understand')),
            )
          ],
        ),
      ),
    );
  }
}


