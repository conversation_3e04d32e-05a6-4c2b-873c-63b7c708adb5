import 'dart:convert';
import 'package:flutter/services.dart' show rootBundle;

class ConfigService {
  Map<String, dynamic>? _config;

  Future<void> load() async {
    if (_config != null) return;
    final jsonStr = await rootBundle.loadString('assets/config.json');
    _config = json.decode(jsonStr) as Map<String, dynamic>;
  }

  String get apiBaseUrl => _config?['api']['baseUrl'] as String? ?? '';
  String get apiEndpoint => _config?['api']['endpoint'] as String? ?? '/quick-processor';
  String? get supabaseAnonKey => _config?['supabase']['anonKey'] as String?;

  Map<String, dynamic> get actions => Map<String, dynamic>.from(_config?['api']['actions'] ?? {});
}


