<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ArionNetworks Admin - Registration Management</title>
  <style>
    :root {
      --brand-primary: #059669;
      --brand-secondary: #047857;
      --neutral-50: #f8fafc;
      --neutral-100: #f1f5f9;
      --neutral-200: #e2e8f0;
      --neutral-600: #475569;
      --neutral-700: #334155;
      --error: #ef4444;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--neutral-50);
      color: var(--neutral-700);
    }

    /* LOGIN STYLES */
    .login-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 2rem;
    }

    .login-card {
      background: white;
      padding: 2rem;
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(0,0,0,0.1);
      width: 100%;
      max-width: 400px;
    }

    .login-card h2 {
      text-align: center;
      color: var(--brand-primary);
      margin-bottom: 2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    .form-group label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 600;
    }

    .form-group input {
      width: 100%;
      padding: 0.75rem;
      border: 2px solid var(--neutral-200);
      border-radius: 8px;
      font-size: 1rem;
      box-sizing: border-box;
    }

    .form-group input:focus {
      outline: none;
      border-color: var(--brand-primary);
    }

    /* DASHBOARD STYLES */
    .dashboard-container {
      padding: 2rem;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2rem;
      background: white;
      padding: 1.5rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .header h1 {
      margin: 0;
      color: var(--brand-primary);
      font-size: 1.5rem;
    }

    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 8px;
      background: var(--brand-primary);
      color: white;
      cursor: pointer;
      font-weight: 600;
      margin-right: 0.5rem;
      transition: all 0.2s ease;
      font-size: 0.875rem;
    }

    .btn:hover {
      background: var(--brand-secondary);
      transform: translateY(-1px);
    }

    .btn-secondary {
      background: var(--neutral-200);
      color: var(--neutral-700);
    }

    .btn-secondary:hover {
      background: #cbd5e0;
    }

    .btn-warning {
      background: #f59e0b;
    }

    .btn-warning:hover {
      background: #d97706;
    }

    .stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .stat-card {
      background: white;
      padding: 1.5rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      text-align: center;
    }

    .stat-number {
      font-size: 2rem;
      font-weight: bold;
      color: var(--brand-primary);
      display: block;
    }

    .stat-label {
      color: var(--neutral-600);
      margin-top: 0.5rem;
    }

    .controls {
      display: flex;
      gap: 1rem;
      margin-bottom: 2rem;
      flex-wrap: wrap;
      align-items: center;
      background: white;
      padding: 1rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .controls select {
      padding: 0.5rem;
      border: 2px solid var(--neutral-200);
      border-radius: 6px;
      font-size: 0.875rem;
      background: white;
    }

    .leads-table {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th, td {
      padding: 1rem;
      text-align: left;
      border-bottom: 1px solid #eee;
    }

    th {
      background: var(--neutral-50);
      font-weight: 600;
      color: var(--neutral-700);
      font-size: 0.875rem;
    }

    tr:hover {
      background: var(--neutral-50);
    }

    .product-badge {
      display: inline-block;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
    }

    .product-arioncomply { background: #e3f2fd; color: #1976d2; }
    .product-arionsecure { background: #ffebee; color: #d32f2f; }
    .product-arionanalytics { background: #e8f5e8; color: #388e3c; }
    .product-arionplatform { background: #f3e5f5; color: #7b1fa2; }
    .product-default { background: #f5f5f5; color: #616161; }

    .status-pending { background: #fff3cd; color: #856404; }
    .status-approved { background: #d4edda; color: #155724; }
    .status-contacted { background: #d1ecf1; color: #0c5460; }

    .priority-high { background: #ff5252; color: white; }
    .priority-medium { background: #ff9800; color: white; }
    .priority-standard { background: #9e9e9e; color: white; }

    .loading {
      text-align: center;
      padding: 2rem;
      color: var(--neutral-600);
    }

    .error, .warning, .info {
      padding: 1rem;
      border-radius: 8px;
      margin-bottom: 1rem;
    }

    .error {
      color: var(--error);
      background: #fef2f2;
      border-left: 4px solid var(--error);
    }

    .warning {
      color: #92400e;
      background: #fffbeb;
      border-left: 4px solid #f59e0b;
    }

    .info {
      color: #1e40af;
      background: #eff6ff;
      border-left: 4px solid #3b82f6;
    }

    .debug-info {
      background: var(--neutral-100);
      padding: 1rem;
      border-radius: 4px;
      font-family: monospace;
      font-size: 0.75rem;
      margin-top: 0.5rem;
      border: 1px solid var(--neutral-200);
      max-height: 200px;
      overflow-y: auto;
    }

    /* RESPONSIVE */
    @media (max-width: 768px) {
      .login-container {
        padding: 1rem;
      }

      .dashboard-container {
        padding: 1rem;
      }

      .header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
      }

      .header > div {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
      }

      .btn {
        margin-right: 0;
        flex: 1;
        text-align: center;
      }

      .controls {
        flex-direction: column;
        align-items: stretch;
      }

      .controls > * {
        width: 100%;
      }

      table {
        font-size: 0.875rem;
      }

      th, td {
        padding: 0.5rem;
      }
    }
  </style>
</head>
<body>
  <!-- LOGIN SECTION -->
  <div id="loginSection" class="login-container">
    <div class="login-card">
      <h2>🔒 Admin Access</h2>
      <form id="loginForm">
        <div class="form-group">
          <label for="adminPassword">Admin Password:</label>
          <input type="password" id="adminPassword" required autocomplete="current-password" placeholder="Enter admin password">
        </div>
        <button type="submit" class="btn" style="width: 100%;">Login to Dashboard</button>
      </form>
      <div id="loginError" class="error" style="display: none;"></div>
    </div>
  </div>

  <!-- DASHBOARD SECTION -->
  <div id="dashboardSection" class="dashboard-container" style="display: none;">
    <div class="container">
      <div class="header">
        <h1>🚀 ArionNetworks Registration Management</h1>
        <div>
          <button class="btn" onclick="refreshData()">🔄 Refresh</button>
          <button class="btn" onclick="exportCSV()">📊 Export CSV</button>
          <button class="btn" onclick="exportJSON()">💾 Export JSON</button>
          <button class="btn btn-warning" onclick="debugConnection()">🔧 Debug</button>
          <button class="btn btn-secondary" onclick="logout()">Logout</button>
        </div>
      </div>

      <div class="stats">
        <div class="stat-card">
          <div class="stat-number" id="totalLeads">-</div>
          <div class="stat-label">Total Registrations</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="approvedLeads">-</div>
          <div class="stat-label">Approved</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="pendingLeads">-</div>
          <div class="stat-label">Pending</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="todayLeads">-</div>
          <div class="stat-label">Today's Registrations</div>
        </div>
      </div>

      <div class="controls">
        <select id="filter-product">
          <option value="">All Products</option>
          <option value="arioncomply">ArionComply</option>
          <option value="arionsecure">ArionSecure</option>
          <option value="arionanalytics">ArionAnalytics</option>
          <option value="arionplatform">ArionPlatform</option>
          <option value="default">Default</option>
        </select>

        <select id="filter-program">
          <option value="">All Programs</option>
          <option value="pilot">Pilot</option>
          <option value="waitlist">Waitlist</option>
          <option value="beta">Beta</option>
          <option value="early_access">Early Access</option>
        </select>

        <select id="filter-status">
          <option value="">All Status</option>
          <option value="pending">Pending</option>
          <option value="approved">Approved</option>
          <option value="contacted">Contacted</option>
          <option value="onboarded">Onboarded</option>
        </select>
      </div>

      <div id="messageDisplay" style="display: none;"></div>

      <div class="leads-table">
        <div class="loading" id="loading">Loading registration data...</div>
        <table id="leadsTable" style="display: none;">
          <thead>
            <tr>
              <th>Contact</th>
              <th>Company</th>
              <th>Product</th>
              <th>Program</th>
              <th>Priority</th>
              <th>Timeline</th>
              <th>Use Case</th>
              <th>Status</th>
              <th>Registered</th>
            </tr>
          </thead>
          <tbody id="leadsTableBody">
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Include Supabase JS -->
  <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

  <script>
    // Configuration - Using service role for admin access (like your edge function)
    // You need to set these environment variables or replace with actual values
    const SUPABASE_URL = 'https://dxncozbhwppvwpugoqjk.supabase.co';

    // For admin dashboard, we need the SERVICE ROLE key, not anon key
    // Get this from Supabase Dashboard → Settings → API → service_role key
    const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR4bmNvemJod3BwdndwdWdvcWprIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MzE5ODEwNCwiZXhwIjoyMDY4Nzc0MTA0fQ.kZlfXCNAqlbZ8O2AXlfhTZ3TW7UAsxEoqGRD6PQJIQQ'; // This bypasses RLS

    // Fallback anon key for testing
    const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR4bmNvemJod3BwdndwdWdvcWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjI4NzMyOTEsImV4cCI6MjAzODQ0OTI5MX0.lnlHjbqA-vqE4KeYu6FfNb4mT4WGlSUBjdpFNhb5pKI';

    // Simple admin password (in production, use proper authentication)
    const ADMIN_PASSWORD = 'ArionAdmin2024!';

    let supabaseClient = null;
    let isAdminAuthenticated = false;
    let allLeads = [];
    let filteredLeads = [];

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize Supabase with service role key for admin access
      if (window.supabase) {
        // Try service role key first (for admin functions)
        if (SUPABASE_SERVICE_KEY && SUPABASE_SERVICE_KEY !== 'YOUR_SERVICE_ROLE_KEY_HERE') {
          supabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
          console.log('Supabase client initialized with SERVICE ROLE key');
        } else {
          // Fallback to anon key (will need RLS policies)
          supabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
          console.log('Supabase client initialized with ANON key (fallback)');
        }
      }

      // Check for existing session
      checkAdminSession();

      // Set up event listeners
      setupEventListeners();
    });

    function setupEventListeners() {
      // Login form
      document.getElementById('loginForm').addEventListener('submit', handleLogin);

      // Filter listeners
      document.getElementById('filter-product').addEventListener('change', applyFilters);
      document.getElementById('filter-program').addEventListener('change', applyFilters);
      document.getElementById('filter-status').addEventListener('change', applyFilters);
    }

    function checkAdminSession() {
      const isAuthenticated = localStorage.getItem('arion-admin-authenticated');
      if (isAuthenticated === 'true') {
        showDashboard();
      } else {
        showLogin();
      }
    }

    async function handleLogin(e) {
      e.preventDefault();
      const password = document.getElementById('adminPassword').value;

      if (!password) {
        showLoginError('Please enter the admin password');
        return;
      }

      // Simple password check (in production, use proper authentication)
      if (password === ADMIN_PASSWORD) {
        localStorage.setItem('arion-admin-authenticated', 'true');
        isAdminAuthenticated = true;
        showDashboard();
      } else {
        showLoginError('Invalid admin password');
      }
    }

    function showLogin() {
      document.getElementById('loginSection').style.display = 'flex';
      document.getElementById('dashboardSection').style.display = 'none';
      document.getElementById('loginError').style.display = 'none';
      document.getElementById('adminPassword').value = '';
    }

    function showDashboard() {
      document.getElementById('loginSection').style.display = 'none';
      document.getElementById('dashboardSection').style.display = 'block';
      isAdminAuthenticated = true;
      loadData();
    }

    function logout() {
      localStorage.removeItem('arion-admin-authenticated');
      isAdminAuthenticated = false;
      showLogin();
    }

    function showLoginError(message) {
      const errorDiv = document.getElementById('loginError');
      errorDiv.textContent = message;
      errorDiv.style.display = 'block';
      setTimeout(() => errorDiv.style.display = 'none', 5000);
    }

    function showMessage(type, message, details = null) {
      const messageDiv = document.getElementById('messageDisplay');
      let className = type; // 'error', 'warning', 'info'

      let content = `<div class="${className}">
        <strong>${type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️'} ${type.toUpperCase()}:</strong><br>
        ${message}`;

      if (details) {
        content += `<div class="debug-info">${details}</div>`;
      }

      content += '</div>';
      messageDiv.innerHTML = content;
      messageDiv.style.display = 'block';
    }

    function hideMessage() {
      document.getElementById('messageDisplay').style.display = 'none';
    }

    async function debugConnection() {
      console.log('🔧 Running comprehensive connection debug...');
      showMessage('info', 'Running diagnostics...', 'Check console for detailed output');

      try {
        // Test 1: Basic configuration
        console.log('=== Test 1: Configuration ===');
        console.log('- URL:', SUPABASE_URL);
        console.log('- Key length:', SUPABASE_ANON_KEY.length);
        console.log('- Key starts with:', SUPABASE_ANON_KEY.substring(0, 20) + '...');
        console.log('- Client initialized:', !!supabaseClient);

        // Test 2: Direct fetch to test API key validity
        console.log('\n=== Test 2: API Key Validation ===');

        // Determine which key to use
        const keyToTest = (SUPABASE_SERVICE_KEY && SUPABASE_SERVICE_KEY !== 'YOUR_SERVICE_ROLE_KEY_HERE')
          ? SUPABASE_SERVICE_KEY
          : SUPABASE_ANON_KEY;

        const keyType = (keyToTest === SUPABASE_SERVICE_KEY) ? 'SERVICE_ROLE' : 'ANON';
        console.log('- Testing key type:', keyType);
        console.log('- Key length:', keyToTest.length);

        try {
          const directResponse = await fetch(`${SUPABASE_URL}/rest/v1/product_registrations?select=count&head=true`, {
            headers: {
              'apikey': keyToTest,
              'Authorization': `Bearer ${keyToTest}`,
              'Content-Type': 'application/json',
              'Prefer': 'count=exact'
            }
          });

          console.log('- API response status:', directResponse.status);
          console.log('- API response ok:', directResponse.ok);

          if (!directResponse.ok) {
            const errorText = await directResponse.text();
            console.error('- API error:', errorText);

            if (directResponse.status === 401) {
              if (keyType === 'ANON') {
                showMessage('error', 'Need SERVICE ROLE key for admin access',
                  `The anon key cannot access registration data due to RLS policies.<br><br>
                  <strong>Solution:</strong><br>
                  1. Go to Supabase Dashboard → Settings → API<br>
                  2. Copy the <strong>service_role</strong> key (not anon)<br>
                  3. Replace SUPABASE_SERVICE_KEY in the code<br><br>
                  <strong>OR run this SQL to allow anon access:</strong><br>
                  <code>CREATE POLICY "admin_read_access" ON public.product_registrations FOR SELECT USING (true);</code>`
                );
              } else {
                showMessage('error', 'Service role key is invalid',
                  `Status: ${directResponse.status}<br>Response: ${errorText}<br><br>
                  Go to Supabase Dashboard → Settings → API and copy a fresh service_role key.`
                );
              }
            } else {
              showMessage('error', `API Error (${directResponse.status})`, errorText);
            }
            return;
          }

          const count = directResponse.headers.get('Content-Range');
          console.log('- Record count from header:', count);
          console.log('- API test: ✅ Key is valid and has access');
        } catch (fetchError) {
          console.error('- API fetch failed:', fetchError);
          showMessage('error', 'Network or API error', fetchError.message);
          return;
        }

        // Test 3: Test table access
        console.log('\n=== Test 3: Table Access Test ===');
        try {
          const tableResponse = await fetch(`${SUPABASE_URL}/rest/v1/product_registrations?select=count&head=true`, {
            headers: {
              'apikey': SUPABASE_ANON_KEY,
              'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
              'Content-Type': 'application/json',
              'Prefer': 'count=exact'
            }
          });

          console.log('- Table access status:', tableResponse.status);
          console.log('- Table access ok:', tableResponse.ok);

          if (!tableResponse.ok) {
            const errorText = await tableResponse.text();
            console.error('- Table access error:', errorText);

            if (tableResponse.status === 401) {
              showMessage('error', 'Unauthorized - RLS Policy Issue',
                `The API key is valid but RLS policies are blocking access.<br><br>
                <strong>Run this SQL in Supabase:</strong><br>
                <code>CREATE POLICY "admin_read_access" ON public.product_registrations FOR SELECT USING (true);<br>
                GRANT SELECT ON public.product_registrations TO anon;</code>`
              );
            } else if (tableResponse.status === 404) {
              showMessage('error', 'Table not found',
                'The product_registrations table does not exist. Run the database setup script.'
              );
            } else {
              showMessage('error', 'Table access failed', `Status: ${tableResponse.status}<br>${errorText}`);
            }
            return;
          }

          const count = tableResponse.headers.get('Content-Range');
          console.log('- Table count header:', count);
          console.log('- Table access: ✅ Success');

        } catch (tableError) {
          console.error('- Table access test failed:', tableError);
          showMessage('error', 'Table access test failed', tableError.message);
          return;
        }

        // Test 4: Supabase client test
        console.log('\n=== Test 4: Supabase Client Test ===');
        const { data, error, count } = await supabaseClient
          .from('product_registrations')
          .select('id', { count: 'exact', head: true });

        if (error) {
          console.error('- Supabase client error:', error);
          showMessage('error', 'Supabase client error',
            `${error.message}<br>Code: ${error.code}<br>Details: ${error.details || 'None'}`
          );
          return;
        }

        console.log('- Supabase client: ✅ Success');
        console.log('- Record count:', count);

        // Final result
        if (count === 0) {
          showMessage('warning', 'Everything works but no data found',
            `Connection is working perfectly, but the table is empty.<br>
            Total records: ${count}<br><br>
            This means no registrations have been submitted yet.`
          );
        } else {
          showMessage('info', '🎉 Everything is working perfectly!',
            `Found ${count} registration records.<br>
            All tests passed - the dashboard should work now.`
          );
        }

      } catch (error) {
        console.error('Debug process failed:', error);
        showMessage('error', 'Debug process failed', error.message);
      }
    }

    async function loadData() {
      if (!supabaseClient) {
        showMessage('error', 'Supabase client not initialized');
        return;
      }

      try {
        showLoading(true);
        hideMessage();
        console.log('Loading registration data...');

        const { data: leads, error } = await supabaseClient
          .from('product_registrations')
          .select(`
            id, full_name, email, company, job_title, phone,
            product_id, program_type, primary_business, company_size,
            timeline, use_case, source_url, utm_source, utm_campaign,
            status, created_at, updated_at
          `)
          .order('created_at', { ascending: false });

        if (error) {
          console.error('Supabase error:', error);

          if (error.code === 'PGRST116') {
            throw new Error('Table "product_registrations" not found. Please run the database setup script.');
          } else if (error.message.includes('permission denied') || error.code === '42501') {
            throw new Error('Permission denied. Please run the RLS policy fix SQL.');
          } else {
            throw new Error(`Database error: ${error.message}`);
          }
        }

        console.log(`Successfully loaded ${leads ? leads.length : 0} registrations`);

        if (!leads || leads.length === 0) {
          showEmptyState();
          updateStats([]);
          showLoading(false);
          return;
        }

        allLeads = leads.map(lead => ({
          ...lead,
          priority: calculatePriority(lead.company_size, lead.timeline)
        }));

        updateStats(allLeads);
        applyFilters();
        showLoading(false);

      } catch (error) {
        console.error('Load data error:', error);
        showMessage('error', error.message);
        showLoading(false);
      }
    }

    function showEmptyState() {
      const tbody = document.getElementById('leadsTableBody');
      tbody.innerHTML = `
        <tr>
          <td colspan="9" style="text-align: center; padding: 40px;">
            <div style="font-size: 48px; margin-bottom: 20px;">📭</div>
            <strong>No registration data found</strong><br>
            <div style="margin-top: 15px; color: #666;">
              <p>This could mean:</p>
              <ul style="text-align: left; display: inline-block; margin-top: 10px;">
                <li>No registrations yet</li>
                <li>Database table is empty</li>
                <li>Permission issues</li>
              </ul>
            </div>
          </td>
        </tr>
      `;
      document.getElementById('leadsTable').style.display = 'table';
    }

    function calculatePriority(companySize, timeline) {
      if (!companySize || !timeline) return 'standard';

      const largeSizes = ['1001-5000', '5000+'];
      const urgentTimelines = ['Immediately', '1-3 months'];
      const mediumSizes = ['201-1000', '1001-5000'];
      const mediumTimelines = ['1-3 months', '3-6 months'];

      if (largeSizes.includes(companySize) && urgentTimelines.includes(timeline)) {
        return 'high';
      } else if (mediumSizes.includes(companySize) && mediumTimelines.includes(timeline)) {
        return 'medium';
      }
      return 'standard';
    }

    function updateStats(leads) {
      const total = leads.length;
      const approved = leads.filter(l => ['approved', 'contacted', 'onboarded'].includes(l.status)).length;
      const pending = leads.filter(l => l.status === 'pending').length;
      const today = leads.filter(l => {
        const today = new Date();
        const leadDate = new Date(l.created_at);
        return leadDate.toDateString() === today.toDateString();
      }).length;

      document.getElementById('totalLeads').textContent = total;
      document.getElementById('approvedLeads').textContent = approved;
      document.getElementById('pendingLeads').textContent = pending;
      document.getElementById('todayLeads').textContent = today;
    }

    function applyFilters() {
      const productFilter = document.getElementById('filter-product').value;
      const programFilter = document.getElementById('filter-program').value;
      const statusFilter = document.getElementById('filter-status').value;

      filteredLeads = allLeads.filter(lead => {
        return (!productFilter || lead.product_id === productFilter) &&
               (!programFilter || lead.program_type === programFilter) &&
               (!statusFilter || lead.status === statusFilter);
      });

      renderTable(filteredLeads);
    }

    function renderTable(leads) {
      const tbody = document.getElementById('leadsTableBody');
      tbody.innerHTML = '';

      if (leads.length === 0) {
        tbody.innerHTML = '<tr><td colspan="9" style="text-align: center; padding: 40px; color: #666;">No registrations found with current filters.</td></tr>';
        return;
      }

      leads.forEach(lead => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>
            <strong>${lead.full_name || 'N/A'}</strong><br>
            <small style="color: #666;">${lead.email || 'N/A'}</small><br>
            <small style="color: #888;">${lead.job_title || 'N/A'}</small>
          </td>
          <td>
            <strong>${lead.company || 'N/A'}</strong><br>
            <small style="color: #666;">${lead.primary_business || 'N/A'}</small><br>
            <small style="color: #888;">${lead.company_size || 'N/A'} employees</small>
          </td>
          <td>
            <span class="product-badge product-${lead.product_id}">${getProductName(lead.product_id)}</span>
          </td>
          <td>
            <span class="product-badge product-${lead.product_id}">${getProgramName(lead.program_type)}</span>
          </td>
          <td>
            <span class="product-badge priority-${lead.priority}">${lead.priority.toUpperCase()}</span>
          </td>
          <td>${lead.timeline || 'Not specified'}</td>
          <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;" title="${lead.use_case || ''}">
            ${(lead.use_case || 'N/A').substring(0, 100)}${lead.use_case && lead.use_case.length > 100 ? '...' : ''}
          </td>
          <td>
            <span class="product-badge status-${lead.status || 'pending'}">${(lead.status || 'pending').toUpperCase()}</span>
          </td>
          <td>${formatDate(lead.created_at)}</td>
        `;
        tbody.appendChild(row);
      });
    }

    function showLoading(show) {
      document.getElementById('loading').style.display = show ? 'block' : 'none';
      document.getElementById('leadsTable').style.display = show ? 'none' : 'table';
    }

    function getProductName(productId) {
      const names = {
        'arioncomply': 'ArionComply',
        'arionsecure': 'ArionSecure',
        'arionanalytics': 'ArionAnalytics',
        'arionplatform': 'ArionPlatform',
        'default': 'ArionNetworks'
      };
      return names[productId] || productId;
    }

    function getProgramName(programType) {
      const names = {
        'pilot': 'Pilot',
        'waitlist': 'Waitlist',
        'beta': 'Beta',
        'early_access': 'Early Access'
      };
      return names[programType] || programType;
    }

    function formatDate(dateString) {
      if (!dateString) return 'N/A';
      const date = new Date(dateString);
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    }

    // Export functions
    function exportCSV() {
      if (filteredLeads.length === 0) {
        alert('No data to export. Please check your filters or add registration data.');
        return;
      }

      const headers = [
        'ID', 'Full Name', 'Email', 'Company', 'Job Title', 'Phone',
        'Product', 'Program', 'Industry', 'Company Size', 'Timeline', 'Priority',
        'Use Case', 'Source URL', 'UTM Source', 'UTM Campaign', 'Status', 'Created At'
      ];

      const csvContent = [
        headers.join(','),
        ...filteredLeads.map(lead => [
          `"${lead.id || ''}"`,
          `"${lead.full_name || ''}"`,
          `"${lead.email || ''}"`,
          `"${lead.company || ''}"`,
          `"${lead.job_title || ''}"`,
          `"${lead.phone || ''}"`,
          getProductName(lead.product_id),
          getProgramName(lead.program_type),
          `"${lead.primary_business || ''}"`,
          `"${lead.company_size || ''}"`,
          `"${lead.timeline || ''}"`,
          lead.priority || '',
          `"${(lead.use_case || '').replace(/"/g, '""')}"`,
          `"${lead.source_url || ''}"`,
          `"${lead.utm_source || ''}"`,
          `"${lead.utm_campaign || ''}"`,
          lead.status || '',
          formatDate(lead.created_at)
        ].join(','))
      ].join('\n');

      downloadFile(csvContent, `arion-registrations-${new Date().toISOString().split('T')[0]}.csv`, 'text/csv');
      console.log(`CSV exported: ${filteredLeads.length} records`);
    }

    function exportJSON() {
      if (filteredLeads.length === 0) {
        alert('No data to export. Please check your filters or add registration data.');
        return;
      }

      const exportData = {
        exported_at: new Date().toISOString(),
        total_records: filteredLeads.length,
        filters_applied: {
          product: document.getElementById('filter-product').value || 'All',
          program: document.getElementById('filter-program').value || 'All',
          status: document.getElementById('filter-status').value || 'All'
        },
        registrations: filteredLeads
      };

      const jsonContent = JSON.stringify(exportData, null, 2);
      downloadFile(jsonContent, `arion-registrations-${new Date().toISOString().split('T')[0]}.json`, 'application/json');
      console.log(`JSON exported: ${filteredLeads.length} records`);
    }

    function downloadFile(content, filename, contentType) {
      const blob = new Blob([content], { type: contentType + ';charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }

    function refreshData() {
      loadData();
    }
  </script>
</body>
</html>
