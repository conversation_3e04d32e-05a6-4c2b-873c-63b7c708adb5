import '../../core/network/api_client.dart';
import '../../core/services/config_service.dart';
import '../../core/services/session_service.dart';
import '../../domain/repositories/auth_repository.dart' as domain;

class AuthRepositoryImpl implements domain.AuthRepository {
  final ApiClient _api;
  final ConfigService _config;
  final SessionService _session;

  AuthRepositoryImpl(this._api, this._config, this._session);

  @override
  Future<Map<String, dynamic>> authenticate(
    String email,
    String password,
  ) async {
    await _config.load();
    final action = _config.actions['authenticateUser'] ?? 'authenticate-user';
    final res = await _api.post(action, {'email': email, 'password': password});
    final map = Map<String, dynamic>.from(res.data ?? {});
    final sid = (map['sessionId'] ?? map['data']?['sessionId']) as String?;
    if (sid != null && sid.isNotEmpty) {
      await _session.saveSessionId(sid);
    }
    return map;
  }

  @override
  Future<Map<String, dynamic>> verifyEmail(String token) async {
    await _config.load();
    final action = _config.actions['verifyEmail'] ?? 'verify-email';
    final res = await _api.post(action, {'token': token});
    final map = Map<String, dynamic>.from(res.data ?? {});
    final sid = (map['sessionId'] ?? map['data']?['sessionId']) as String?;
    if (sid != null && sid.isNotEmpty) {
      await _session.saveSessionId(sid);
    }
    return map;
  }
}
