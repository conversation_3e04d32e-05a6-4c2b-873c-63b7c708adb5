import 'package:dio/dio.dart';
import '../../core/network/api_client.dart';
import '../../core/services/config_service.dart';
import '../../domain/repositories/admin_repository.dart' as domain;

class AdminRepositoryImpl implements domain.AdminRepository {
  final ApiClient _api;
  final ConfigService _config;
  AdminRepositoryImpl(this._api, this._config);

  @override
  Future<Map<String, dynamic>> requestCode({
    required String password,
    String? email,
  }) async {
    await _config.load();
    final action = _config.actions['adminRequestCode'] ?? 'admin-request-code';
    final Response res = await _api.post(action, {
      if (email != null) 'email': email,
      'password': password,
    });
    return Map<String, dynamic>.from(res.data ?? {});
  }

  @override
  Future<Map<String, dynamic>> verifyCode({required String code}) async {
    await _config.load();
    final action = _config.actions['adminVerifyCode'] ?? 'admin-verify-code';
    final Response res = await _api.post(action, {'code': code});
    return Map<String, dynamic>.from(res.data ?? {});
  }

  @override
  Future<Map<String, dynamic>> verifySession({required String token}) async {
    await _config.load();
    final action =
        _config.actions['verifyAdminSession'] ?? 'verify-admin-session';
    final Response res = await _api.post(action, {'token': token});
    return Map<String, dynamic>.from(res.data ?? {});
  }

  @override
  Future<Map<String, dynamic>> getUsers() async {
    await _config.load();
    final action = _config.actions['getUsers'] ?? 'get-users';
    final Response res = await _api.post(action, {});
    return Map<String, dynamic>.from(res.data ?? {});
  }

  @override
  Future<Map<String, dynamic>> getLeads() async {
    await _config.load();
    final Response res = await _api.post('get-leads', {});
    return Map<String, dynamic>.from(res.data ?? {});
  }

  @override
  Future<Map<String, dynamic>> adminManageUser({
    required String adminToken,
    required String userId,
    required String userAction,
    required String userType,
  }) async {
    await _config.load();
    final Response res = await _api.post('admin-manage-user', {
      'adminToken': adminToken,
      'userId': userId,
      'userAction': userAction,
      'userType': userType,
    });
    return Map<String, dynamic>.from(res.data ?? {});
  }

  @override
  Future<Map<String, dynamic>> cleanupExpiredData({int daysOld = 30}) async {
    await _config.load();
    final Response res = await _api.post('cleanup-expired-data', {
      'daysOld': daysOld,
    });
    return Map<String, dynamic>.from(res.data ?? {});
  }
}
