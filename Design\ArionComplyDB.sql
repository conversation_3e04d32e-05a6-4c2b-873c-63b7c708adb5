-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.admin_sessions (
  token uuid NOT NULL UNIQUE,
  expires_at timestamp with time zone NOT NULL,
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  created_at timestamp with time zone NOT NULL DEFAULT timezone('utc'::text, now()),
  is_active boolean NOT NULL DEFAULT true,
  CONSTRAINT admin_sessions_pkey PRIMARY KEY (id)
);
CREATE TABLE public.admin_verification_codes (
  code text NOT NULL,
  password_hash text NOT NULL,
  expires_at timestamp with time zone NOT NULL,
  used_at timestamp with time zone,
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  is_used boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT admin_verification_codes_pkey PRIMARY KEY (id)
);
CREATE TABLE public.contextual_prompts (
  id character varying NOT NULL,
  name character varying NOT NULL,
  description text NOT NULL,
  system_prompt text NOT NULL,
  response_guidelines ARRAY,
  forbidden_topics ARRAY,
  required_elements ARRAY,
  format_instructions text,
  example_responses ARRAY,
  created_by uuid,
  tone character varying DEFAULT 'professional'::character varying,
  max_tokens integer DEFAULT 800,
  temperature double precision DEFAULT 0.3,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  usage_count integer DEFAULT 0,
  avg_response_rating double precision DEFAULT 0.0,
  CONSTRAINT contextual_prompts_pkey PRIMARY KEY (id),
  CONSTRAINT contextual_prompts_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id)
);
CREATE TABLE public.demo_browsing_logs (
  session_id uuid NOT NULL,
  page_url text NOT NULL,
  page_title text,
  referrer_url text,
  time_on_page integer,
  scroll_depth numeric,
  document_id uuid,
  document_section text,
  event_type text,
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  clicks integer DEFAULT 0,
  metadata jsonb DEFAULT '{}'::jsonb,
  CONSTRAINT demo_browsing_logs_pkey PRIMARY KEY (id)
);
CREATE TABLE public.demo_contacts (
  session_id uuid NOT NULL,
  name text NOT NULL,
  email text NOT NULL,
  company text,
  phone text,
  message text,
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  created_at timestamp with time zone DEFAULT now(),
  application_type text DEFAULT 'contact'::text,
  application_data jsonb DEFAULT '{}'::jsonb,
  lead_score integer DEFAULT 0,
  lead_qualification text DEFAULT 'suspect'::text,
  CONSTRAINT demo_contacts_pkey PRIMARY KEY (id),
  CONSTRAINT demo_contacts_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.demo_sessions(id)
);
CREATE TABLE public.demo_documents (
  session_id uuid NOT NULL,
  doc_type text NOT NULL,
  title text NOT NULL,
  content text NOT NULL,
  description text,
  icon text,
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT demo_documents_pkey PRIMARY KEY (id),
  CONSTRAINT demo_documents_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.demo_sessions(id)
);
CREATE TABLE public.demo_embeddings (
  session_id uuid,
  content text NOT NULL,
  embedding USER-DEFINED,
  id bigint NOT NULL DEFAULT nextval('demo_embeddings_id_seq'::regclass),
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT demo_embeddings_pkey PRIMARY KEY (id)
);
CREATE TABLE public.demo_intake_data (
  session_id uuid NOT NULL,
  goal_type text NOT NULL,
  company_size text,
  industry text,
  current_compliance_level text,
  risk_level text,
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  responses jsonb NOT NULL DEFAULT '{}'::jsonb,
  priority_areas jsonb DEFAULT '[]'::jsonb,
  recommended_documents jsonb DEFAULT '[]'::jsonb,
  CONSTRAINT demo_intake_data_pkey PRIMARY KEY (id)
);
CREATE TABLE public.demo_interactions (
  session_id uuid NOT NULL,
  event_type text NOT NULL,
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  event_data jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT demo_interactions_pkey PRIMARY KEY (id),
  CONSTRAINT demo_interactions_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.demo_sessions(id)
);
CREATE TABLE public.demo_sessions (
  user_type text DEFAULT 'regular'::text CHECK (user_type = ANY (ARRAY['regular'::text, 'admin'::text])),
  admin_role text,
  detected_goal text,
  user_id uuid,
  user_email text,
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  metadata jsonb DEFAULT '{}'::jsonb,
  status text DEFAULT 'active'::text,
  last_interaction timestamp with time zone DEFAULT now(),
  interaction_count integer DEFAULT 0,
  preview_ready boolean DEFAULT false,
  document_count integer DEFAULT 0,
  intake_data jsonb DEFAULT '{}'::jsonb,
  contact_captured boolean DEFAULT false,
  registration_required boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT demo_sessions_pkey PRIMARY KEY (id),
  CONSTRAINT fk_demo_sessions_user FOREIGN KEY (user_id) REFERENCES public.demo_users(id)
);
CREATE TABLE public.demo_users (
  user_type text DEFAULT 'regular'::text CHECK (user_type = ANY (ARRAY['regular'::text, 'admin'::text])),
  admin_role text,
  admin_permissions ARRAY,
  last_login timestamp with time zone,
  temporary_password text,
  first_name text,
  last_name text,
  full_name text,
  email text NOT NULL UNIQUE,
  company text,
  job_title text,
  company_size text,
  compliance_needs text,
  verified_at timestamp with time zone,
  verification_token text,
  user_agent text,
  referrer text,
  demo_session_id uuid,
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  industry text DEFAULT 'Technology'::text,
  email_verified boolean DEFAULT false,
  agreed_to_terms boolean DEFAULT false,
  marketing_consent boolean DEFAULT false,
  registration_source text DEFAULT 'demo'::text,
  lead_score integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT demo_users_pkey PRIMARY KEY (id),
  CONSTRAINT demo_users_demo_session_id_fkey FOREIGN KEY (demo_session_id) REFERENCES public.demo_sessions(id)
);
CREATE TABLE public.demo_verification_requests (
  temporary_password text,
  email text NOT NULL,
  verification_token text NOT NULL UNIQUE,
  user_data jsonb NOT NULL,
  used_at timestamp with time zone,
  session_id uuid,
  expires_at timestamp with time zone NOT NULL,
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  is_used boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT demo_verification_requests_pkey PRIMARY KEY (id),
  CONSTRAINT demo_verification_requests_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.demo_sessions(id)
);
CREATE TABLE public.event_logs (
  event_type character varying NOT NULL,
  session_id character varying,
  user_id uuid,
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  event_data jsonb DEFAULT '{}'::jsonb,
  timestamp timestamp with time zone DEFAULT now(),
  CONSTRAINT event_logs_pkey PRIMARY KEY (id),
  CONSTRAINT event_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.faq_items (
  question text NOT NULL,
  answer text NOT NULL,
  category text NOT NULL,
  subcategory text,
  embedding USER-DEFINED,
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  helpful_votes integer DEFAULT 0,
  unhelpful_votes integer DEFAULT 0,
  status text DEFAULT 'published'::text CHECK (status = ANY (ARRAY['draft'::text, 'published'::text, 'archived'::text])),
  priority integer DEFAULT 0,
  metadata jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT faq_items_pkey PRIMARY KEY (id)
);
CREATE TABLE public.goal_analytics (
  date date NOT NULL,
  goal_type character varying NOT NULL,
  avg_confidence double precision,
  avg_response_time_ms double precision,
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  detection_count integer DEFAULT 0,
  successful_responses integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT goal_analytics_pkey PRIMARY KEY (id)
);
CREATE TABLE public.knowledge_base (
  title text NOT NULL,
  content text NOT NULL,
  summary text,
  category text NOT NULL,
  subcategory text,
  embedding USER-DEFINED,
  author text,
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  metadata jsonb DEFAULT '{}'::jsonb,
  status text DEFAULT 'published'::text CHECK (status = ANY (ARRAY['draft'::text, 'published'::text, 'archived'::text])),
  version integer DEFAULT 1,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT knowledge_base_pkey PRIMARY KEY (id)
);
CREATE TABLE public.knowledge_documents (
  title character varying NOT NULL,
  content text NOT NULL,
  category character varying,
  subcategory character varying,
  document_type character varying,
  source_url text,
  embedding USER-DEFINED,
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  is_active boolean DEFAULT true,
  metadata jsonb DEFAULT '{}'::jsonb,
  CONSTRAINT knowledge_documents_pkey PRIMARY KEY (id)
);
CREATE TABLE public.persistent_admin_sessions (
  token text NOT NULL,
  expires_at timestamp with time zone NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT persistent_admin_sessions_pkey PRIMARY KEY (token)
);
CREATE TABLE public.product_registrations (
  user_id uuid,
  source_url text,
  utm_source text,
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  product_id text NOT NULL DEFAULT 'default'::text CHECK (product_id = ANY (ARRAY['arioncomply'::text, 'arionsecure'::text, 'arionanalytics'::text, 'arionplatform'::text, 'default'::text])),
  program_type text NOT NULL DEFAULT 'pilot'::text CHECK (program_type = ANY (ARRAY['pilot'::text, 'waitlist'::text, 'beta'::text, 'early_access'::text])),
  utm_campaign text,
  full_name text NOT NULL CHECK (char_length(full_name) >= 2 AND char_length(full_name) <= 100),
  email text NOT NULL CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'::text),
  company text NOT NULL CHECK (char_length(company) >= 2 AND char_length(company) <= 100),
  job_title text NOT NULL CHECK (char_length(job_title) >= 2 AND char_length(job_title) <= 100),
  primary_business text NOT NULL CHECK (primary_business = ANY (ARRAY['Technology'::text, 'Financial Services'::text, 'Healthcare'::text, 'Manufacturing'::text, 'Government'::text, 'Education'::text, 'Retail'::text, 'Energy'::text, 'Telecommunications'::text, 'Other'::text])),
  company_size text NOT NULL CHECK (company_size = ANY (ARRAY['1-10'::text, '11-50'::text, '51-200'::text, '201-1000'::text, '1001-5000'::text, '5000+'::text])),
  phone text NOT NULL CHECK (char_length(phone) >= 10 AND char_length(phone) <= 20),
  use_case text NOT NULL CHECK (char_length(use_case) >= 20 AND char_length(use_case) <= 1000),
  timeline text CHECK (timeline IS NULL OR (timeline = ANY (ARRAY['Immediately'::text, '1-3 months'::text, '3-6 months'::text, '6-12 months'::text, '12+ months'::text, 'Just exploring'::text]))),
  email_verified_at timestamp with time zone,
  ip_address inet,
  user_agent text,
  assigned_to uuid,
  notes text,
  last_contacted timestamp with time zone,
  email_verified boolean DEFAULT false,
  submission_source text DEFAULT 'web'::text,
  created_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  updated_at timestamp with time zone DEFAULT timezone('utc'::text, now()),
  status text DEFAULT 'pending_verification'::text CHECK (status = ANY (ARRAY['pending_verification'::text, 'verified'::text, 'pending'::text, 'approved'::text, 'rejected'::text, 'waitlist'::text, 'contacted'::text, 'onboarded'::text])),
  priority text DEFAULT 'normal'::text CHECK (priority = ANY (ARRAY['low'::text, 'normal'::text, 'high'::text, 'urgent'::text])),
  contact_count integer DEFAULT 0,
  CONSTRAINT product_registrations_pkey PRIMARY KEY (id)
);
CREATE TABLE public.prompt_performance_metrics (
  prompt_id character varying,
  date date NOT NULL,
  avg_response_time_ms double precision,
  avg_token_usage double precision,
  avg_user_rating double precision,
  success_rate double precision,
  goal_accuracy double precision,
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  usage_count integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT prompt_performance_metrics_pkey PRIMARY KEY (id),
  CONSTRAINT prompt_performance_metrics_prompt_id_fkey FOREIGN KEY (prompt_id) REFERENCES public.contextual_prompts(id)
);
CREATE TABLE public.prompt_usage_logs (
  prompt_id character varying,
  session_id character varying,
  user_message text NOT NULL,
  detected_goal character varying,
  goal_confidence double precision,
  ai_response text,
  response_time_ms integer,
  token_usage jsonb,
  user_satisfaction_rating integer,
  feedback_text text,
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  timestamp timestamp with time zone DEFAULT now(),
  CONSTRAINT prompt_usage_logs_pkey PRIMARY KEY (id),
  CONSTRAINT prompt_usage_logs_prompt_id_fkey FOREIGN KEY (prompt_id) REFERENCES public.contextual_prompts(id)
);
CREATE TABLE public.response_templates (
  intent_name text NOT NULL,
  category_name text NOT NULL,
  template_format text NOT NULL,
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  required_variables ARRAY DEFAULT '{}'::text[],
  followup_suggestions ARRAY DEFAULT '{}'::text[],
  conditions jsonb DEFAULT '{}'::jsonb,
  priority integer DEFAULT 0,
  status text DEFAULT 'active'::text CHECK (status = ANY (ARRAY['active'::text, 'inactive'::text])),
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT response_templates_pkey PRIMARY KEY (id)
);
CREATE TABLE public.user_custom_prompts (
  user_id uuid,
  name character varying NOT NULL,
  description text,
  system_prompt text NOT NULL,
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  tone character varying DEFAULT 'professional'::character varying,
  max_tokens integer DEFAULT 800,
  temperature double precision DEFAULT 0.3,
  is_private boolean DEFAULT true,
  is_active boolean DEFAULT true,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  usage_count integer DEFAULT 0,
  CONSTRAINT user_custom_prompts_pkey PRIMARY KEY (id),
  CONSTRAINT user_custom_prompts_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id)
);
CREATE TABLE public.user_queries_log (
  session_id uuid,
  query_text text NOT NULL,
  query_intent text,
  query_category text,
  response_generated text,
  response_time_ms integer,
  retrieval_confidence numeric,
  user_satisfaction integer CHECK (user_satisfaction >= 1 AND user_satisfaction <= 5),
  conversation_resolved boolean,
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  retrieval_results jsonb DEFAULT '{}'::jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_queries_log_pkey PRIMARY KEY (id),
  CONSTRAINT user_queries_log_session_id_fkey FOREIGN KEY (session_id) REFERENCES public.demo_sessions(id)
);
