part of 'admin_cubit.dart';

class AdminState {
  final bool busy;
  final String? error;
  final String? adminToken;
  final bool stepMfa;
  final int tabIndex;
  final Map<String, dynamic>? usersData;
  final Map<String, dynamic>? leadsData;

  const AdminState({
    this.busy = false,
    this.error,
    this.adminToken,
    this.stepMfa = false,
    this.tabIndex = 0,
    this.usersData,
    this.leadsData,
  });

  AdminState copyWith({
    bool? busy,
    String? error,
    String? adminToken,
    bool? stepMfa,
    int? tabIndex,
    Map<String, dynamic>? usersData,
    Map<String, dynamic>? leadsData,
    bool clearError = false,
  }) => AdminState(
    busy: busy ?? this.busy,
    error: clearError ? null : (error ?? this.error),
    adminToken: adminToken ?? this.adminToken,
    stepMfa: stepMfa ?? this.stepMfa,
    tabIndex: tabIndex ?? this.tabIndex,
    usersData: usersData ?? this.usersData,
    leadsData: leadsData ?? this.leadsData,
  );
}
