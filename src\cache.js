/**
 * ArionComply Response Caching System
 * Version: 1.2 - Clean and Complete
 * Purpose: Intelligent caching for both mock responses and real API calls
 */

class ResponseCache {
  constructor(options = {}) {
    this.options = {
      maxCacheSize: options.maxCacheSize || 1000,
      ttl: options.ttl || 24 * 60 * 60 * 1000, // 24 hours
      enableCompression: options.enableCompression !== false,
      enableAnalytics: options.enableAnalytics !== false,
      storageKey: options.storageKey || 'arion-response-cache',
      analyticsKey: options.analyticsKey || 'arion-cache-analytics',
      debugMode: options.debugMode || false,
      demoMode: options.demoMode || false
    };

    this.cache = new Map();
    this.mockResponseCache = new Map();
    this.analytics = {
      hits: 0,
      misses: 0,
      evictions: 0,
      mockHits: 0,
      mockMisses: 0
    };

    this.init();
  }

  init() {
    this.loadFromStorage();
    this.startCleanupTimer();
    this.log('📦 Response cache initialized', {
      size: this.cache.size,
      maxSize: this.options.maxCacheSize,
      demoMode: this.options.demoMode
    });

    if (this.options.demoMode) {
      this.preWarmDemoResponses();
    }
  }

  generateKey(input, context = {}) {
    const normalizedInput = this.normalizeInput(input);
    const contextSignature = this.createContextSignature(context);
    return this.hash(normalizedInput + contextSignature);
  }

  normalizeInput(input) {
    return input
      .toLowerCase()
      .trim()
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ')
      .replace(/\b(the|a|an|and|or|but|in|on|at|to|for|of|with|by|how|what|when|where|why|tell|me|about|please|can|you|help|explain)\b/g, '')
      .trim();
  }

  createContextSignature(context) {
    const relevantContext = {
      detectedGoal: context.detectedGoal,
      conversationStage: context.conversationStage,
      userType: context.userType,
      demoMode: this.options.demoMode
    };

    return Object.entries(relevantContext)
      .filter(([_, value]) => value !== undefined && value !== null)
      .map(([key, value]) => `${key}:${value}`)
      .sort()
      .join('|');
  }

  hash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }

  get(key, isMockRequest = false) {
    const targetCache = isMockRequest ? this.mockResponseCache : this.cache;
    const item = targetCache.get(key);

    if (!item) {
      if (isMockRequest) {
        this.analytics.mockMisses++;
      } else {
        this.analytics.misses++;
      }
      return null;
    }

    if (Date.now() > item.expires) {
      targetCache.delete(key);
      this.analytics.evictions++;
      return null;
    }

    if (isMockRequest) {
      this.analytics.mockHits++;
    } else {
      this.analytics.hits++;
    }

    return {
      ...item.data,
      fromCache: true,
      cacheAge: Date.now() - item.created,
      cacheType: isMockRequest ? 'mock' : 'real'
    };
  }

  set(key, response, customTTL = null, isMockRequest = false) {
    const ttl = customTTL || this.options.ttl;
    const now = Date.now();
    const targetCache = isMockRequest ? this.mockResponseCache : this.cache;

    if (targetCache.size >= this.options.maxCacheSize) {
      const oldestKey = targetCache.keys().next().value;
      targetCache.delete(oldestKey);
      this.analytics.evictions++;
    }

    let data = { ...response };
    delete data.fromCache;
    delete data.cacheAge;
    delete data.cacheType;

    const cacheItem = {
      data,
      created: now,
      expires: now + ttl,
      type: isMockRequest ? 'mock' : 'real'
    };

    targetCache.set(key, cacheItem);
    this.log('💾 Cached response', { key, type: isMockRequest ? 'mock' : 'real' });
  }

  shouldCache(response, input, context = {}) {
    if (this.options.demoMode) {
      return true;
    }

    if (!response || (response.success === false) || response.error) {
      return false;
    }

    if (response.response && response.response.length < 30) {
      return false;
    }

    return true;
  }

  preWarmDemoResponses() {
    const demoResponses = [
      {
        input: 'what is iso 27001',
        response: {
          success: true,
          response: "ISO 27001 is an international standard for information security management systems (ISMS). It provides a systematic approach to managing sensitive company information, ensuring it remains secure through risk management processes.",
          quickReplies: ['Implementation guide', 'Required documents', 'Certification process', 'Cost breakdown']
        }
      },
      {
        input: 'gdpr compliance',
        response: {
          success: true,
          response: "GDPR compliance requires implementing data protection by design and default, conducting Data Protection Impact Assessments (DPIA), maintaining Records of Processing Activities (ROPA), and ensuring data subject rights are respected.",
          quickReplies: ['Create ROPA document', 'DPIA template', 'Data subject rights', 'Privacy policies']
        }
      },
      {
        input: 'ai governance',
        response: {
          success: true,
          response: "AI governance frameworks help organizations manage AI risks responsibly. Key components include AI ethics principles, risk assessment processes, and compliance with regulations like the EU AI Act.",
          quickReplies: ['EU AI Act compliance', 'Risk assessment', 'Ethics guidelines', 'Bias testing']
        }
      }
    ];

    demoResponses.forEach(({ input, response }) => {
      const key = this.generateKey(input, { demoMode: true });
      this.set(key, response, this.options.ttl, true);
    });

    this.log('🔥 Demo cache pre-warmed', { responses: demoResponses.length });
  }

  getStats() {
    const totalRequests = this.analytics.hits + this.analytics.misses;
    const totalMockRequests = this.analytics.mockHits + this.analytics.mockMisses;
    const hitRate = totalRequests > 0 ? (this.analytics.hits / totalRequests * 100).toFixed(1) : 0;
    const mockHitRate = totalMockRequests > 0 ? (this.analytics.mockHits / totalMockRequests * 100).toFixed(1) : 0;

    return {
      realCacheSize: this.cache.size,
      mockCacheSize: this.mockResponseCache.size,
      totalCacheSize: this.cache.size + this.mockResponseCache.size,
      maxSize: this.options.maxCacheSize,
      realHits: this.analytics.hits,
      realMisses: this.analytics.misses,
      realHitRate: `${hitRate}%`,
      mockHits: this.analytics.mockHits,
      mockMisses: this.analytics.mockMisses,
      mockHitRate: `${mockHitRate}%`,
      evictions: this.analytics.evictions,
      demoMode: this.options.demoMode
    };
  }

  cleanup() {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, item] of this.cache.entries()) {
      if (now > item.expires) {
        this.cache.delete(key);
        cleaned++;
      }
    }

    for (const [key, item] of this.mockResponseCache.entries()) {
      if (now > item.expires) {
        this.mockResponseCache.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      this.analytics.evictions += cleaned;
      this.log('🧹 Cleanup completed', { removed: cleaned });
    }

    return cleaned;
  }

  startCleanupTimer() {
    setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000); // Every 5 minutes
  }

  clear() {
    const size = this.cache.size + this.mockResponseCache.size;
    this.cache.clear();
    this.mockResponseCache.clear();
    this.log('🗑️ Both caches cleared', { itemsRemoved: size });
  }

  saveToStorage() {
    try {
      const cacheData = {
        version: '1.2',
        timestamp: Date.now(),
        realCache: Array.from(this.cache.entries()).slice(-50),
        mockCache: Array.from(this.mockResponseCache.entries()).slice(-50),
        analytics: this.analytics
      };

      localStorage.setItem(this.options.storageKey, JSON.stringify(cacheData));
      this.log('💾 Cache saved to storage');
    } catch (error) {
      this.log('❌ Failed to save cache', { error: error.message });
    }
  }

  loadFromStorage() {
    try {
      const stored = localStorage.getItem(this.options.storageKey);
      if (!stored) return;

      const cacheData = JSON.parse(stored);
      if (!cacheData.version) return;

      const now = Date.now();
      let loaded = 0;

      if (cacheData.realCache) {
        for (const [key, item] of cacheData.realCache) {
          if (item.expires > now) {
            this.cache.set(key, item);
            loaded++;
          }
        }
      }

      if (cacheData.mockCache) {
        for (const [key, item] of cacheData.mockCache) {
          if (item.expires > now) {
            this.mockResponseCache.set(key, item);
            loaded++;
          }
        }
      }

      if (cacheData.analytics) {
        this.analytics = { ...this.analytics, ...cacheData.analytics };
      }

      this.log('📥 Cache loaded from storage', { items: loaded });
    } catch (error) {
      this.log('❌ Failed to load cache', { error: error.message });
      localStorage.removeItem(this.options.storageKey);
    }
  }

  log(message, data = {}) {
    if (this.options.debugMode) {
      console.log(`🗄️ ResponseCache: ${message}`, data);
    }
  }

  export() {
    return {
      realCache: Array.from(this.cache.entries()),
      mockCache: Array.from(this.mockResponseCache.entries()),
      analytics: this.analytics,
      stats: this.getStats(),
      timestamp: new Date().toISOString()
    };
  }

  import(data) {
    try {
      if (data.realCache && Array.isArray(data.realCache)) {
        this.cache = new Map(data.realCache);
      }
      if (data.mockCache && Array.isArray(data.mockCache)) {
        this.mockResponseCache = new Map(data.mockCache);
      }
      if (data.analytics) {
        this.analytics = { ...this.analytics, ...data.analytics };
      }
      this.log('📥 Cache imported', { items: this.cache.size + this.mockResponseCache.size });
    } catch (error) {
      this.log('❌ Failed to import cache', { error: error.message });
    }
  }
}

// ArionUtils Integration Manager
class ArionCacheIntegration {
  constructor() {
    this.initialized = false;
    this.cache = null;
    this.originalApiCall = null;
    this.initAttempts = 0;
    this.maxAttempts = 50;

    this.init();
  }

  init() {
    if (this.tryInitialize()) {
      return;
    }

    const checkInterval = setInterval(() => {
      this.initAttempts++;

      if (this.tryInitialize()) {
        clearInterval(checkInterval);
        return;
      }

      if (this.initAttempts >= this.maxAttempts) {
        clearInterval(checkInterval);
        console.log('🗄️ Cache initialized standalone');
        this.initializeStandalone();
      }
    }, 100);
  }

  tryInitialize() {
    if (typeof ArionUtils === 'undefined' || !ArionUtils.api) {
      return false;
    }

    this.initializeWithArionUtils();
    return true;
  }

  initializeWithArionUtils() {
    if (this.initialized) return;

    console.log('🗄️ Initializing cache with ArionUtils integration...');

    this.cache = new ResponseCache({
      debugMode: (typeof ArionConfig !== 'undefined' && ArionConfig?.development?.debugMode) || false,
      enableAnalytics: (typeof ArionConfig !== 'undefined' && ArionConfig?.analytics?.enabled) !== false,
      maxCacheSize: 2000,
      ttl: 2 * 60 * 60 * 1000,
      demoMode: false
    });

    ArionUtils.responseCache = this.cache;
    this.originalApiCall = ArionUtils.api.call;

    ArionUtils.api.call = async (action, data = {}, sessionId = null) => {
      return this.enhancedApiCall(action, data, sessionId);
    };

    ArionUtils.cache = {
      getStats: () => this.cache.getStats(),
      clear: () => this.cache.clear(),
      preWarm: () => this.cache.preWarmDemoResponses(),
      export: () => this.cache.export(),
      import: (data) => this.cache.import(data),
      getInstance: () => this.cache
    };

    this.cache.preWarmDemoResponses();
    this.initialized = true;
    console.log('✅ Response caching system fully integrated with ArionUtils');
    this.addGlobalMethods();
  }

  initializeStandalone() {
    if (this.initialized) return;

    this.cache = new ResponseCache({
      debugMode: false,
      enableAnalytics: true,
      maxCacheSize: 1000,
      ttl: 2 * 60 * 60 * 1000,
      demoMode: true
    });

    window.ArionCache = {
      getInstance: () => this.cache,
      getStats: () => this.cache.getStats(),
      clear: () => this.cache.clear(),
      preWarm: () => this.cache.preWarmDemoResponses()
    };

    this.cache.preWarmDemoResponses();
    this.initialized = true;
    console.log('✅ Response caching system initialized in standalone mode');
  }

  async enhancedApiCall(action, data = {}, sessionId = null) {
    if (action !== 'query-agent' || !data.message) {
      return this.originalApiCall.call(ArionUtils.api, action, data, sessionId);
    }

    const message = data.message;
    const context = {
      action,
      sessionId: sessionId ? 'has_session' : 'no_session',
      demoMode: false  // Always try real responses first
    };

    console.log('🧠 Processing query with intelligent caching:', message.substring(0, 50) + '...');

    const cacheKey = this.cache.generateKey(message, context);

    // Only check real cache, not demo cache
    const cachedResponse = this.cache.get(cacheKey, false);  // false = real cache only

    if (cachedResponse) {
      console.log('⚡ Returning cached response');
      await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 200));
      return cachedResponse;
    }

    try {
      console.log('🤖 Making Claude API call...');
      const claudeResponse = await this.originalApiCall.call(ArionUtils.api, action, data, sessionId);

      if (claudeResponse.success && claudeResponse.response) {
        console.log('✅ Claude responded successfully');

        // Cache the real response (not demo response)
        this.cache.set(cacheKey, claudeResponse, null, false);  // false = real cache
        return claudeResponse;
      } else {
        throw new Error(claudeResponse.error || 'Claude API returned error');
      }

    } catch (error) {
      console.log('⚠️ Claude API failed, using intelligent fallback:', error.message);

      // Only now use fallback, but don't cache it as aggressively
      const fallbackResponse = this.generateIntelligentFallback(message, context);

      // Cache fallback for shorter time (5 minutes instead of 30)
      this.cache.set(cacheKey, fallbackResponse, 5 * 60 * 1000, false);
      return fallbackResponse;
    }
  }

  generateIntelligentFallback(message, context) {
    const messageLower = message.toLowerCase();

    if (messageLower.includes('iso') || messageLower.includes('27001')) {
      return {
        success: true,
        response: "**ISO 27001** is the international standard for Information Security Management Systems (ISMS). It provides a systematic approach to managing sensitive information through:\n\n**Key Benefits:**\n• Risk-based approach to security\n• Continuous improvement processes\n• Stakeholder confidence building\n• Competitive advantage in tenders\n\n**Core Requirements:**\n• Leadership commitment and security policies\n• Risk assessment and treatment processes\n• Security awareness and training programs\n• Regular audits and management reviews\n\nThe certification process typically takes 6-12 months depending on your organization's size and current maturity level.",
        quickReplies: ['Implementation guide', 'Required documents', 'Certification timeline', 'Cost breakdown']
      };
    }

    else if (messageLower.includes('gdpr') || messageLower.includes('privacy')) {
      return {
        success: true,
        response: "**GDPR (General Data Protection Regulation)** is the EU's comprehensive data protection law:\n\n**Key Principles:**\n• Lawfulness, fairness, and transparency\n• Purpose limitation and data minimization\n• Accuracy and storage limitation\n• Security and accountability\n\n**Main Requirements:**\n• Privacy by design and default\n• Data Protection Impact Assessments (DPIA)\n• Records of Processing Activities (ROPA)\n• Data subject rights management\n• Breach notification within 72 hours\n\n**Penalties:** Up to €20M or 4% of annual global revenue",
        quickReplies: ['ROPA template', 'DPIA guide', 'Data subject rights', 'Compliance checklist']
      };
    }

    else if (messageLower.includes('ai') || messageLower.includes('artificial intelligence')) {
      return {
        success: true,
        response: "**AI Governance** provides frameworks for responsible AI development and deployment:\n\n**Core Elements:**\n• AI ethics principles and guidelines\n• Risk management throughout AI lifecycle\n• Human oversight and transparency\n• Quality assurance and testing\n• Regulatory compliance (EU AI Act)\n\n**EU AI Act Requirements:**\n• Risk-based approach (prohibited, high, limited, minimal risk)\n• Transparency obligations for AI systems\n• Quality management and documentation\n• Human oversight requirements\n\nThe EU AI Act enters into force in 2024 with phased implementation through 2027.",
        quickReplies: ['EU AI Act compliance', 'Risk classification', 'Ethics framework', 'Implementation guide']
      };
    }

    else {
      return {
        success: true,
        response: "**ArionComply - AI-Powered Compliance Automation**\n\nI'm your intelligent compliance assistant, ready to help with:\n\n🏆 **ISO 27001** - Information security management certification\n🛡️ **GDPR & Privacy** - Data protection compliance\n🤖 **AI Governance** - Responsible AI management\n🔒 **Security & Risk** - Comprehensive risk management\n\n**How can I assist with your specific compliance needs today?**",
        quickReplies: ['ISO 27001 guidance', 'GDPR compliance help', 'AI risk management', 'Security assessment']
      };
    }
  }

  addGlobalMethods() {
    window.debugCache = () => {
      console.log('🗄️ Cache Debug Information:');
      console.table(this.cache.getStats());

      if (this.cache.options.debugMode) {
        console.log('Turning debug mode OFF');
        this.cache.options.debugMode = false;
      } else {
        console.log('Turning debug mode ON');
        this.cache.options.debugMode = true;
      }
    };

    window.clearCache = () => {
      if (confirm('Clear response cache? This will remove all cached responses.')) {
        this.cache.clear();
        console.log('✅ Cache cleared successfully');
      }
    };

    window.cacheStats = () => {
      const stats = this.cache.getStats();
      console.log('📊 Cache Statistics:');
      console.table(stats);
      return stats;
    };
  }
}

// Initialize the integration manager
const cacheIntegration = new ArionCacheIntegration();

// Make ResponseCache available globally
window.ResponseCache = ResponseCache;

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ResponseCache;
}

