/**
 * ArionComply Demo Configuration
 * Version: 1.2 - Enhanced for Edge Function Integration
 * Purpose: Centralized configuration for the demo application
 */

const ArionConfig = {
  // API Configuration - Updated for single router endpoint
  api: {
    // IMPORTANT: Update this to your actual Supabase project URL
    //baseUrl: 'https://plvaxsqhgchbkuefvyip.supabase.co/functions/v1',
    //baseUrl: 'https://oyvbuuugbfuvupruecvp.supabase.co/functions/v1',
    baseUrl: 'https://dxncozbhwppvwpugoqjk.supabase.co/functions/v1',

    // Single router endpoint for all API calls
    endpoint: '/quick-processor',

    timeout: 30000, // 30 seconds
    retryAttempts: 3,
    retryDelay: 1000, // 1 second

    // All actions route through single endpoint with action parameter
    actions: {
      initSession: 'init-session',
      queryAgent: 'query-agent',
      queryRag: 'query-rag',
      generateDocuments: 'generate-documents',
      getDocuments: 'get-documents',
      getDocument: 'get-document',
      saveContact: 'save-contact',
      logInteraction: 'log-interaction',
      logDocumentView: 'log-document-view',
      logEvent: 'log-event',
      requestDemoAccess: 'request-demo-access',
      verifyEmail: 'verify-email',
      resendVerification: 'resend-verification',
      submitPilotApplication: 'submit-pilot-application',
      // Admin actions
      adminRequestCode: 'admin-request-code',
      adminVerifyCode: 'admin-verify-code',
      verifyAdminSession: 'verify-admin-session',
      getLeads: 'get-leads'
    }
  },

  // Supabase Configuration
  supabase: {
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImR4bmNvemJod3BwdndwdWdvcWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMxOTgxMDQsImV4cCI6MjA2ODc3NDEwNH0.zx_wwG5nnNyxUwpUqJldQfNzSSfxgud4C4x0Bvx-r90'
  },

  // Assistant Configuration - Enhanced
  assistant: {
    messageMaxLength: 500,
    typingDelay: {
      min: 800,
      max: 2000
    },
    autoSuggestDelay: 30000, // 30 seconds of inactivity
    welcomeMessage: "Hi there! I'm your ArionComply Guide. What can I help you with today?",
    defaultQuickReplies: [
      "Get ISO 27001 certified",
      "GDPR compliance help",
      "AI governance guidance",
      "What can this platform do?"
    ],
    // Enhanced conversation stages
    conversationStages: {
      initial: 'initial',
      goalDetected: 'goal_detected',
      intakeStarted: 'intake_started',
      intakeComplete: 'intake_complete',
      documentsReady: 'documents_ready',
      leadCaptured: 'lead_captured',
      generalChat: 'general_chat'
    },
    // Quick reply templates by stage
    quickReplyTemplates: {
      initial: [
        "Get ISO 27001 certified",
        "GDPR compliance help",
        "AI governance guidance", 
        "What can this platform do?"
      ],
      goal_detected: [
        "Yes, let's get started",
        "Tell me more about this",
        "What documents will I get?",
        "How long does this take?"
      ],
      intake_started: [
        "Continue with questions",
        "Skip to document preview",
        "Change my goal",
        "Contact an expert"
      ],
      intake_complete: [
        "Generate my documents",
        "Schedule a consultation",
        "Start over with different goal",
        "Download information"
      ],
      documents_ready: [
        "View my documents",
        "Schedule consultation",
        "Download documents",
        "Get pricing information"
      ],
      general_chat: [
        "Tell me about ISO 27001",
        "GDPR requirements",
        "AI compliance",
        "Contact sales"
      ]
    },
    errorMessages: {
      generic: "I'm sorry, I encountered an error. Please try again.",
      timeout: "The request took too long. Please try again.",
      network: "Network error. Please check your connection and try again.",
      rateLimited: "Too many requests. Please wait a moment and try again.",
      sessionExpired: "Your session has expired. Please refresh the page to start over.",
      invalidInput: "Please provide valid input and try again."
    }
  },

  // Document Preview Configuration - Enhanced
  documents: {
    leadCaptureThreshold: 2, // Show lead capture after viewing 2 documents
    autoRefreshInterval: 5000, // Check for new documents every 5 seconds
    maxDocumentSize: 1000000, // 1MB limit for document content
    previewLength: 500, // Characters to show in preview
    supportedTypes: [
      'risk_register',
      'soa',
      'policy',
      'assessment',
      'ropa',
      'dpia',
      'audit_report',
      'gap_analysis',
      'implementation_plan'
    ],
    typeLabels: {
      risk_register: 'Risk Register',
      soa: 'Statement of Applicability',
      policy: 'Policy Document',
      assessment: 'Assessment Report',
      ropa: 'Record of Processing Activities',
      dpia: 'Data Protection Impact Assessment',
      audit_report: 'Audit Report',
      gap_analysis: 'Gap Analysis',
      implementation_plan: 'Implementation Plan'
    },
    typeIcons: {
      risk_register: '📋',
      soa: '📄',
      policy: '📜',
      assessment: '🔍',
      ropa: '🛡️',
      dpia: '🔒',
      audit_report: '✅',
      gap_analysis: '📊',
      implementation_plan: '🗺️'
    },
    typeDescriptions: {
      risk_register: 'Comprehensive inventory of information security risks',
      soa: 'ISO 27001 control selection and justification document',
      policy: 'Formal organizational security policies and procedures',
      assessment: 'Detailed compliance assessment and recommendations',
      ropa: 'GDPR-required record of data processing activities',
      dpia: 'Privacy impact assessment for high-risk processing',
      audit_report: 'Internal audit findings and recommendations',
      gap_analysis: 'Current state vs. required compliance analysis',
      implementation_plan: 'Step-by-step compliance implementation roadmap'
    }
  },

  // Goal Configuration - Enhanced
  goals: {
    types: {
      iso_certification: {
        title: 'ISO 27001 Certification',
        description: 'Get certified with ISO 27001 information security management',
        icon: '🏆',
        color: '#059669',
        category: 'Information Security',
        estimatedTime: '3-6 months',
        difficulty: 'Medium',
        intakeQuestions: [
          'How many people are in your organization?',
          'Which industry are you in?',
          'Are your systems cloud-based, on-premises, or hybrid?',
          'What key assets do you want to protect?',
          'Do you already have any policies or registers?'
        ],
        expectedDocuments: ['risk_register', 'soa', 'policy', 'gap_analysis'],
        benefits: [
          'Industry-recognized security certification',
          'Improved customer trust and compliance',
          'Structured approach to information security',
          'Competitive advantage in tenders'
        ]
      },
      gdpr_audit: {
        title: 'GDPR Compliance',
        description: 'Ensure compliance with General Data Protection Regulation',
        icon: '🛡️',
        color: '#3b82f6',
        category: 'Data Privacy',
        estimatedTime: '2-4 months',
        difficulty: 'Medium',
        intakeQuestions: [
          'What type of personal data do you process?',
          'What is the legal basis for processing?',
          'Do you transfer data outside the EU?',
          'How many data subjects are affected?',
          'Do you have a Data Protection Officer?'
        ],
        expectedDocuments: ['ropa', 'dpia', 'policy', 'assessment'],
        benefits: [
          'Avoid GDPR fines up to €20M or 4% of revenue',
          'Enhanced customer trust in data handling',
          'Improved data governance processes',
          'Competitive advantage in EU market'
        ]
      },
      ai_risk_management: {
        title: 'AI Risk Management',
        description: 'Manage AI governance and compliance with emerging regulations',
        icon: '🤖',
        color: '#8b5cf6',
        category: 'AI Governance',
        estimatedTime: '1-3 months',
        difficulty: 'High',
        intakeQuestions: [
          'What AI systems are you deploying?',
          'What is the risk level of your AI systems?',
          'Are you subject to the EU AI Act?',
          'What data does your AI process?',
          'Do you have AI governance policies?'
        ],
        expectedDocuments: ['assessment', 'risk_register', 'policy', 'implementation_plan'],
        benefits: [
          'Proactive AI risk management',
          'EU AI Act compliance readiness',
          'Ethical AI implementation framework',
          'Stakeholder confidence in AI systems'
        ]
      },
      soa_generation: {
        title: 'Statement of Applicability',
        description: 'Generate comprehensive SoA documents',
        icon: '📄',
        color: '#f59e0b',
        category: 'Documentation',
        estimatedTime: '2-4 weeks',
        difficulty: 'Low',
        intakeQuestions: [
          'Which ISO 27001 controls are applicable?',
          'What is your implementation status?',
          'Are there any excluded controls?',
          'What is your risk appetite?',
          'Do you have existing controls documentation?'
        ],
        expectedDocuments: ['soa', 'gap_analysis', 'implementation_plan'],
        benefits: [
          'ISO 27001 certification requirement',
          'Clear control implementation roadmap',
          'Audit-ready documentation',
          'Systematic approach to controls'
        ]
      },
      capa_flow: {
        title: 'CAPA Process',
        description: 'Manage corrective and preventive actions',
        icon: '🔄',
        color: '#ef4444',
        category: 'Process Management',
        estimatedTime: '1-2 weeks',
        difficulty: 'Low',
        intakeQuestions: [
          'What type of nonconformity occurred?',
          'What is the severity level?',
          'Who is responsible for corrective actions?',
          'What is the expected timeline?',
          'Are there similar incidents in the past?'
        ],
        expectedDocuments: ['assessment', 'implementation_plan', 'audit_report'],
        benefits: [
          'Systematic issue resolution',
          'Prevention of recurring problems',
          'Audit compliance demonstration',
          'Continuous improvement culture'
        ]
      }
    },
    detectionPatterns: {
      iso_certification: /\b(iso|27001|certification|isms|information security management)\b/i,
      gdpr_audit: /\b(gdpr|privacy|data protection|personal data|consent)\b/i,
      ai_risk_management: /\b(ai|artificial intelligence|machine learning|algorithm|automated)\b/i,
      soa_generation: /\b(soa|statement of applicability|controls|annex)\b/i,
      capa_flow: /\b(capa|corrective|preventive|action|incident|nonconformity)\b/i
    },
    // Goal progression tracking
    progressStages: {
      detected: 'Goal detected from user input',
      confirmed: 'User confirmed the goal',
      intake_started: 'Intake questions begun',
      intake_complete: 'All intake questions answered',
      documents_generated: 'Documents created and ready',
      lead_captured: 'Contact information provided',
      consultation_scheduled: 'Follow-up meeting booked'
    }
  },

  // UI Configuration - Enhanced
  ui: {
    theme: {
      colors: {
        primary: '#059669',
        secondary: '#047857',
        accent: '#065f46',
        dark: '#064e3b',
        light: '#ecfdf5',
        blue: '#3b82f6',
        blueSecondary: '#2563eb',
        blueLight: '#eff6ff',
        purple: '#8b5cf6',
        purpleLight: '#f3f4f6',
        orange: '#f59e0b',
        orangeLight: '#fef3c7',
        red: '#ef4444',
        redLight: '#fee2e2',
        neutral: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a'
        },
        success: '#10b981',
        warning: '#f59e0b',
        error: '#ef4444',
        info: '#3b82f6'
      },
      fonts: {
        primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif',
        mono: 'ui-monospace, SFMono-Regular, "SF Mono", Menlo, Monaco, Consolas, monospace'
      },
      breakpoints: {
        mobile: 768,
        tablet: 1024,
        desktop: 1200,
        wide: 1400
      },
      spacing: {
        xs: '0.25rem',
        sm: '0.5rem',
        md: '1rem',
        lg: '1.5rem',
        xl: '2rem',
        xxl: '3rem'
      }
    },
    animations: {
      duration: {
        fast: 150,
        normal: 250,
        slow: 400,
        slower: 600
      },
      easing: {
        default: 'cubic-bezier(0.4, 0, 0.2, 1)',
        bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
        smooth: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
      }
    },
    components: {
      modal: {
        maxWidth: '480px',
        maxHeight: '90vh',
        padding: '1rem',
        borderRadius: '12px',
        backdropBlur: '8px'
      },
      assistant: {
        launcher: {
          size: '60px',
          position: { bottom: '24px', right: '24px' }
        },
        bubble: {
          maxWidth: '85%',
          borderRadius: '12px',
          padding: '12px 16px'
        },
        quickReply: {
          maxPerRow: 2,
          borderRadius: '20px',
          padding: '8px 16px'
        }
      },
      document: {
        previewHeight: '400px',
        thumbnailSize: '120px',
        borderRadius: '8px'
      }
    }
  },

  // Analytics Configuration - Enhanced
  analytics: {
    enabled: true,
    events: {
      // Session events
      pageView: 'page_view',
      sessionStart: 'session_start',
      sessionEnd: 'session_end',
      
      // Assistant events
      assistantOpen: 'assistant_opened',
      assistantClose: 'assistant_closed',
      messageSent: 'message_sent',
      quickReplyUsed: 'quick_reply_used',
      
      // Goal and intake events
      goalDetected: 'goal_detected',
      goalConfirmed: 'goal_confirmed',
      intakeStarted: 'intake_started',
      intakeQuestionAnswered: 'intake_question_answered',
      intakeCompleted: 'intake_completed',
      intakeSkipped: 'intake_skipped',
      
      // Document events
      documentsGenerated: 'documents_generated',
      documentViewed: 'document_viewed',
      documentDownloaded: 'document_downloaded',
      documentShared: 'document_shared',
      
      // Lead capture events
      leadCaptureShown: 'lead_capture_shown',
      leadCaptured: 'lead_captured',
      consultationRequested: 'consultation_requested',
      
      // Error events
      errorOccurred: 'error_occurred',
      apiError: 'api_error',
      timeoutError: 'timeout_error'
    },
    trackingProperties: {
      includeUserAgent: true,
      includeReferrer: true,
      includeTimestamp: true,
      includeSessionId: true,
      includeViewportSize: true,
      includeDeviceType: true
    },
    // Funnel tracking for conversion analysis
    funnelSteps: [
      'session_start',
      'assistant_opened',
      'goal_detected',
      'intake_completed',
      'documents_generated',
      'lead_captured'
    ]
  },

  // Feature Flags - Enhanced
  features: {
    // Core features
    voiceInput: false,
    darkMode: false,
    realTimeUpdates: true,
    leadCapture: true,
    analytics: true,
    errorReporting: true,
    autoSave: true,
    
    // Document features
    documentExport: false, // Disabled for demo
    documentSharing: true,
    documentVersioning: false,
    
    // Assistant features
    conversationalIntake: true,
    goalDetection: true,
    contextAwareness: true,
    multiLanguage: false,
    
    // Advanced features
    vectorSearch: false, // Future feature
    aiDocumentGeneration: true,
    customBranding: false,
    integrations: false,
    
    // A/B testing flags
    newWelcomeFlow: false,
    enhancedQuickReplies: true,
    simplifiedIntake: false
  },

  // Development Configuration - Enhanced
  development: {
    debugMode: false,
    mockApiCalls: false,
    showPerformanceMetrics: false,
    enableTestMode: false,
    logLevel: 'warn', // 'debug', 'info', 'warn', 'error'
    
    // Testing configurations
    mockResponses: {
      enabled: false,
      delay: 1000, // Simulate API delay
      failureRate: 0 // 0.1 = 10% failure rate
    },
    
    // Debug panels
    showSessionInfo: false,
    showApiLogs: false,
    showAnalyticsEvents: false
  },

  // Security Configuration - Enhanced
  security: {
    apiKey: null, // Set via environment
    corsOrigins: [
      'https://iso.arionetworks.com',
      'https://arionetworks.com',
      'http://localhost:3000', // Development only
      'http://127.0.0.1:3000'  // Development only
    ],
    rateLimiting: {
      enabled: true,
      maxRequests: 100,
      windowMs: 900000, // 15 minutes
      skipOnSuccess: true
    },
    inputValidation: {
      maxMessageLength: 500,
      maxNameLength: 100,
      maxEmailLength: 254,
      maxCompanyLength: 200,
      maxPhoneLength: 20,
      allowedFileTypes: ['pdf', 'doc', 'docx'], // Future file upload
      maxFileSize: 5242880 // 5MB
    },
    // Content Security Policy
    csp: {
      enabled: true,
      directives: {
        'default-src': ["'self'"],
        'script-src': ["'self'", "'unsafe-inline'", 'https://cdnjs.cloudflare.com'],
        'style-src': ["'self'", "'unsafe-inline'"],
        'img-src': ["'self'", 'data:', 'https:'],
        'connect-src': ["'self'", 'https://*.supabase.co', 'https://api.anthropic.com']
      }
    }
  },

  // Performance Configuration - Enhanced
  performance: {
    lazyLoading: true,
    imageOptimization: true,
    cacheTimeout: 300000, // 5 minutes
    debounceDelay: 300,
    throttleLimit: 1000,
    
    // Memory management
    maxCachedSessions: 10,
    maxCachedMessages: 100,
    maxCachedDocuments: 50,
    
    // Network optimization
    retryStrategy: 'exponential', // 'linear', 'exponential'
    maxRetries: 3,
    requestTimeout: 30000,
    
    // Rendering optimization
    virtualScrolling: false, // For large document lists
    batchSize: 20, // Items to render per batch
    prefetchDistance: 5 // Items to prefetch ahead
  },

  // Internationalization (i18n) - Future feature
  i18n: {
    defaultLanguage: 'en',
    supportedLanguages: ['en'], // Future: ['en', 'de', 'fr', 'es']
    fallbackLanguage: 'en',
    loadPath: '/locales/{{lng}}/{{ns}}.json'
  },

  // Lead Capture Configuration - Enhanced
  leadCapture: {
    enabled: true,
    triggers: {
      documentViewCount: 2,
      timeOnSite: 120000, // 2 minutes
      exitIntent: true,
      scrollPercentage: 75
    },
    fields: {
      required: ['name', 'email'],
      optional: ['company', 'phone', 'message'],
      validation: {
        name: /^[a-zA-Z\s]{2,50}$/,
        email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        phone: /^[\+]?[\d\s\-\(\)]{7,20}$/
      }
    },
    followUp: {
      autoResponse: true,
      responseDelay: 5000, // 5 seconds
      schedulingLink: 'https://calendly.com/arion-demo', // Example
      thankYouMessage: 'Thank you! We\'ll be in touch soon.'
    }
  },

  // Integration Configuration - Future features
  integrations: {
    crm: {
      enabled: false,
      provider: null, // 'hubspot', 'salesforce', 'pipedrive'
      webhookUrl: null,
      apiKey: null
    },
    calendar: {
      enabled: false,
      provider: null, // 'calendly', 'acuity', 'google'
      bookingUrl: null
    },
    email: {
      enabled: false,
      provider: null, // 'sendgrid', 'mailchimp', 'klaviyo'
      apiKey: null,
      templates: {
        welcome: null,
        followUp: null,
        documentReady: null
      }
    }
  }
};

// Environment-specific overrides
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
  ArionConfig.development.debugMode = true;
  ArionConfig.development.logLevel = 'debug';
  ArionConfig.development.showSessionInfo = true;
  ArionConfig.development.showApiLogs = true;
  //ArionConfig.api.baseUrl = 'http://localhost:54321/functions/v1';
}

// Production optimizations
if (window.location.hostname.includes('arionetworks.com')) {
  ArionConfig.performance.lazyLoading = true;
  ArionConfig.performance.imageOptimization = true;
  ArionConfig.analytics.enabled = true;
  ArionConfig.security.csp.enabled = true;
}

// Validation function for configuration
ArionConfig.validate = function() {
  const errors = [];
  
  if (!this.api.baseUrl) {
    errors.push('API base URL is required');
  }
  
  if (!this.supabase.anonKey) {
    errors.push('Supabase anonymous key is required');
  }
  
  if (this.leadCapture.enabled && !this.leadCapture.fields.required.length) {
    errors.push('Lead capture requires at least one required field');
  }
  
  if (errors.length > 0) {
    console.error('Configuration validation errors:', errors);
    return false;
  }
  
  return true;
};

// Helper function to get goal info by key
ArionConfig.getGoalInfo = function(goalKey) {
  return this.goals.types[goalKey] || null;
};

// Helper function to get color by goal
ArionConfig.getGoalColor = function(goalKey) {
  const goal = this.getGoalInfo(goalKey);
  return goal ? goal.color : this.ui.theme.colors.neutral[500];
};

// Helper function to check if feature is enabled
ArionConfig.isFeatureEnabled = function(featureName) {
  return this.features[featureName] === true;
};

// Helper function to get environment
ArionConfig.getEnvironment = function() {
  const hostname = window.location.hostname;
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return 'development';
  } else if (hostname.includes('staging') || hostname.includes('dev')) {
    return 'staging';
  } else {
    return 'production';
  }
};

// Freeze configuration to prevent accidental modifications
Object.freeze(ArionConfig);

// Make configuration globally available
window.ArionConfig = ArionConfig;

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ArionConfig;
}
