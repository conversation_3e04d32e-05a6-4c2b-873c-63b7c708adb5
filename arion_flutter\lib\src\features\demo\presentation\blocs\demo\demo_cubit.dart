import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../../../core/services/compliance_filter.dart';
import '../../../../../domain/repositories/chat_repository.dart';
import '../../../../../domain/repositories/document_repository.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../../domain/repositories/feedback_repository.dart';

part 'demo_state.dart';

class DemoCubit extends Cubit<DemoState> {
  final ChatRepository _chat;
  final DocumentRepository _docs;
  final ComplianceFilterService _filter;
  final FeedbackRepository _feedbackRepo;

  DemoCubit(this._chat, this._docs, this._filter, this._feedbackRepo)
    : super(const DemoState.initial());

  Future<void> initialize({bool isAdmin = false}) async {
    final prefs = await SharedPreferences.getInstance();
    final limits = prefs.getString('arion-demo-limits');
    emit(state.copyWith(isAdmin: isAdmin));
    if (limits != null) {
      // simplistic restore
      emit(state.copyWith(sessionCount: 0));
    }
    // add welcome assistant message
    final initialMessages = List<DemoMessage>.from(state.messages)
      ..add(
        DemoMessage.assistant(
          'Hi! I\'m your ArionComply Guide. I can help you with ISO 27001 certification, GDPR compliance, AI governance, and security best practices. What would you like to explore today?',
          quickReplies: const [
            'Get ISO 27001 certified',
            'GDPR compliance help',
            'AI governance guidance',
          ],
        ),
      );
    emit(state.copyWith(messages: initialMessages));
  }

  void toggleInfo() => emit(state.copyWith(infoOpen: !state.infoOpen));
  void closeWelcome() => emit(state.copyWith(welcomeClosed: true));
  void hideSuggestions() => emit(state.copyWith(suggestionsHidden: true));
  void showSuggestions() => emit(state.copyWith(suggestionsHidden: false));

  Future<void> sendQuickReply(String text) async {
    if (text == '📄 View My Documents' || text == 'View My Documents') {
      await showDocuments();
      return;
    }
    if (text.contains('Request Full Access')) {
      emit(state.copyWith(leadCaptureOpen: true));
      return;
    }
    await sendMessage(text);
  }

  Future<void> sendMessage(String text) async {
    if (state.waiting || text.trim().isEmpty) return;
    if (!state.isAdmin && !_checkLimit()) return;

    final compliance = _filter.validate(text);
    final updated = List<DemoMessage>.from(state.messages)
      ..add(DemoMessage.user(text));
    emit(state.copyWith(messages: updated, waiting: true));

    if (!compliance.isCompliant) {
      final rej = compliance.rejectionMessage ?? {};
      final rejectionMsg = DemoMessage.assistantRejection(rej);
      emit(
        state.copyWith(
          messages: List.of(updated)..add(rejectionMsg),
          waiting: false,
        ),
      );
      return;
    }

    try {
      final res = await _chat.queryAgent(text);
      if (res['success'] == true && res['response'] != null) {
        final replies = List<String>.from(res['quickReplies'] ?? const []);
        // Add view docs suggestion when suggested
        final List<String> suggested = List<String>.from(replies);
        if ((res['suggestedActions'] ?? []).contains('show_documents')) {
          suggested.add('📄 View My Documents');
        }
        final next = DemoMessage.assistant(
          res['response'] as String,
          quickReplies: suggested,
        );
        emit(
          state.copyWith(messages: List.of(updated)..add(next), waiting: false),
        );
      } else {
        emit(
          state.copyWith(
            messages: List.of(updated)
              ..add(
                DemoMessage.assistant(
                  'I\'m sorry, I had trouble responding. Please try again.',
                ),
              ),
            waiting: false,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          messages: List.of(updated)
            ..add(DemoMessage.assistant('Network error. Please try again.')),
          waiting: false,
        ),
      );
    }
  }

  Future<void> showDocuments() async {
    emit(state.copyWith(view: DemoView.documents));
    try {
      final res = await _docs.getDocuments();
      if (res['success'] == true) {
        final docs = List<Map<String, dynamic>>.from(
          res['documents'] ?? const [],
        );
        emit(state.copyWith(documents: docs));
      } else {
        emit(state.copyWith(documents: const []));
      }
    } catch (_) {
      emit(state.copyWith(documents: const []));
    }
  }

  Future<void> viewDocument(String id) async {
    try {
      final res = await _docs.getDocument(id);
      if (res['success'] == true && res['document'] != null) {
        emit(
          state.copyWith(
            view: DemoView.viewer,
            currentDocument: Map<String, dynamic>.from(res['document']),
          ),
        );
        final viewed = state.documentsViewed + 1;
        emit(state.copyWith(documentsViewed: viewed));
        if (viewed >= 2) {
          // delay opening lead capture
          Future.delayed(
            const Duration(seconds: 3),
            () => emit(state.copyWith(leadCaptureOpen: true)),
          );
        }
      }
    } catch (_) {}
  }

  void backToDocuments() => emit(state.copyWith(view: DemoView.documents));
  void backToChat() => emit(state.copyWith(view: DemoView.chat));
  void closeLeadCapture() => emit(state.copyWith(leadCaptureOpen: false));
  void openLeadCapture() => emit(state.copyWith(leadCaptureOpen: true));
  void openLeadFromLimit() =>
      emit(state.copyWith(leadCaptureOpen: true, limitReached: false));
  void clearLimit() => emit(state.copyWith(limitReached: false));

  bool _checkLimit() {
    final s = state.sessionCount + 1;
    if (s > state.sessionLimit) {
      emit(state.copyWith(limitReached: true, limitType: 'session'));
      return false;
    }
    emit(state.copyWith(sessionCount: s));
    return true;
  }

  // ---------------- Feedback persistence (v2 parity) ----------------

  // Legacy wrapper retained for existing UI; maps to full feedback with defaults.
  Future<void> submitFeedbackForm({
    required String type,
    required String subject,
    required String message,
    String? rating,
  }) async {
    await submitFullFeedback(
      isAdmin: state.isAdmin,
      id: _generateFeedbackId(),
      type: type,
      priority: 'medium',
      subject: subject,
      message: message,
      additionalNotes: null,
      rating: rating,
      contactMethod: null,
    );
  }

  Future<void> submitFullFeedback({
    required bool isAdmin,
    required String id,
    required String type,
    required String priority,
    required String subject,
    required String message,
    String? additionalNotes,
    String? rating,
    String? contactMethod,
    String adminStatus = 'new',
    String? adminAssignee,
    String? adminNotes,
  }) async {
    await _feedbackRepo.submitFullFeedback(
      isAdmin: isAdmin,
      id: id,
      type: type,
      priority: priority,
      subject: subject,
      message: message,
      additionalNotes: additionalNotes,
      rating: rating,
      contactMethod: contactMethod,
      adminStatus: adminStatus,
      adminAssignee: adminAssignee,
      adminNotes: adminNotes,
    );
  }

  Future<void> submitMessageFeedback({
    required String messagePreview,
    required String feedback, // 'helpful' | 'not-helpful'
  }) async {
    await _feedbackRepo.submitMessageFeedback(
      messagePreview: messagePreview,
      feedback: feedback,
    );
  }

  Future<void> submitQuickMessageReport({
    required String messageContent,
    required String issueType,
    required String description,
  }) async {
    await _feedbackRepo.submitQuickMessageReport(
      messageContent: messageContent,
      issueType: issueType,
      description: description,
    );
  }

  String _generateFeedbackId() {
    final ts = DateTime.now().millisecondsSinceEpoch;
    final rand = DateTime.now().microsecondsSinceEpoch % 1000;
    return 'FB' +
        ts.toRadixString(36).toUpperCase() +
        rand.toRadixString(36).toUpperCase();
  }
}
