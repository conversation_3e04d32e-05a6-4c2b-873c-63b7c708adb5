<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enterprise Innovation Platform - Build • Test • Deploy</title>
    <style>
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f0f9ff 0%, #f3e8ff 50%, #cffafe 100%);
            color: #1f2937;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }
        
        .logo-header {
            height: 80px;
            background: transparent;
            display: flex;
            justify-content: center;
            align-items: center;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        nav {
            background: transparent;
            padding: 16px 0;
            box-shadow: none;
            position: relative;
            width: 100%;
            z-index: 100;
        }
        
        nav .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        nav .logo {
            font-weight: 800;
            font-size: 1.4em;
            background: linear-gradient(135deg, #7c3aed 0%, #0891b2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.5px;
        }
        
        nav .family {
            color: #64748b;
            font-size: 0.8em;
            margin-left: 12px;
        }
        
        nav a {
            text-decoration: none;
            color: #475569;
            margin: 0 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        nav a:hover {
            color: #7c3aed;
        }
        
        .back-link {
            color: #64748b !important;
            font-size: 0.9em;
        }
        
        .back-link:hover {
            color: #7c3aed !important;
        }
        
        .hero {
            text-align: center;
            margin: 40px 0 60px;
            padding: 80px 0;
        }
        
        .hero h1 {
            font-size: 4em;
            font-weight: 900;
            margin: 0 0 20px;
            color: #0f172a;
            letter-spacing: -2px;
        }
        
        .hero .subtitle {
            font-size: 1.6em;
            background: linear-gradient(135deg, #7c3aed 0%, #0891b2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 600;
            margin-bottom: 24px;
        }
        
        .hero .description {
            font-size: 1.3em;
            color: #64748b;
            max-width: 900px;
            margin: 0 auto 50px;
            line-height: 1.7;
        }
        
        .pipeline-section {
            background: white;
            border-radius: 20px;
            padding: 60px 0;
            margin: 80px 0;
            box-shadow: 0 8px 40px rgba(0,0,0,0.06);
        }
        
        .pipeline-flow {
            display: grid;
            grid-template-columns: 1fr auto 1fr auto 1fr;
            gap: 20px;
            align-items: center;
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .pipeline-stage {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 40px;
            border-radius: 16px;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .pipeline-stage.build {
            border-color: #7c3aed;
        }
        
        .pipeline-stage.test {
            border-color: #0891b2;
        }
        
        .pipeline-stage.deploy {
            border-color: #059669;
        }
        
        .pipeline-stage:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }
        
        .stage-icon {
            font-size: 3em;
            margin-bottom: 16px;
        }
        
        .stage-title {
            font-size: 1.4em;
            font-weight: 700;
            color: #0f172a;
            margin-bottom: 12px;
        }
        
        .stage-platform {
            font-size: 1.1em;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .stage-platform.build {
            color: #7c3aed;
        }
        
        .stage-platform.test {
            color: #0891b2;
        }
        
        .stage-platform.deploy {
            color: #059669;
        }
        
        .stage-desc {
            color: #64748b;
            font-size: 0.95em;
            line-height: 1.5;
        }
        
        .pipeline-arrow {
            font-size: 2.5em;
            color: #7c3aed;
            font-weight: bold;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 60px 0;
        }
        
        .stat-card {
            background: white;
            padding: 30px;
            border-radius: 16px;
            text-align: center;
            box-shadow: 0 4px 25px rgba(0,0,0,0.06);
            border: 1px solid rgba(124, 58, 237, 0.1);
            border-top: 4px solid;
            border-image: linear-gradient(135deg, #7c3aed 0%, #0891b2 100%) 1;
        }
        
        .stat-number {
            font-size: 3em;
            font-weight: 900;
            background: linear-gradient(135deg, #7c3aed 0%, #0891b2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
            line-height: 1;
        }
        
        .stat-label {
            color: #64748b;
            font-weight: 500;
            margin-top: 8px;
        }
        
        .demo-section {
            background: white;
            border-radius: 20px;
            padding: 60px 0;
            margin: 80px 0;
            box-shadow: 0 8px 40px rgba(0,0,0,0.06);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .demo-header h2 {
            font-size: 2.5em;
            font-weight: 800;
            color: #0f172a;
            margin-bottom: 16px;
        }
        
        .chat-interface {
            max-width: 900px;
            margin: 0 auto;
            background: #f8fafc;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
        }
        
        .chat-header {
            background: linear-gradient(135deg, #7c3aed 0%, #0891b2 100%);
            color: white;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .chat-info h3 {
            margin: 0;
            font-size: 1.1em;
        }
        
        .chat-info p {
            margin: 0;
            opacity: 0.9;
            font-size: 0.9em;
        }
        
        .chat-messages {
            padding: 30px;
            height: 700px;
            overflow-y: auto;
            background: #f8fafc;
        }
        
        .message {
            margin-bottom: 20px;
            display: flex;
            gap: 12px;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message.user .bubble {
            background: #3b82f6;
            color: white;
            margin-left: 80px;
        }
        
        .message.ai .bubble {
            background: white;
            border: 1px solid #e5e7eb;
            margin-right: 80px;
        }
        
        .bubble {
            padding: 16px 20px;
            border-radius: 18px;
            max-width: 550px;
            line-height: 1.5;
        }
        
        .source-note {
            font-size: 0.8em;
            color: #64748b;
            margin-top: 8px;
            font-style: italic;
        }
        
        .platform-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
            margin: 4px 0;
        }
        
        .platform-tag.arionflow {
            background: rgba(124, 58, 237, 0.1);
            color: #7c3aed;
        }
        
        .platform-tag.labs {
            background: rgba(8, 145, 178, 0.1);
            color: #0891b2;
        }
        
        .integration-section {
            background: linear-gradient(135deg, #f0f9ff 0%, #f3e8ff 50%, #cffafe 100%);
            padding: 80px 0;
            margin: 80px 0;
            border-radius: 20px;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin: 60px 0;
        }
        
        .feature-card {
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 4px 25px rgba(0,0,0,0.06);
            border-left: 6px solid;
            border-image: linear-gradient(135deg, #7c3aed 0%, #0891b2 100%) 1;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 40px rgba(0,0,0,0.1);
        }
        
        .feature-card h3 {
            color: #0f172a;
            font-size: 1.4em;
            font-weight: 700;
            margin-bottom: 16px;
        }
        
        .feature-card p {
            color: #64748b;
            line-height: 1.7;
        }
        
        .cta-section {
            background: linear-gradient(135deg, #7c3aed 0%, #0891b2 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
            border-radius: 20px;
            margin: 80px 0;
        }
        
        .cta-section h2 {
            font-size: 2.5em;
            font-weight: 800;
            margin-bottom: 20px;
        }
        
        .cta-section p {
            font-size: 1.3em;
            margin-bottom: 40px;
            opacity: 0.95;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 40px;
        }
        
        .btn {
            display: inline-block;
            background: white;
            color: #7c3aed;
            padding: 16px 40px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 700;
            font-size: 1.1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(255,255,255,0.3);
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(255,255,255,0.4);
        }
        
        footer {
            background: #1f2937;
            color: white;
            padding: 60px 0 40px;
            text-align: center;
        }
        
        footer .logo {
            font-size: 1.8em;
            font-weight: 800;
            background: linear-gradient(135deg, #7c3aed 0%, #0891b2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 12px;
        }
        
        footer .family {
            color: #9ca3af;
            margin-bottom: 20px;
        }
        
        footer a {
            color: #9ca3af;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        footer a:hover {
            color: #7c3aed;
        }
        
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5em;
            }
            
            .pipeline-flow {
                grid-template-columns: 1fr;
                gap: 30px;
            }
            
            .pipeline-arrow {
                transform: rotate(90deg);
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .chat-messages {
                height: 500px;
            }
            
            .message.user .bubble,
            .message.ai .bubble {
                margin-left: 0;
                margin-right: 0;
                max-width: 320px;
            }
        }
    </style>
</head>
<body>

    <!-- Logo Header -->
    <div class="logo-header">
        <img src="/images/ArionLogo.png" alt="Arion Networks" style="height: 60px; width: auto;">
    </div>

    <nav>
        <div class="container">
            <div>
                <span class="logo">Enterprise Innovation Platform</span>
                <span class="family">Family of Arion Networks</span>
            </div>
            <div>
                <a href="#pipeline">Platform</a>
                <a href="#demo">AI Demo</a>
                <a href="#features">Features</a>
                <a href="#contact">Contact</a>
                <a href="https://arionetworks.com" class="back-link">← Back to Arion Networks</a>
            </div>
        </div>
    </nav>

    <div class="container">
        
        <div class="hero">
            <h1>Enterprise Innovation Platform</h1>
            <p class="subtitle">Build • Test • Deploy</p>
            <p class="description">
                The complete solution for enterprise application development and testing. Build applications 10x faster with conversational AI, then test them instantly in enterprise-grade lab environments. From concept to production in days, not months.
            </p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">10x</div>
                <div class="stat-label">Faster Development to Production</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">80%</div>
                <div class="stat-label">Reduction in Infrastructure Overhead</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">15min</div>
                <div class="stat-label">From Code to Testing Environment</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">100%</div>
                <div class="stat-label">End-to-End Process Automation</div>
            </div>
        </div>

    </div>

    <div class="pipeline-section" id="pipeline">
        <div class="container">
            <div style="text-align: center; margin-bottom: 60px;">
                <h2 style="font-size: 2.5em; font-weight: 800; color: #0f172a; margin-bottom: 20px;">Complete Innovation Pipeline</h2>
                <p style="font-size: 1.2em; color: #64748b;">Seamlessly integrated development and testing ecosystem with AI assistance at every stage</p>
            </div>
            
            <div class="pipeline-flow">
                <div class="pipeline-stage build">
                    <div class="stage-icon">🚀</div>
                    <div class="stage-title">BUILD</div>
                    <div class="stage-platform build">ArionFlow</div>
                    <div class="stage-desc">
                        Conversational application development with AI-powered workflow creation. Build enterprise apps through natural language.
                    </div>
                </div>
                
                <div class="pipeline-arrow">→</div>
                
                <div class="pipeline-stage test">
                    <div class="stage-icon">🧪</div>
                    <div class="stage-title">TEST</div>
                    <div class="stage-platform test">Labs as a Service</div>
                    <div class="stage-desc">
                        Instant provisioning of enterprise testing environments. Validate applications with real-world infrastructure.
                    </div>
                </div>
                
                <div class="pipeline-arrow">→</div>
                
                <div class="pipeline-stage deploy">
                    <div class="stage-icon">🎯</div>
                    <div class="stage-title">DEPLOY</div>
                    <div class="stage-platform deploy">Integrated Pipeline</div>
                    <div class="stage-desc">
                        Automated deployment with continuous monitoring. Production-ready applications with built-in reliability.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="demo-section" id="demo">
        <div class="container">
            <div class="demo-header">
                <h2>Unified AI Development Assistant</h2>
                <p style="font-size: 1.2em; color: #64748b;">Experience the complete development-to-testing pipeline with intelligent orchestration</p>
            </div>
            
            <div class="chat-interface">
                <div class="chat-header">
                    <div class="avatar">EIP</div>
                    <div class="chat-info">
                        <h3>Enterprise Innovation Platform AI</h3>
                        <p>Build-Test-Deploy Expert • Fully Integrated</p>
                    </div>
                    <div style="margin-left: auto; width: 12px; height: 12px; background: linear-gradient(135deg, #7c3aed 0%, #0891b2 100%); border-radius: 50%; animation: pulse 2s infinite;"></div>
                </div>
                
                <div class="chat-messages">
                    <div class="message user">
                        <div class="bubble">
                            I need to build and test a new employee expense approval system that integrates with our SAP environment
                        </div>
                    </div>
                    
                    <div class="message ai">
                        <div class="bubble">
                            I'll help you build and test a complete expense approval system. Let me coordinate both the development and testing phases:
                            <br><br>
                            <strong>🚀 PHASE 1: Application Development</strong>
                            <span class="platform-tag arionflow">ArionFlow</span>
                            <br><br>
                            Building your expense approval workflow:
                            <br>
                            • Mobile expense submission with OCR receipt scanning<br>
                            • AI-powered expense categorization & policy validation<br>
                            • Dynamic approval routing (amount + department-based)<br>
                            • SAP integration for reimbursement processing<br>
                            • Real-time status tracking & notifications
                            <br><br>
                            <strong>🧪 PHASE 2: Testing Environment</strong>
                            <span class="platform-tag labs">Labs as a Service</span>
                            <br><br>
                            Simultaneously provisioning test infrastructure:
                            <br>
                            • Complete SAP testing environment (15 minutes)<br>
                            • Mobile device simulation farm<br>
                            • Load testing tools for approval workflows<br>
                            • Integration testing suite for ERP connectivity
                            <br><br>
                            Estimated timeline: Development ready in 2 hours, testing environment ready in 15 minutes. Should I proceed?
                            <div class="source-note">Source: ArionFlow workflow engine, Labs infrastructure manager, SAP integration templates</div>
                        </div>
                    </div>
                    
                    <div class="message user">
                        <div class="bubble">
                            Yes, and add compliance features for Sarbanes-Oxley requirements
                        </div>
                    </div>
                    
                    <div class="message ai">
                        <div class="bubble">
                            <strong>✅ Enhanced with SOX Compliance Features</strong>
                            <br><br>
                            <strong>📋 ArionFlow Enhancements Added:</strong>
                            <span class="platform-tag arionflow">ArionFlow</span>
                            <br>
                            • Segregation of duties enforcement<br>
                            • Immutable audit trail for all approvals<br>
                            • Automated expense policy compliance checks<br>
                            • Executive approval thresholds (>$5,000)<br>
                            • Quarterly compliance reporting automation
                            <br><br>
                            <strong>🔍 Testing Environment Enhanced:</strong>
                            <span class="platform-tag labs">Labs as a Service</span>
                            <br>
                            • SOX compliance validation tools<br>
                            • Audit trail verification systems<br>
                            • Role-based access testing scenarios<br>
                            • Compliance reporting test datasets
                            <br><br>
                            <strong>⚡ Automatic Integration:</strong><br>
                            Your ArionFlow application will automatically deploy to the testing environment once built. All compliance features will be validated against SOX requirements in the test lab.
                            <br><br>
                            <strong>📊 Testing Scenarios Ready:</strong><br>
                            • 1,000 simulated expense submissions<br>
                            • Multi-department approval workflow testing<br>
                            • SOX compliance audit simulation<br>
                            • SAP integration stress testing
                            <div class="source-note">Source: SOX compliance templates, audit testing frameworks, SAP test data generators</div>
                        </div>
                    </div>
                    
                    <div class="message user">
                        <div class="bubble">
                            Perfect! Can you also set up user training for the new system?
                        </div>
                    </div>
                    
                    <div class="message ai">
                        <div class="bubble">
                            <strong>🎓 Complete Training Program Activated</strong>
                            <br><br>
                            I'm setting up an integrated training environment that combines your new application with hands-on learning:
                            <br><br>
                            <strong>📱 Interactive Training Environment:</strong>
                            <span class="platform-tag labs">Labs as a Service</span>
                            <br>
                            • Dedicated training lab with your expense system<br>
                            • Safe sandbox environment for practice<br>
                            • Realistic expense scenarios and test data<br>
                            • Role-specific training paths (Employee, Manager, Finance)
                            <br><br>
                            <strong>🤖 AI-Powered Training Workflows:</strong>
                            <span class="platform-tag arionflow">ArionFlow</span>
                            <br>
                            • Adaptive learning paths based on user role<br>
                            • Interactive tutorials with guided practice<br>
                            • Progress tracking and competency assessment<br>
                            • Certification workflow upon completion
                            <br><br>
                            <strong>📅 Training Schedule:</strong><br>
                            • Manager training: Next Tuesday 2:00-4:00 PM<br>
                            • Employee training: Wednesday 10:00 AM-12:00 PM<br>
                            • Finance team training: Thursday 1:00-3:00 PM<br>
                            • Self-paced modules available 24/7
                            <br><br>
                            <strong>🎯 Complete Learning Ecosystem:</strong><br>
                            Your team will learn the new expense system in the same environment where it was tested, ensuring perfect familiarity before production rollout.
                            <br><br>
                            All training materials, lab access, and completion tracking have been configured. Training invitations will be sent within the hour!
                            <div class="source-note">Source: Training lab scheduler, learning management workflows, competency assessment tools</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="integration-section">
        <div class="container">
            <div style="text-align: center; margin-bottom: 60px;">
                <h2 style="font-size: 2.5em; font-weight: 800; color: #0f172a; margin-bottom: 20px;">Integrated Platform Capabilities</h2>
                <p style="font-size: 1.2em; color: #64748b;">Seamless orchestration between development and testing with AI-powered automation at every step</p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <h3>Conversational Development</h3>
                    <p>Build complex enterprise applications through natural language conversations. AI translates business requirements into complete, production-ready workflows with integrated testing plans.</p>
                </div>
                
                <div class="feature-card">
                    <h3>Instant Test Environments</h3>
                    <p>Testing infrastructure provisioned automatically as applications are built. No waiting for environments - test immediately with realistic enterprise data and scenarios.</p>
                </div>
                
                <div class="feature-card">
                    <h3>Intelligent Orchestration</h3>
                    <p>AI coordinates between development and testing phases, ensuring optimal resource allocation and seamless handoffs. Automated deployment pipelines with continuous validation.</p>
                </div>
                
                <div class="feature-card">
                    <h3>Enterprise Integration</h3>
                    <p>Native connectors for 200+ enterprise systems. Applications built with ArionFlow automatically integrate with testing environments that mirror your production infrastructure.</p>
                </div>
                
                <div class="feature-card">
                    <h3>Complete Auditability</h3>
                    <p>Full transparency from development through testing to deployment. Every decision, test result, and change is logged with AI-generated explanations for compliance requirements.</p>
                </div>
                
                <div class="feature-card">
                    <h3>Integrated Training</h3>
                    <p>Training environments automatically created alongside development and testing. Teams learn on the actual systems they'll use in production, accelerating adoption and reducing errors.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="cta-section" id="contact">
        <div class="container">
            <h2>Transform Your Innovation Pipeline</h2>
            <p>Join the exclusive pilot program for the world's first integrated enterprise development and testing platform. Build, test, and deploy applications faster than ever imagined.</p>
            <a href="#" class="btn">Apply for Innovation Pilot</a>
        </div>
    </div>

    <footer>
        <div class="container">
            <div class="logo">Enterprise Innovation Platform</div>
            <div class="family">Family of Arion Networks</div>
            <p style="color: #9ca3af; margin-bottom: 30px;">Complete development-to-deployment platform with AI-powered workflow creation and enterprise-grade testing infrastructure.</p>
            <div style="border-top: 1px solid #374151; padding-top: 30px;">
                <p style="color: #6b7280;">&copy; 2025 Arion Networks. All rights reserved.</p>
                <a href="https://arionetworks.com">Visit Arion Networks →</a>
            </div>
        </div>
    </footer>

    <style>
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>

</body>
</html>