import 'package:get_it/get_it.dart';
import '../network/api_client.dart';
import '../services/session_service.dart';
import '../services/config_service.dart';
import '../../domain/repositories/auth_repository.dart' as domain_auth;
import '../../data/repositories/auth_repository.dart';
import '../../domain/repositories/demo_access_repository.dart'
    as domain_demo_access;
import '../../data/repositories/demo_access_repository.dart';
import '../../domain/repositories/chat_repository.dart';
import '../../domain/repositories/document_repository.dart';
import '../../data/repositories/chat_repository_impl.dart';
import '../../data/repositories/document_repository_impl.dart';
import '../services/compliance_filter.dart';
import '../../domain/repositories/admin_repository.dart' as domain;
import '../../data/repositories/admin_repository.dart';
import '../../domain/repositories/feedback_repository.dart';
import '../../data/datasources/feedback_local_ds.dart';
import '../../data/repositories/feedback_repository_impl.dart';

final GetIt locator = GetIt.instance;

void setupLocator() {
  if (locator.isRegistered<ApiClient>()) return;

  locator.registerLazySingleton<ConfigService>(() => ConfigService());
  locator.registerLazySingleton<ApiClient>(() => ApiClient(locator()));
  locator.registerLazySingleton<SessionService>(() => SessionService());

  locator.registerLazySingleton<domain_auth.AuthRepository>(
    () => AuthRepositoryImpl(
      locator<ApiClient>(),
      locator<ConfigService>(),
      locator<SessionService>(),
    ),
  );
  locator.registerLazySingleton<domain_demo_access.DemoAccessRepository>(
    () => DemoAccessRepositoryImpl(
      locator<ApiClient>(),
      locator<ConfigService>(),
      locator<SessionService>(),
    ),
  );
  locator.registerLazySingleton<ChatRepository>(
    () => ChatRepositoryImpl(
      locator<ApiClient>(),
      locator<ConfigService>(),
      locator<SessionService>(),
    ),
  );
  locator.registerLazySingleton<DocumentRepository>(
    () => DocumentRepositoryImpl(
      locator<ApiClient>(),
      locator<ConfigService>(),
      locator<SessionService>(),
    ),
  );
  locator.registerLazySingleton<ComplianceFilterService>(
    () => ComplianceFilterService(),
  );
  locator.registerLazySingleton<domain.AdminRepository>(
    () => AdminRepositoryImpl(locator<ApiClient>(), locator<ConfigService>()),
  );
  // Feedback
  locator.registerLazySingleton<FeedbackLocalDataSource>(
    () => FeedbackLocalDataSource(),
  );
  locator.registerLazySingleton<FeedbackRepository>(
    () => FeedbackRepositoryImpl(locator<FeedbackLocalDataSource>()),
  );
}
