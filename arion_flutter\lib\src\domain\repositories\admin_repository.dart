abstract class AdminRepository {
  Future<Map<String, dynamic>> requestCode({
    required String password,
    String? email,
  });
  Future<Map<String, dynamic>> verifyCode({required String code});
  Future<Map<String, dynamic>> verifySession({required String token});
  Future<Map<String, dynamic>> getUsers();
  Future<Map<String, dynamic>> getLeads();
  Future<Map<String, dynamic>> adminManageUser({
    required String adminToken,
    required String userId,
    required String userAction,
    required String userType,
  });
  Future<Map<String, dynamic>> cleanupExpiredData({int daysOld = 30});
}
