import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../domain/repositories/admin_repository.dart';

part 'admin_state.dart';

class AdminCubit extends Cubit<AdminState> {
  final AdminRepository repo;
  AdminCubit(this.repo) : super(const AdminState());

  void setTab(int i) => emit(state.copyWith(tabIndex: i));
  void backToPassword() => emit(state.copyWith(stepMfa: false));

  Future<void> requestCode({
    required String email,
    required String password,
  }) async {
    emit(state.copyWith(busy: true, clearError: true));
    try {
      final res = await repo.requestCode(email: email, password: password);
      if (res['success'] == true) {
        emit(state.copyWith(stepMfa: true));
      } else {
        emit(
          state.copyWith(
            error: (res['error'] ?? 'Invalid admin credentials').toString(),
          ),
        );
      }
    } catch (e) {
      emit(state.copyWith(error: 'Network error'));
    } finally {
      emit(state.copyWith(busy: false));
    }
  }

  Future<void> verifyCode(String code) async {
    emit(state.copyWith(busy: true, clearError: true));
    try {
      final res = await repo.verifyCode(code: code);
      if (res['success'] == true) {
        final token = res['data']?['token'] as String?;
        emit(state.copyWith(adminToken: token));
        await loadInitialData();
      } else {
        emit(
          state.copyWith(error: (res['error'] ?? 'Invalid code').toString()),
        );
      }
    } catch (e) {
      emit(state.copyWith(error: 'Network error'));
    } finally {
      emit(state.copyWith(busy: false));
    }
  }

  Future<void> loadInitialData() async {
    emit(state.copyWith(busy: true, clearError: true));
    try {
      final users = await repo.getUsers();
      final leads = await repo.getLeads();
      emit(state.copyWith(usersData: users['data'], leadsData: leads['data']));
    } catch (e) {
      emit(state.copyWith(error: 'Failed to load admin data'));
    } finally {
      emit(state.copyWith(busy: false));
    }
  }

  Future<void> cleanup() async {
    emit(state.copyWith(busy: true));
    try {
      await repo.cleanupExpiredData();
    } catch (_) {}
    emit(state.copyWith(busy: false));
  }

  Future<void> manageUser(Map u, String action) async {
    final token = state.adminToken;
    if (token == null) return;
    emit(state.copyWith(busy: true));
    try {
      await repo.adminManageUser(
        adminToken: token,
        userId: (u['id']).toString(),
        userAction: action,
        userType: (u['type']).toString(),
      );
      await loadInitialData();
    } catch (_) {
    } finally {
      emit(state.copyWith(busy: false));
    }
  }

  void logout() {
    emit(const AdminState());
  }
}
