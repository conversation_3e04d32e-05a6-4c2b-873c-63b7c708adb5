# 🖼️ ArionComply Assistant UI Design – Public Demo

**Version:** v1.0  
**Module:** Assistant Frontend (HTML/CSS/JS)  
**Date:** 2025-07-13  
**Purpose:** Define the UI structure, layout, behavior, and responsiveness of the AI assistant widget used in the public-facing ArionComply demo. Focus is on clarity, responsiveness, and integration with Supabase-powered backend.

---

## 🎯 Objectives

- Provide a floating, accessible, and responsive assistant interface
- Support goal-based chat flow with onboarding inputs
- Show suggested actions and preview document triggers
- Avoid user confusion or dead-ends
- No need for React or frontend frameworks

---

## 🧩 UI Components Overview

| Component          | Description                                |
|-------------------|--------------------------------------------|
| **Chat Launcher** | Floating button (bottom-right corner)      |
| **Chat Modal**    | Full interaction panel with message thread |
| **Message Bubbles** | User & assistant messages, markdown/rendered |
| **Input Area**    | Text input, send button, mic icon (optional) |
| **System Prompts**| Assistant hints or follow-up suggestions   |
| **Quick Replies** | Clickable options (if suggested)           |
| **Preview CTA**   | "Your preview is ready" button              |
| **Lead Modal**    | Contact form (shown conditionally)         |

---

## 🧱 Layout Zones

```
_______________________________
|  ArionComply Guide 🧠         | ← Header (titlebar + close X)
|------------------------------|
| [👤] Hi! What can you help me with?       |
| [🤖] I can help you with things like...   |
| [🤖] Want to start with ISO 27001?        |
|-----------------------------------------|
| ⬜ "Get ISO certified"  ⬜ "Show GDPR docs"| ← Quick reply buttons
|-----------------------------------------|
| [🔤 Type your question…      ][Send ▶️]   | ← Input bar
|_________________________________________|
```

---

## 📱 Responsive Behavior

| Screen Type | Behavior |
|-------------|----------|
| **Desktop** | Chat modal centered or anchored to bottom-right; 400–500px wide |
| **Tablet**  | Full height modal, same width |
| **Mobile**  | Full-screen takeover modal; vertical scrolling for chat |

---

## 🎨 Style Guide

| Element            | Style |
|--------------------|-------|
| **Assistant Bubble** | Light brand color background, left-aligned |
| **User Bubble**      | Neutral or gray background, right-aligned |
| **Quick Replies**    | Pill-style buttons, hover effect |
| **Send Button**      | Icon (▶️), disabled when input is empty |
| **Preview CTA**      | Primary button, sticky or in-chat |
| **Fonts**            | Sans-serif, 16px base |
| **Accessibility**    | 4.5:1 contrast ratio, keyboard navigation supported |

---

## 📐 Component Dimensions

| Component          | Width       | Notes            |
|-------------------|-------------|------------------|
| Floating Button   | 56px × 56px | Fixed, bottom-right |
| Modal Width       | 400–480px   | Responsive       |
| Modal Height      | 500–100vh   | Scroll if needed |
| Input Bar Height  | 50–60px     | Sticky bottom    |
| Chat Bubbles       | Max 80% width | Auto-size content |

---

## 🔧 UX Features

- Starts with a greeting + suggestion buttons
- Scrollback is preserved during session
- Input is disabled while assistant is responding
- Optional: animated typing indicator (e.g., "...typing")
- Optional: markdown rendering in assistant replies
- Optional: voice input button for Whisper support

---

## ✅ Behavioral Summary

| Trigger                 | Result                                      |
|-------------------------|---------------------------------------------|
| User clicks launcher    | Opens modal with welcome message            |
| User sends message      | Bubble appears + request sent to backend    |
| Assistant replies       | Shows with delay, may include quick replies |
| Preview is ready        | Button shown or in-message link offered     |
| No user input (30s)     | Assistant offers help or suggestions        |
| User closes modal       | Session scroll and state preserved          |

---

## 📌 Implementation Notes

- Pure HTML/CSS/JS — no React required
- Modal controlled via `display: none | block` or JS class toggle
- Message thread is a scrollable div inside modal
- Input events: `Enter` to send, optional send button
- Session ID stored in memory (or cookie/localStorage if needed)