import 'package:flutter/material.dart';

class FeatureShowcase extends StatelessWidget {
  final int currentIndex;
  final ValueChanged<int> onDotTap;
  const FeatureShowcase({super.key, required this.currentIndex, required this.onDotTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [BoxShadow(color: Color(0x26000000), blurRadius: 12, offset: Offset(0, 6))],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          const Text('See ArionComply in Action', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
          const SizedBox(height: 12),
          _Slide(index: 0, active: currentIndex == 0,
            title: '🏆 ISO 27001 Certification',
            body: 'Generate risk registers, policies, and implementation roadmaps automatically. Our AI creates tailored documentation that passes audits.',
            previewHeader: 'Risk Register Preview',
            lines: const ['📋 Data Breach Risk - High Priority', '🔍 Access Control Gap - Medium', '☁️ Cloud Security Review - Low'],
          ),
          _Slide(index: 1, active: currentIndex == 1,
            title: '🛡️ GDPR Compliance',
            body: 'Automated ROPA, Data Protection Impact Assessments, and privacy policy generation. Stay compliant with evolving data protection regulations.',
            previewHeader: 'GDPR Assessment',
            lines: const ['✅ Data mapping complete', '🔍 Consent mechanisms reviewed', '📊 Privacy impact assessed'],
          ),
          _Slide(index: 2, active: currentIndex == 2,
            title: '🤖 AI Governance',
            body: 'Stay ahead of AI regulations with comprehensive governance frameworks. Risk assessments, ethical guidelines, and EU AI Act compliance tools.',
            previewHeader: 'AI Risk Dashboard',
            lines: const ['🎯 High-risk AI systems: 2', '⚖️ Bias assessment: Pending', '📊 Compliance score: 85%'],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(3, (i) => GestureDetector(
              onTap: () => onDotTap(i),
              child: Container(
                width: 12,
                height: 12,
                margin: const EdgeInsets.symmetric(horizontal: 6),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: currentIndex == i ? const Color(0xFF059669) : const Color(0xFFCBD5E1),
                ),
              ),
            )),
          )
        ],
      ),
    );
  }
}

class _Slide extends StatelessWidget {
  final int index;
  final bool active;
  final String title;
  final String body;
  final String previewHeader;
  final List<String> lines;
  const _Slide({required this.index, required this.active, required this.title, required this.body, required this.previewHeader, required this.lines});

  @override
  Widget build(BuildContext context) {
    if (!active) return const SizedBox.shrink();
    return LayoutBuilder(
      builder: (_, constraints) {
        final isNarrow = constraints.maxWidth < 700;
        final column = Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Color(0xFF1E293B))),
            const SizedBox(height: 8),
            Text(body, style: const TextStyle(fontSize: 14, height: 1.4, color: Color(0xFF475569))),
            const SizedBox(height: 8),
            Wrap(spacing: 8, runSpacing: 8, children: [
              _Tag(text: index == 0 ? '3-6 months faster' : index == 1 ? 'Auto ROPA' : 'EU AI Act ready'),
              _Tag(text: index == 0 ? 'Audit-ready docs' : index == 1 ? 'DPIA templates' : 'Risk assessment'),
              _Tag(text: index == 0 ? 'Expert guidance' : index == 1 ? 'Legal updates' : 'Ethics framework'),
            ])
          ],
        );
        final preview = Container(
          decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(12), border: Border.all(color: const Color(0xFFE2E8F0)), boxShadow: const [BoxShadow(color: Color(0x1F000000), blurRadius: 10, offset: Offset(0, 8))]),
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(previewHeader, style: const TextStyle(color: Color(0xFF059669), fontWeight: FontWeight.w600, fontSize: 13)),
              const SizedBox(height: 12),
              ...lines.map((l) => Container(
                width: double.infinity,
                margin: const EdgeInsets.symmetric(vertical: 4),
                padding: const EdgeInsets.all(8),
                decoration: const BoxDecoration(color: Color(0xFFF8FAFC), borderRadius: BorderRadius.all(Radius.circular(6))),
                child: Text(l, style: const TextStyle(fontSize: 13, color: Color(0xFF334155))),
              ))
            ],
          ),
        );
        if (isNarrow) {
          return Column(children: [column, const SizedBox(height: 16), preview]);
        }
        return Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Expanded(child: column),
          const SizedBox(width: 24),
          SizedBox(width: 320, child: preview),
        ]);
      },
    );
  }
}

class _Tag extends StatelessWidget {
  final String text;
  const _Tag({required this.text});
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: const BoxDecoration(color: Color(0xFFECFDF5), borderRadius: BorderRadius.all(Radius.circular(20))),
      child: Text(text, style: const TextStyle(color: Color(0xFF059669), fontSize: 12, fontWeight: FontWeight.w500)),
    );
  }
}


