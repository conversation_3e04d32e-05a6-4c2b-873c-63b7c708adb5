import '../../core/network/api_client.dart';
import '../../core/services/config_service.dart';
import '../../core/services/session_service.dart';
import '../../domain/repositories/chat_repository.dart';

class ChatRepositoryImpl implements ChatRepository {
  final ApiClient _api;
  final ConfigService _config;
  final SessionService _session;

  ChatRepositoryImpl(this._api, this._config, this._session);

  @override
  Future<Map<String, dynamic>> queryAgent(
    String message, {
    Map<String, dynamic>? context,
  }) async {
    await _config.load();
    final sessionId = await _ensureSession();
    final action = _config.actions['queryAgent'] ?? 'query-agent';
    try {
      final res = await _api.post(action, {
        'message': message,
        'timestamp': DateTime.now().toIso8601String(),
        ...?context,
      }, sessionId: sessionId);
      return Map<String, dynamic>.from(res.data ?? {});
    } catch (_) {
      // Fallback to query-rag if agent fails
      final ragAction = _config.actions['queryRag'] ?? 'query-rag';
      final res = await _api.post(ragAction, {
        'message': message,
      }, sessionId: sessionId);
      return Map<String, dynamic>.from(res.data ?? {});
    }
  }

  Future<String> _ensureSession() async {
    var sessionId = await _session.getSessionId();
    if (sessionId == null) {
      final action = _config.actions['initSession'] ?? 'init-session';
      final res = await _api.post(action, {
        'timestamp': DateTime.now().toIso8601String(),
        'user_type': 'regular',
      });
      final data = Map<String, dynamic>.from(res.data ?? {});
      sessionId =
          (data['sessionId'] ?? data['id'] ?? data['data']?['sessionId'] ?? '')
              as String;
      if (sessionId.isNotEmpty) {
        await _session.saveSessionId(sessionId);
      }
    }
    return sessionId;
  }
}
