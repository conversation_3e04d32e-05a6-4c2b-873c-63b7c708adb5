import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import '../services/config_service.dart';

class ApiClient {
  final ConfigService _configService;
  final Dio _dio = Dio();
  final Logger _logger = Logger();

  ApiClient(this._configService) {
    _dio.options = BaseOptions(
      connectTimeout: const Duration(milliseconds: 30000),
      receiveTimeout: const Duration(milliseconds: 30000),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-API-Key': 'arion-demo-2025',
      },
    );
  }

  Future<Response<dynamic>> post(
    String action,
    Map<String, dynamic> data, {
    String? sessionId,
  }) async {
    final baseUrl = _configService.apiBaseUrl;
    final endpoint = _configService.apiEndpoint;
    final url = '$baseUrl$endpoint';

    final body = {
      'action': action,
      if (sessionId != null) 'sessionId': sessionId,
      ...data,
    };

    final headers = Map<String, dynamic>.from(_dio.options.headers);
    final anonKey = _configService.supabaseAnonKey;
    if (anonKey != null) {
      headers['Authorization'] = 'Bearer $anonKey';
    }
    if (sessionId != null) headers['X-Session-ID'] = sessionId;

    try {
      _logger.d('POST $url action=$action');
      return await _dio.post(
        url,
        data: body,
        options: Options(headers: headers),
      );
    } on DioException catch (e) {
      _logger.e('Api error', error: e, stackTrace: e.stackTrace);
      rethrow;
    }
  }
}
