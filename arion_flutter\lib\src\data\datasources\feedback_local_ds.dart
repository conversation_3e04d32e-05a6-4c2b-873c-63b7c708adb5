import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class FeedbackLocalDataSource {
  static const key = 'arion-feedback-data';

  Future<List<dynamic>> _load() async {
    final prefs = await SharedPreferences.getInstance();
    final raw = prefs.getString(key);
    return raw != null ? jsonDecode(raw) as List<dynamic> : <dynamic>[];
  }

  Future<void> _save(List<dynamic> list) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(key, jsonEncode(list));
  }

  Future<void> insert(Map<String, dynamic> item) async {
    final list = await _load();
    list.add(item);
    await _save(list);
  }
}
