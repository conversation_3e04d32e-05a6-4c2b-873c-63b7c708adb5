# Database Implementation Guide

**Version:** v1.0  
**Date:** 2025-07-14  
**Purpose:** Step-by-step guide to implement the ArionComply demo database schema

---

## 📋 Implementation Checklist

### ✅ Pre-Implementation Steps

1. **Review Documents Created:**
   - [ ] Data Models Specification (entity definitions, relationships)
   - [ ] Workflow Documentation (complete user journey flows)
   - [ ] Migration SQL Files (ready-to-execute schema)

2. **Supabase Setup:**
   - [ ] Supabase project created
   - [ ] Database access confirmed
   - [ ] SQL Editor accessible

---

## 🚀 Database Implementation Steps

### Step 1: Execute Core Migration
**Location:** Supabase SQL Editor  
**File:** Use the complete migration SQL provided

```sql
-- Copy and paste the entire migration file into Supabase SQL Editor
-- This includes:
-- ✅ 6 main tables with proper relationships
-- ✅ Performance indexes 
-- ✅ Utility functions and triggers
-- ✅ Enhanced Row Level Security policies
-- ✅ Analytics views
-- ✅ Cleanup functions
-- ✅ Sample data for testing
```

### Step 2: Create Single Router Edge Function
**Location:** Supabase Functions  
**Command:** `supabase functions new api`

```bash
# Create the router function
supabase functions new api

# Copy the router implementation to functions/api/index.ts
# This handles ALL API calls and CORS in one place
```

### Step 3: Deploy Router Function
```bash
# Deploy the router function
supabase functions deploy api

# Set environment variables
supabase secrets set OPENAI_API_KEY=your_key_here
supabase secrets set IP_HASH_SALT=your_unique_salt_here
```

## ⚡ Quick Migration Verification

**Run this single query to verify everything worked:**

```sql
-- One-query verification check
SELECT 
  'Tables Created' as check_type,
  COUNT(*) as expected_6,
  COUNT(*) = 6 as success
FROM information_schema.tables 
WHERE table_schema = 'public' AND table_name LIKE 'demo_%'

UNION ALL

SELECT 
  'Sample Data',
  (SELECT COUNT(*) FROM demo_sessions),
  (SELECT COUNT(*) FROM demo_sessions) = 3

UNION ALL

SELECT 
  'Functions Created',
  COUNT(*),
  COUNT(*) >= 6
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND routine_name IN ('calculate_intake_completion', 'calculate_engagement_score', 'cleanup_old_sessions', 'refresh_analytics', 'update_intake_completion_percentages', 'update_session_timestamp')

UNION ALL

SELECT 
  'Analytics Views',
  COUNT(*),
  COUNT(*) = 2
FROM information_schema.views 
WHERE table_schema = 'public' 
  AND table_name IN ('session_analytics', 'document_analytics');
```

**Expected Result - ALL `success` columns should be `true`:**
```
check_type        | expected_6 | success
------------------|------------|--------
Tables Created    |          6 | true
Sample Data       |          3 | true  
Functions Created |          9 | true    -- Updated: now includes security functions
Analytics Views   |          2 | true
```

**✅ If ALL show `success = true`, your migration was completely successful!**

**Additional Security Verification:**
```sql
-- Verify enhanced security features
SELECT 
    'Security Features' as check_type,
    CASE 
        WHEN EXISTS (SELECT 1 FROM demo_sessions WHERE ip_address_hash IS NOT NULL) 
        AND EXISTS (SELECT 1 FROM demo_contacts WHERE data_processing_consented IS NOT NULL)
        THEN 'Enabled' 
        ELSE 'Missing' 
    END as status;
```

**Expected Result:**
```
check_type        | status
------------------|--------
Security Features | Enabled
```

**❌ If ANY show `success = false`, see the troubleshooting section below.**
Run this verification query:

```sql
-- Check all tables were created
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name LIKE 'demo_%'
ORDER BY table_name;
```

**Expected Result:** You should see exactly 6 tables:
```
table_name           | table_type
---------------------|------------
demo_browsing_logs   | BASE TABLE
demo_contacts        | BASE TABLE  
demo_documents       | BASE TABLE
demo_intake_data     | BASE TABLE
demo_interactions    | BASE TABLE
demo_sessions        | BASE TABLE
```

**Check sample data:**
```sql
SELECT COUNT(*) as sessions FROM demo_sessions;
SELECT COUNT(*) as intake_records FROM demo_intake_data;
```

**Expected Result:**
```
sessions: 3  (3 sample sessions were inserted)
intake_records: 3  (3 sample intake records)
```

### Step 3: Test Database Functions
```sql
-- Test intake completion calculation
SELECT calculate_intake_completion(
    'iso_certification', 
    'small', 
    'technology', 
    'cloud', 
    ARRAY['customer_data'], 
    ARRAY['password_policy'], 
    '{}'::jsonb
) as completion_score;
```

**Expected Result:**
```
completion_score: 1.00
```
This means 100% completion (all 5 required fields for ISO certification were provided).

**Test analytics views:**
```sql
SELECT * FROM session_analytics LIMIT 5;
```

**Expected Result:**
```
date       | goal_detected      | total_sessions | completed_intake | generated_docs | preview_ready | captured_leads | avg_session_duration_seconds | goal_detected_count
-----------|-------------------|----------------|------------------|----------------|---------------|----------------|----------------------------|--------------------
2025-07-14 | ai_risk_management|              1 |                0 |              0 |             0 |              0 |                          0 |                   1
2025-07-14 | gdpr_audit        |              1 |                0 |              0 |             0 |              0 |                          0 |                   1  
2025-07-14 | iso_certification |              1 |                0 |              0 |             0 |              0 |                          0 |                   1
```

**Check available functions:**
```sql
SELECT routine_name 
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND (routine_name LIKE '%demo%' OR routine_name IN ('calculate_intake_completion', 'calculate_engagement_score', 'cleanup_old_sessions', 'refresh_analytics'))
ORDER BY routine_name;
```

**Expected Result:**
```
routine_name
--------------------------------
calculate_engagement_score
calculate_intake_completion  
cleanup_old_sessions
refresh_analytics
update_intake_completion_percentages
update_session_timestamp
```

---

## 🔧 Post-Implementation Configuration

### Environment Variables for Edge Functions
Create these in your Supabase project:

```bash
# Database connection (auto-configured in Supabase)
DATABASE_URL=postgresql://[auto-configured]

# API Configuration  
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here  # if using Claude

# Security
JWT_SECRET=your_supabase_jwt_secret
ALLOWED_ORIGINS=https://iso.arionetworks.com,http://localhost:3000
IP_HASH_SALT=arion-demo-salt-2025  # Change this in production

# GDPR Compliance
DEFAULT_DATA_RETENTION_DAYS=730  # 2 years
CLEANUP_SCHEDULE=daily
```

### Enhanced Contact Form Requirements

**Frontend Update Required:**
Your contact form needs these fields to match the new schema:

```html
<!-- Updated contact capture form -->
<form id="enhanced-contact-form">
    <!-- Required Contact Information -->
    <input type="text" name="first_name" required placeholder="First Name" maxlength="100">
    <input type="text" name="last_name" required placeholder="Last Name" maxlength="100">
    <input type="email" name="email" required placeholder="Email Address" maxlength="254">
    <input type="text" name="company_name" required placeholder="Company Name" maxlength="200">
    
    <!-- Optional but Valuable -->
    <input type="tel" name="phone_number" placeholder="Phone (+1234567890)" pattern="^\+?[1-9]\d{1,14}$">
    <input type="text" name="job_title" placeholder="Job Title" maxlength="100">
    <select name="interest_area">
        <option value="">Select Primary Interest</option>
        <option value="iso_certification">ISO 27001 Certification</option>
        <option value="gdpr_audit">GDPR Compliance</option>
        <option value="ai_risk_management">AI Risk Management</option>
        <option value="soa_generation">Statement of Applicability</option>
        <option value="general">General Compliance</option>
    </select>
    <textarea name="message" placeholder="Additional comments or questions" maxlength="1000"></textarea>
    
    <!-- GDPR Consent (Required) -->
    <div class="consent-section">
        <label class="required">
            <input type="checkbox" name="data_processing_consented" required>
            I consent to the processing of my personal data for demo and follow-up purposes. 
            <a href="/privacy" target="_blank">Privacy Policy</a>
        </label>
        
        <label>
            <input type="checkbox" name="marketing_consented">
            I would like to receive marketing communications about ArionComply products and services.
        </label>
        
        <small class="retention-notice">
            Your data will be retained for 2 years. You can request deletion at any time <NAME_EMAIL>
        </small>
    </div>
</form>
```

### Row Level Security Notes
Current RLS policies allow full access for demo purposes. For production, consider:

```sql
-- More restrictive session access (example)
CREATE POLICY "Users access own sessions via JWT" ON demo_sessions
    FOR ALL USING (
        id = (current_setting('request.headers')::json->>'x-session-id')::uuid
    );
```

---

## 📊 Database Schema Summary

| Table | Purpose | Key Features |
|-------|---------|--------------|
| `demo_sessions` | User session tracking | Goal detection, progress flags |
| `demo_interactions` | Chat messages | User/assistant conversations |
| `demo_intake_data` | Onboarding responses | Goal-specific questionnaire data |
| `demo_documents` | Generated documents | Compliance docs for preview |
| `demo_contacts` | Lead capture | Contact info + engagement scoring |
| `demo_browsing_logs` | User behavior | Page views, time tracking |

---

## 🔄 Next Steps After Database Setup

### Immediate Tasks (Backend Development)
1. **Create Edge Functions Structure:**
   ```bash
   supabase functions new init-session
   supabase functions new query-agent  
   supabase functions new generate-documents
   supabase functions new save-contact
   ```

2. **Vector Database Setup:**
   - Enable Supabase Vector/pgvector extension
   - Chunk compliance content (ISO 27001, GDPR guides)
   - Generate embeddings for RAG functionality

3. **LLM Integration:**
   - Choose provider (OpenAI recommended for stability)
   - Set up streaming responses
   - Implement goal detection patterns

### Frontend Integration Points
The database is designed to work with your existing frontend structure:

```javascript
// Frontend will call these endpoints:
POST /functions/v1/init-session        // Creates demo_sessions entry
POST /functions/v1/query-agent         // Inserts demo_interactions  
POST /functions/v1/generate-documents  // Creates demo_documents
GET  /functions/v1/get-documents       // Reads demo_documents
POST /functions/v1/save-contact        // Creates demo_contacts
```

---

## 🔍 Monitoring & Maintenance

### Daily Maintenance (Automated)
```sql
-- Set up as a cron job or scheduled function
SELECT cleanup_old_sessions();
SELECT refresh_analytics();
SELECT update_intake_completion_percentages();
```

### Key Metrics to Monitor
- Session completion rates by goal type
- Document generation success rates  
- Lead conversion rates
- Average session duration
- API response times

### Performance Optimization
- Monitor slow queries via Supabase dashboard
- Consider additional indexes based on usage patterns
- Archive old data based on retention policies

---

## 🚨 Troubleshooting Common Issues

### ❌ If You Don't See 6 Tables
**Problem:** Query returns fewer than 6 tables  
**Solution:** 
```sql
-- Check what tables exist
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';

-- If migration failed partway, drop everything and restart:
DROP TABLE IF EXISTS demo_browsing_logs CASCADE;
DROP TABLE IF EXISTS demo_contacts CASCADE;  
DROP TABLE IF EXISTS demo_documents CASCADE;
DROP TABLE IF EXISTS demo_intake_data CASCADE;
DROP TABLE IF EXISTS demo_interactions CASCADE;
DROP TABLE IF EXISTS demo_sessions CASCADE;
DROP MATERIALIZED VIEW IF EXISTS session_analytics CASCADE;
DROP MATERIALIZED VIEW IF EXISTS document_analytics CASCADE;

-- Then re-run the complete migration file
```

### ❌ If Sample Data Count is 0
**Problem:** `SELECT COUNT(*) FROM demo_sessions` returns 0  
**Likely Cause:** Migration ran but sample data section failed  
**Solution:**
```sql
-- Insert sample data manually:
INSERT INTO demo_sessions (id, goal_detected, goal_confidence, session_tags) VALUES
    ('00000000-0000-0000-0000-000000000001', 'iso_certification', 0.95, ARRAY['sample', 'test']),
    ('00000000-0000-0000-0000-000000000002', 'gdpr_audit', 0.88, ARRAY['sample', 'test']),
    ('00000000-0000-0000-0000-000000000003', 'ai_risk_management', 0.92, ARRAY['sample', 'test']);

INSERT INTO demo_intake_data (session_id, goal_type, company_size, industry, completion_percentage) VALUES
    ('00000000-0000-0000-0000-000000000001', 'iso_certification', '11-50', 'Technology', 0.8),
    ('00000000-0000-0000-0000-000000000002', 'gdpr_audit', '51-200', 'Healthcare', 0.6),
    ('00000000-0000-0000-0000-000000000003', 'ai_risk_management', '201-500', 'Financial Services', 0.9);
```

### ❌ If Functions Don't Exist
**Problem:** Function queries return errors  
**Solution:**
```sql
-- Check if functions were created:
SELECT proname FROM pg_proc WHERE proname LIKE 'calculate_%';

-- If missing, re-run just the function section from Migration 3
```

### ❌ If Analytics Views are Empty
**Problem:** `SELECT * FROM session_analytics` returns no rows  
**Expected:** This is normal if no real sessions exist yet  
**To Test:** Create a test session:
```sql
INSERT INTO demo_sessions (goal_detected) VALUES ('test_goal');
SELECT refresh_analytics();
SELECT * FROM session_analytics WHERE goal_detected = 'test_goal';
```

### ❌ Permission Issues
**Problem:** "permission denied for table" errors  
**Solution:**
```sql
-- Check RLS status
SELECT tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename LIKE 'demo_%';

-- All should show rowsecurity = true
-- If not, re-run Migration 4 (RLS section)
```

---

## ✅ Implementation Validation

After completing the database setup, verify these work:

- [ ] Can create new session in `demo_sessions`
- [ ] Can insert chat interactions in `demo_interactions`  
- [ ] Can store intake data in `demo_intake_data`
- [ ] Can generate documents in `demo_documents`
- [ ] Can capture leads in `demo_contacts`
- [ ] Can log browsing in `demo_browsing_logs`
- [ ] Analytics views return data
- [ ] Cleanup functions execute without errors

---

## 📞 Support

If you encounter issues during implementation:

1. Check Supabase logs for specific error messages
2. Verify all foreign key relationships are properly created
3. Ensure RLS policies don't block necessary operations
4. Test with sample data before connecting frontend

The database schema is now production-ready and optimized for the ArionComply demo workflow!