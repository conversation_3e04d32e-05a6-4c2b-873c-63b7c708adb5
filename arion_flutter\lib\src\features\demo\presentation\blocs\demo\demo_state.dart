part of 'demo_cubit.dart';

enum DemoView { chat, documents, viewer }

class DemoMessage extends Equatable {
  final String sender; // 'user' or 'assistant' or 'assistant_rejection'
  final String text;
  final List<String> quickReplies;
  final Map<String, dynamic>? rejection; // for compliance rejection

  const DemoMessage({
    required this.sender,
    required this.text,
    this.quickReplies = const [],
    this.rejection,
  });
  factory DemoMessage.user(String text) =>
      DemoMessage(sender: 'user', text: text);
  factory DemoMessage.assistant(
    String text, {
    List<String> quickReplies = const [],
  }) =>
      DemoMessage(sender: 'assistant', text: text, quickReplies: quickReplies);
  factory DemoMessage.assistantRejection(Map<String, dynamic> rej) =>
      DemoMessage(
        sender: 'assistant_rejection',
        text: (rej['title'] ?? 'Outside Our Expertise') as String,
        rejection: rej,
      );

  @override
  List<Object?> get props => [sender, text, quickReplies, rejection];
}

class DemoState extends Equatable {
  final DemoView view;
  final List<DemoMessage> messages;
  final bool waiting;
  final bool infoOpen;
  final bool welcomeClosed;
  final bool suggestionsHidden;
  final bool isAdmin;
  final int sessionCount;
  final int sessionLimit;
  final bool limitReached;
  final String? limitType;
  final List<Map<String, dynamic>> documents;
  final Map<String, dynamic>? currentDocument;
  final int documentsViewed;
  final bool leadCaptureOpen;

  const DemoState({
    required this.view,
    required this.messages,
    required this.waiting,
    required this.infoOpen,
    required this.welcomeClosed,
    required this.suggestionsHidden,
    required this.isAdmin,
    required this.sessionCount,
    required this.sessionLimit,
    required this.limitReached,
    required this.limitType,
    required this.documents,
    required this.currentDocument,
    required this.documentsViewed,
    required this.leadCaptureOpen,
  });

  const DemoState.initial()
    : this(
        view: DemoView.chat,
        messages: const [],
        waiting: false,
        infoOpen: false,
        welcomeClosed: false,
        suggestionsHidden: false,
        isAdmin: false,
        sessionCount: 0,
        sessionLimit: 12,
        limitReached: false,
        limitType: null,
        documents: const [],
        currentDocument: null,
        documentsViewed: 0,
        leadCaptureOpen: false,
      );

  DemoState copyWith({
    DemoView? view,
    List<DemoMessage>? messages,
    bool? waiting,
    bool? infoOpen,
    bool? welcomeClosed,
    bool? suggestionsHidden,
    bool? isAdmin,
    int? sessionCount,
    int? sessionLimit,
    bool? limitReached,
    String? limitType,
    List<Map<String, dynamic>>? documents,
    Map<String, dynamic>? currentDocument,
    int? documentsViewed,
    bool? leadCaptureOpen,
  }) {
    return DemoState(
      view: view ?? this.view,
      messages: messages ?? this.messages,
      waiting: waiting ?? this.waiting,
      infoOpen: infoOpen ?? this.infoOpen,
      welcomeClosed: welcomeClosed ?? this.welcomeClosed,
      suggestionsHidden: suggestionsHidden ?? this.suggestionsHidden,
      isAdmin: isAdmin ?? this.isAdmin,
      sessionCount: sessionCount ?? this.sessionCount,
      sessionLimit: sessionLimit ?? this.sessionLimit,
      limitReached: limitReached ?? this.limitReached,
      limitType: limitType ?? this.limitType,
      documents: documents ?? this.documents,
      currentDocument: currentDocument ?? this.currentDocument,
      documentsViewed: documentsViewed ?? this.documentsViewed,
      leadCaptureOpen: leadCaptureOpen ?? this.leadCaptureOpen,
    );
  }

  @override
  List<Object?> get props => [
    view,
    messages,
    waiting,
    infoOpen,
    welcomeClosed,
    suggestionsHidden,
    isAdmin,
    sessionCount,
    sessionLimit,
    limitReached,
    limitType,
    documents,
    currentDocument,
    documentsViewed,
    leadCaptureOpen,
  ];
}
