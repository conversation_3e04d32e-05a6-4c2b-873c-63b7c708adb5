# 🚀 Quick Setup Guide - Product Registration System

This guide gets your product-based registration system up and running fast.

---

## 🌊 User Flow - What You're Building

### The Complete Journey

1. **User visits your product page** (e.g., `iso.arionetworks.com`)
2. **User clicks "Join <PERSON>onComply Pilot" button**
3. **Registration form opens** with ArionComply colors, logo, and messaging
4. **Form automatically knows** they're interested in ArionComply Pilot
5. **User fills out** their details (name, company, use case, etc.)
6. **Form submits securely** with spam protection and validation
7. **Database stores** all info including product=arioncomply, type=pilot, source page
8. **User sees** "Thank you! Your ArionComply registration has been received"

### What You Get as Admin

**Structured data showing:**
- **Which product** they're interested in (ArionComply, ArionSecure, etc.)
- **Which program** they want (Pilot, Waitlist, Beta, Early Access)  
- **Which page** they came from (iso.arionetworks.com, security.arionetworks.com)
- **All their details** (company size, industry, use case, timeline)

**Example database record:**
```
Product: arioncomply
Program: pilot  
Source: https://iso.arionetworks.com
Company: "Example Corp"
Industry: "Technology"
Use Case: "Need to automate ISO 27001 compliance for upcoming audit"
```

### Multiple Products Support

The same form handles all your products dynamically:
- **ArionComply** → Blue theme, compliance messaging
- **ArionSecure** → Red theme, security messaging  
- **ArionAnalytics** → Green theme, analytics messaging
- **ArionPlatform** → Purple theme, platform messaging

---

## 📋 What You Need

- Supabase account
- Google account (for reCAPTCHA)
- Web hosting (Verpex or similar)
- 30 minutes

---

## ⚡ Quick Start (5 Steps)

### Step 1: Create Supabase Project (5 min)

1. Go to [supabase.com](https://supabase.com) → **New Project**
2. Name: `arionnetworks-registrations`
3. Save your **Project URL** and **Service Role Key**
4. Go to **SQL Editor** → **New Query**
5. Copy & paste the entire **Database Schema** file content → **Run**

### Step 2: Set Up reCAPTCHA (3 min)

1. Go to [Google reCAPTCHA Admin](https://www.google.com/recaptcha/admin)
2. Create new site → **reCAPTCHA v3**
3. Add domains: `arionetworks.com`, `iso.arionetworks.com`, `localhost`
4. Save **Site Key** and **Secret Key**

### Step 3: Deploy Edge Function (5 min)

```bash
# Install Supabase CLI
npm install -g supabase

# Login & link
supabase login
supabase link --project-ref YOUR_PROJECT_REF_ID

# Create function
mkdir -p supabase/functions/register
# Copy the Edge Function code into: supabase/functions/register/index.ts

# Deploy
supabase functions deploy register
```

**Set environment variables in Supabase Dashboard:**
- Go to **Edge Functions** → **Manage Secrets**
- Add: `RECAPTCHA_SECRET_KEY` = your reCAPTCHA secret key

### Step 4: Configure & Upload Form (5 min)

1. Open the **HTML file**
2. Replace these 2 lines:
   ```javascript
   SUPABASE_URL: 'YOUR_SUPABASE_URL',           // ← Your Supabase URL
   RECAPTCHA_SITE_KEY: 'YOUR_RECAPTCHA_SITE_KEY' // ← Your reCAPTCHA site key
   ```
3. Upload to your web hosting as `register.html`

### Step 5: Add Buttons to Product Pages (2 min)

Add buttons to your product pages that link to:
```
https://yoursite.com/register.html?product=arioncomply&type=pilot&source=https://iso.arionetworks.com
```

**Button HTML example:**
```html
<a href="https://yoursite.com/register.html?product=arioncomply&type=pilot&source=https://iso.arionetworks.com" 
   class="btn btn-primary">
   Join ArionComply Pilot Program
</a>
```

---

## 🎯 Product Page Button URLs

Just replace `yoursite.com` with your actual domain:

### ArionComply (ISO page)
```
https://yoursite.com/register.html?product=arioncomply&type=pilot&source=https://iso.arionetworks.com
https://yoursite.com/register.html?product=arioncomply&type=waitlist&source=https://iso.arionetworks.com
```

### ArionSecure (Security page)
```
https://yoursite.com/register.html?product=arionsecure&type=pilot&source=https://security.arionetworks.com
https://yoursite.com/register.html?product=arionsecure&type=waitlist&source=https://security.arionetworks.com
```

### ArionAnalytics (Analytics page)
```
https://yoursite.com/register.html?product=arionanalytics&type=pilot&source=https://analytics.arionetworks.com
https://yoursite.com/register.html?product=arionanalytics&type=waitlist&source=https://analytics.arionetworks.com
```

### ArionPlatform (Platform page)
```
https://yoursite.com/register.html?product=arionplatform&type=pilot&source=https://platform.arionetworks.com
https://yoursite.com/register.html?product=arionplatform&type=early_access&source=https://platform.arionetworks.com
```

---

## ✅ Test It Works

1. **Test registration**: Click a button from a product page → fill form → submit
2. **Check database**: Go to Supabase → **Table Editor** → `product_registrations` → verify data appears
3. **Check logs**: Go to **Edge Functions** → **Logs** → verify no errors

---

## 🔧 What Happens Automatically

**When someone clicks your product page button:**
- Form opens with product-specific colors and messaging
- Hidden fields populate with product ID and program type
- Form validates and prevents spam/duplicates
- Data saves to database with all tracking info
- Success message shows product-specific confirmation

**Database stores:**
- Which product they're interested in (`arioncomply`, `arionsecure`, etc.)
- Program type (`pilot`, `waitlist`, `beta`, `early_access`)
- Which page they came from
- All their contact and company info

---

## 📊 View Your Data

**In Supabase Dashboard:**
- **Table Editor** → `product_registrations` → See all registrations
- **SQL Editor** → Run queries like:
  ```sql
  SELECT product_id, program_type, COUNT(*) 
  FROM product_registrations 
  GROUP BY product_id, program_type;
  ```

---

## 🚨 Troubleshooting

**"Form not loading"**
- Check SUPABASE_URL is correct in HTML file
- Verify SSL certificate on your domain

**"reCAPTCHA errors"**  
- Check RECAPTCHA_SITE_KEY in HTML file
- Verify domain added to reCAPTCHA console

**"Submission fails"**
- Check Edge Function logs in Supabase
- Verify RECAPTCHA_SECRET_KEY set in Edge Functions

**"No data in database"**
- Check RLS policies in database
- Verify service role key is set

---

## 🎉 You're Done!

Your system is now live. Users can register from any product page and you'll get structured data showing exactly which products and programs they're interested in.

**Next steps:**
- Monitor registrations in Supabase
- Set up team notifications  
- Plan follow-up workflows for pilots/waitlists