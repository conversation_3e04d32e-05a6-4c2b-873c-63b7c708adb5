<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArionComply - AI-Driven Compliance Management</title>
    <style>
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            color: #1f2937;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }
        
        nav {
            background: transparent;
            padding: 16px 0;
            box-shadow: none;
            position: relative;
            width: 100%;
            z-index: 100;
        }
        
        nav .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        nav .logo {
            font-weight: 800;
            font-size: 1.4em;
            color: #059669;
            letter-spacing: -0.5px;
        }
        
        nav .family {
            color: #64748b;
            font-size: 0.8em;
            margin-left: 12px;
        }
        
        nav a {
            text-decoration: none;
            color: #475569;
            margin: 0 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        nav a:hover {
            color: #059669;
        }
        
        .back-link {
            color: #64748b !important;
            font-size: 0.9em;
        }
        
        .logo-header {
            height: 80px;
            background: transparent;
            display: flex;
            justify-content: center;
            align-items: center;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .logo-placeholder {
            width: 200px;
            height: 60px;
            background: transparent;
            border: 2px dashed rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(0,0,0,0.4);
            font-size: 1.2em;
            font-weight: 600;
        }
        
        .hero {
            text-align: center;
            margin: 40px 0 60px;
            padding: 60px 0;
        }
        
        .hero h1 {
            font-size: 3.5em;
            font-weight: 900;
            margin: 0 0 20px;
            color: #0f172a;
            letter-spacing: -2px;
        }
        
        .hero .subtitle {
            font-size: 1.4em;
            color: #059669;
            font-weight: 600;
            margin-bottom: 16px;
        }
        
        .hero .description {
            font-size: 1.2em;
            color: #64748b;
            max-width: 800px;
            margin: 0 auto 40px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 60px 0;
        }
        
        .stat-card {
            background: white;
            padding: 30px;
            border-radius: 16px;
            text-align: center;
            box-shadow: 0 4px 25px rgba(0,0,0,0.06);
            border: 1px solid rgba(5, 150, 105, 0.1);
            border-top: 4px solid #059669;
        }
        
        .stat-number {
            font-size: 3em;
            font-weight: 900;
            color: #059669;
            margin: 0;
            line-height: 1;
        }
        
        .stat-label {
            color: #64748b;
            font-weight: 500;
            margin-top: 8px;
        }
        
        .demo-section {
            background: white;
            border-radius: 20px;
            padding: 60px 0;
            margin: 80px 0;
            box-shadow: 0 8px 40px rgba(0,0,0,0.06);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .demo-header h2 {
            font-size: 2.5em;
            font-weight: 800;
            color: #0f172a;
            margin-bottom: 16px;
        }
        
        .chat-interface {
            max-width: 800px;
            margin: 0 auto;
            background: #f8fafc;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
        }
        
        .chat-header {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .chat-info h3 {
            margin: 0;
            font-size: 1.1em;
        }
        
        .chat-info p {
            margin: 0;
            opacity: 0.9;
            font-size: 0.9em;
        }
        
        .chat-messages {
            padding: 30px;
            height: 650px;
            overflow-y: auto;
            background: #f8fafc;
        }
        
        .message {
            margin-bottom: 20px;
            display: flex;
            gap: 12px;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message.user .bubble {
            background: #3b82f6;
            color: white;
            margin-left: 80px;
        }
        
        .message.ai .bubble {
            background: white;
            border: 1px solid #e5e7eb;
            margin-right: 80px;
        }
        
        .bubble {
            padding: 16px 20px;
            border-radius: 18px;
            max-width: 500px;
            line-height: 1.5;
        }
        
        .source-note {
            font-size: 0.8em;
            color: #64748b;
            margin-top: 8px;
            font-style: italic;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 80px 0;
        }
        
        .feature-card {
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 4px 25px rgba(0,0,0,0.06);
            border-left: 6px solid #059669;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 40px rgba(0,0,0,0.1);
        }
        
        .feature-card h3 {
            color: #0f172a;
            font-size: 1.4em;
            font-weight: 700;
            margin-bottom: 16px;
        }
        
        .feature-card p {
            color: #64748b;
            line-height: 1.7;
        }
        
        .frameworks-section {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            padding: 80px 0;
            margin: 80px 0;
            border-radius: 20px;
        }
        
        .frameworks-grid {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            margin-top: 40px;
            position: relative;
        }
        
        .framework-card {
            background: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border: 1px solid rgba(5, 150, 105, 0.2);
            min-width: 200px;
            position: absolute;
            opacity: 0;
            transform: translateX(100px);
            transition: all 0.8s ease;
        }
        
        .framework-card.active {
            opacity: 1;
            transform: translateX(0);
        }
        
        .framework-card.exit {
            opacity: 0;
            transform: translateX(-100px);
        }
        
        .framework-name {
            font-size: 1.2em;
            font-weight: 700;
            color: #059669;
            margin-bottom: 8px;
        }
        
        .framework-desc {
            font-size: 0.9em;
            color: #64748b;
        }
        
        .trust-section {
            text-align: center;
            margin: 80px 0;
        }
        
        .trust-section h2 {
            font-size: 2.5em;
            font-weight: 800;
            color: #0f172a;
            margin-bottom: 40px;
        }
        
        .trust-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        
        .trust-card {
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 4px 25px rgba(0,0,0,0.06);
            border-top: 4px solid #059669;
        }
        
        .trust-card h3 {
            color: #0f172a;
            font-size: 1.3em;
            font-weight: 700;
            margin-bottom: 16px;
        }
        
        .cta-section {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
            border-radius: 20px;
            margin: 80px 0;
        }
        
        .cta-section h2 {
            font-size: 2.5em;
            font-weight: 800;
            margin-bottom: 20px;
        }
        
        .cta-section p {
            font-size: 1.3em;
            margin-bottom: 40px;
            opacity: 0.95;
        }
        
        .btn {
            display: inline-block;
            background: white;
            color: #059669;
            padding: 16px 40px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 700;
            font-size: 1.1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(255,255,255,0.3);
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(255,255,255,0.4);
        }
        
        footer {
            background: #1f2937;
            color: white;
            padding: 60px 0 40px;
            text-align: center;
        }
        
        footer .logo {
            font-size: 1.8em;
            font-weight: 800;
            color: #059669;
            margin-bottom: 12px;
        }
        
        footer .family {
            color: #9ca3af;
            margin-bottom: 20px;
        }
        
        footer a {
            color: #9ca3af;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        footer a:hover {
            color: #059669;
        }
        
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5em;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .chat-messages {
                height: 300px;
            }
            
            .message.user .bubble,
            .message.ai .bubble {
                margin-left: 0;
                margin-right: 0;
                max-width: 280px;
            }
        }
    </style>
</head>
<body>

    <!-- Logo Header -->
    <div class="logo-header">
        <img src="/images/ArionLogo.png" alt="Arion Networks" style="height: 60px; width: auto;">
    </div>

    <nav>
        <div class="container">
            <div>
                <span class="logo">ArionComply</span>
                <span class="family">Family of Arion Networks</span>
            </div>
            <div>
                <a href="#demo">AI Demo</a>
                <a href="#features">Features</a>
                <a href="#contact">Contact</a>
                <a href="https://arionetworks.com" class="back-link">← Back to Arion Networks</a>
            </div>
        </div>
    </nav>

    <div class="container">
        
        <div class="hero">
            <h1>ArionComply</h1>
            <p class="subtitle">AI-Driven Compliance Management Platform</p>
            <p class="description">Transform your compliance operations with conversational AI that's fully auditable and transparent. Streamline audits, manage policies, and ensure regulatory adherence with intelligent automation.</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">90%</div>
                <div class="stat-label">Faster Audit Preparation</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">75%</div>
                <div class="stat-label">Reduction in Manual Work</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">100%</div>
                <div class="stat-label">Audit Trail Coverage</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">24/7</div>
                <div class="stat-label">Compliance Monitoring</div>
            </div>
        </div>

    </div>

    <div class="demo-section" id="demo">
        <div class="container">
            <div class="demo-header">
                <h2>Meet Your AI Compliance Consultant</h2>
                <p style="font-size: 1.2em; color: #64748b;">Experience natural language compliance management with full transparency</p>
            </div>
            
            <div class="chat-interface">
                <div class="chat-header">
                    <div class="avatar">AC</div>
                    <div class="chat-info">
                        <h3>ArionComply AI Assistant</h3>
                        <p>Compliance Expert • Always Auditable</p>
                    </div>
                    <div style="margin-left: auto; width: 12px; height: 12px; background: #10b981; border-radius: 50%; animation: pulse 2s infinite;"></div>
                </div>
                
                <div class="chat-messages">
                    <div class="message user">
                        <div class="bubble">
                            Show me our ISO 27001 compliance status for Q1
                        </div>
                    </div>
                    
                    <div class="message ai">
                        <div class="bubble">
                            I've analyzed your Q1 ISO 27001 compliance data. Here's your status:
                            <br><br>
                            <strong>✅ Controls Implementation:</strong> 94% complete<br>
                            <strong>⚠️ Risk Assessments:</strong> 3 pending reviews<br>
                            <strong>✅ Documentation:</strong> All policies updated<br>
                            <strong>✅ Training:</strong> 89% staff certified
                            <div class="source-note">Source: Internal audit logs, policy database, risk register, training records</div>
                        </div>
                    </div>
                    
                    <div class="message user">
                        <div class="bubble">
                            Are we ready for our upcoming SOX audit in 6 weeks?
                        </div>
                    </div>
                    
                    <div class="message ai">
                        <div class="bubble">
                            <strong>📊 SOX Audit Readiness Assessment: 78% Ready</strong>
                            <br><br>
                            <strong>✅ What's Ready:</strong><br>
                            • All key financial controls documented<br>
                            • SOC 1 Type II testing completed<br>
                            • Management assertions prepared<br>
                            • Control deficiency remediation complete
                            <br><br>
                            <strong>⚠️ Still Need to Complete:</strong><br>
                            • 3 control effectiveness testing cycles<br>
                            • 2 IT general controls attestations<br>
                            • Management certifications pending<br>
                            • Final walkthrough documentation
                            <br><br>
                            <strong>💡 Recommendation:</strong> Would you like me to create a detailed project plan with tasks, assignments, and deadlines to ensure full audit readiness by your SOX audit date?
                            <div class="source-note">Source: SOX compliance system, control testing results, management attestations</div>
                        </div>
                    </div>
                    
                    <div class="message user">
                        <div class="bubble">
                            We've had a security incident, what do we do?
                        </div>
                    </div>
                    
                    <div class="message ai">
                        <div class="bubble">
                            <strong>🚨 Security Incident Response Activated</strong>
                            <br><br>
                            Let's get this properly documented and managed. I'll walk you through our incident response process.
                            <br><br>
                            <strong>First, quick triage questions:</strong><br>
                            • Is the incident currently active?<br>
                            • What type of security event? (Data breach, malware, unauthorized access, etc.)<br>
                            • Estimated impact level? (Low, Medium, High, Critical)
                            <br><br>
                            Once I have these details, I'll:<br>
                            ✅ Create incident ticket in our system<br>
                            ✅ Assign immediate response team<br>
                            ✅ Guide you through containment steps<br>
                            ✅ Schedule root cause analysis<br>
                            ✅ Ensure compliance reporting requirements are met
                            <br><br>
                            <strong>Ready to start?</strong> Let's begin with incident classification.
                            <div class="source-note">Source: Incident response playbook, ISMS procedures, escalation matrix</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <div id="features">
            <div style="text-align: center; margin-bottom: 60px;">
                <h2 style="font-size: 2.5em; font-weight: 800; color: #0f172a; margin-bottom: 20px;">Intelligent Compliance Management</h2>
                <p style="font-size: 1.2em; color: #64748b; max-width: 800px; margin: 0 auto;">Advanced AI capabilities designed specifically for compliance professionals, with complete transparency and auditability.</p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <h3>Natural Language Interface</h3>
                    <p>Ask questions in plain English and get instant, comprehensive answers about your compliance status, risk assessments, and regulatory requirements.</p>
                </div>
                
                <div class="feature-card">
                    <h3>Full Audit Transparency</h3>
                    <p>Every AI decision includes complete source attribution and reasoning chains. See exactly how conclusions were reached and track all data sources.</p>
                </div>
                
                <div class="feature-card">
                    <h3>Automated Policy Management</h3>
                    <p>Intelligent policy creation, updates, and compliance monitoring. AI tracks changes and ensures consistency across all documentation.</p>
                </div>
                
                <div class="feature-card">
                    <h3>Real-time Risk Assessment</h3>
                    <p>Continuous monitoring and intelligent risk identification with automated prioritization and recommended remediation actions.</p>
                </div>
                
                <div class="feature-card">
                    <h3>Intelligent Reporting</h3>
                    <p>Generate compliance reports, executive summaries, and audit documentation automatically with natural language explanations.</p>
                </div>
                
                <div class="feature-card">
                    <h3>Seamless Integration</h3>
                    <p>Connect with existing GRC systems, document repositories, and audit tools. API-first design for maximum flexibility.</p>
                </div>
            </div>
        </div>

    </div>

    <div class="frameworks-section">
        <div class="container">
            <div style="text-align: center;">
                <h2 style="font-size: 2.5em; font-weight: 800; color: #0f172a; margin-bottom: 20px;">Comprehensive Framework Coverage</h2>
                <p style="font-size: 1.2em; color: #64748b;">Flexible, expandable support for evolving compliance requirements</p>
            </div>
            
            <div class="frameworks-grid">
                <div class="framework-card">
                    <div class="framework-name">ISO 27001</div>
                    <div class="framework-desc">Information Security Management</div>
                </div>
                <div class="framework-card">
                    <div class="framework-name">ISO 27701</div>
                    <div class="framework-desc">Privacy Information Management</div>
                </div>
                <div class="framework-card">
                    <div class="framework-name">ISO 42001</div>
                    <div class="framework-desc">AI Management Systems</div>
                </div>
                <div class="framework-card">
                    <div class="framework-name">SOX</div>
                    <div class="framework-desc">Financial Controls</div>
                </div>
                <div class="framework-card">
                    <div class="framework-name">GDPR</div>
                    <div class="framework-desc">Data Privacy Regulation</div>
                </div>
                <div class="framework-card">
                    <div class="framework-name">HIPAA</div>
                    <div class="framework-desc">Healthcare Data Protection</div>
                </div>
                <div class="framework-card">
                    <div class="framework-name">PCI DSS</div>
                    <div class="framework-desc">Payment Card Security</div>
                </div>
                <div class="framework-card">
                    <div class="framework-name">NIST</div>
                    <div class="framework-desc">Cybersecurity Framework</div>
                </div>
                <div class="framework-card">
                    <div class="framework-name">CCPA/CPRA</div>
                    <div class="framework-desc">California Privacy Rights</div>
                </div>
                <div class="framework-card">
                    <div class="framework-name">ISO 27017</div>
                    <div class="framework-desc">Cloud Security Controls</div>
                </div>
                <div class="framework-card">
                    <div class="framework-name">ISO 27018</div>
                    <div class="framework-desc">Cloud Privacy Protection</div>
                </div>
                <div class="framework-card">
                    <div class="framework-name">NIS2 Directive</div>
                    <div class="framework-desc">EU Cybersecurity Standards</div>
                </div>
                <div class="framework-card">
                    <div class="framework-name">CISA Guidelines</div>
                    <div class="framework-desc">US Critical Infrastructure</div>
                </div>
                <div class="framework-card">
                    <div class="framework-name">EU AI Act</div>
                    <div class="framework-desc">AI Governance & Risk Management</div>
                </div>
                <div class="framework-card">
                    <div class="framework-name">NIST AI RMF</div>
                    <div class="framework-desc">AI Risk Management Framework</div>
                </div>
                <div class="framework-card">
                    <div class="framework-name">ISO 23053</div>
                    <div class="framework-desc">AI Risk Management</div>
                </div>
                <div class="framework-card">
                    <div class="framework-name">FedRAMP</div>
                    <div class="framework-desc">Federal Cloud Security</div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <div class="trust-section">
            <h2>AI You Can Trust and Verify</h2>
            <p style="font-size: 1.2em; color: #64748b; max-width: 800px; margin: 0 auto 40px;">Unlike black-box AI systems, ArionComply provides complete transparency into every decision and recommendation.</p>
            
            <div class="trust-grid">
                <div class="trust-card">
                    <h3>Source Attribution</h3>
                    <p>Every answer includes specific references to source documents, policies, and data points used in the analysis.</p>
                </div>
                
                <div class="trust-card">
                    <h3>Reasoning Chains</h3>
                    <p>See the logical steps and decision criteria used by the AI to reach conclusions and recommendations.</p>
                </div>
                
                <div class="trust-card">
                    <h3>Complete Audit Trails</h3>
                    <p>Comprehensive logs of all interactions, modifications, and decisions for full compliance documentation.</p>
                </div>
            </div>
        </div>

    </div>

    <div class="cta-section" id="contact">
        <div class="container">
            <h2>Join Our Exclusive Pilot Program</h2>
            <p>Be among the first organizations to transform compliance operations with conversational AI. Limited pilot program available for forward-thinking compliance teams.</p>
            <a href="#" class="btn">Apply for Pilot Program</a>
        </div>
    </div>

    <footer>
        <div class="container">
            <div class="logo">ArionComply</div>
            <div class="family">Family of Arion Networks</div>
            <p style="color: #9ca3af; margin-bottom: 30px;">AI-driven compliance management with transparency, auditability, and natural language interaction.</p>
            <div style="border-top: 1px solid #374151; padding-top: 30px;">
                <p style="color: #6b7280;">&copy; 2025 Arion Networks. All rights reserved.</p>
                <a href="https://arionetworks.com">Visit Arion Networks →</a>
            </div>
        </div>
    </footer>

    <style>
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>

    <script>
        // Framework cards carousel effect
        document.addEventListener('DOMContentLoaded', function() {
            const frameworkCards = document.querySelectorAll('.framework-card');
            const frameworksSection = document.querySelector('.frameworks-section');
            let currentIndex = 0;
            let carouselInterval;
            
            function showNextCard() {
                // Hide current card with exit animation
                if (frameworkCards[currentIndex]) {
                    frameworkCards[currentIndex].classList.remove('active');
                    frameworkCards[currentIndex].classList.add('exit');
                }
                
                // Move to next card
                currentIndex = (currentIndex + 1) % frameworkCards.length;
                
                // Show next card after a brief delay
                setTimeout(() => {
                    // Reset all cards
                    frameworkCards.forEach(card => {
                        card.classList.remove('active', 'exit');
                    });
                    
                    // Show new current card
                    frameworkCards[currentIndex].classList.add('active');
                }, 400);
            }
            
            function startCarousel() {
                // Show first card immediately
                frameworkCards[0].classList.add('active');
                
                // Start the carousel loop
                carouselInterval = setInterval(showNextCard, 2500); // 2.5 seconds per card
            }
            
            function stopCarousel() {
                if (carouselInterval) {
                    clearInterval(carouselInterval);
                }
                // Hide all cards
                frameworkCards.forEach(card => {
                    card.classList.remove('active', 'exit');
                });
                currentIndex = 0;
            }
            
            // Observer to start/stop carousel when section is visible
            const observerOptions = {
                root: null,
                rootMargin: '0px',
                threshold: 0.3
            };
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        startCarousel();
                    } else {
                        stopCarousel();
                    }
                });
            }, observerOptions);
            
            observer.observe(frameworksSection);
        });
    </script>

</body>
</html>
