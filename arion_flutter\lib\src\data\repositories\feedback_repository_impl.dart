import 'package:flutter/foundation.dart';
import '../../domain/repositories/feedback_repository.dart';
import '../datasources/feedback_local_ds.dart';

class FeedbackRepositoryImpl implements FeedbackRepository {
  final FeedbackLocalDataSource local;
  FeedbackRepositoryImpl(this.local);

  String? _getSessionId() => Uri.base.queryParameters['session'];
  String _generateId() {
    final ts = DateTime.now().millisecondsSinceEpoch;
    final rand = DateTime.now().microsecondsSinceEpoch % 1000;
    return 'FB' +
        ts.toRadixString(36).toUpperCase() +
        rand.toRadixString(36).toUpperCase();
  }

  @override
  Future<void> submitFullFeedback({
    required bool isAdmin,
    required String id,
    required String type,
    required String priority,
    required String subject,
    required String message,
    String? additionalNotes,
    String? rating,
    String? contactMethod,
    String adminStatus = 'new',
    String? adminAssignee,
    String? adminNotes,
  }) async {
    final item = {
      'id': id.isEmpty ? _generateId() : id,
      'type': type,
      'priority': priority,
      'subject': subject,
      'message': message,
      'additionalNotes': additionalNotes,
      'rating': rating,
      'contactMethod': contactMethod,
      'adminStatus': adminStatus,
      'adminAssignee': adminAssignee,
      'adminNotes': adminNotes,
      'timestamp': DateTime.now().toIso8601String(),
      'sessionId': _getSessionId(),
      'userAgent': kIsWeb ? 'flutter-web' : 'flutter',
      'url': Uri.base.toString(),
      'isAdmin': isAdmin,
    };
    await local.insert(item);
  }

  @override
  Future<void> submitMessageFeedback({
    required String messagePreview,
    required String feedback,
  }) async {
    final item = {
      'id': _generateId(),
      'type': 'message-feedback',
      'messageContent': messagePreview,
      'feedback': feedback,
      'timestamp': DateTime.now().toIso8601String(),
      'sessionId': _getSessionId(),
    };
    await local.insert(item);
  }

  @override
  Future<void> submitQuickMessageReport({
    required String messageContent,
    required String issueType,
    required String description,
  }) async {
    final item = {
      'id': _generateId(),
      'type': 'message-report',
      'issueType': issueType,
      'description': description,
      'messageContent': messageContent,
      'timestamp': DateTime.now().toIso8601String(),
      'sessionId': _getSessionId(),
      'priority': 'high',
    };
    await local.insert(item);
  }
}
