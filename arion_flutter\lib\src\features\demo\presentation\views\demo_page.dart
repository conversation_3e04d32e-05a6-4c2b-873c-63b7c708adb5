import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/di/locator.dart';
import '../../../../core/services/compliance_filter.dart';
import '../../../../domain/repositories/chat_repository.dart';
import '../../../../domain/repositories/document_repository.dart';
import '../../../../domain/repositories/feedback_repository.dart';
import '../blocs/demo/demo_cubit.dart';
import '../../../auth/presentation/blocs/auth/auth_cubit.dart';

class DemoPage extends StatelessWidget {
  final bool adminFromLink;
  const DemoPage({super.key, this.adminFromLink = false});

  @override
  Widget build(BuildContext context) {
    final isAdmin = adminFromLink || context.read<AuthCubit>().state.isAdmin;
    return BlocProvider(
      create: (_) => DemoCubit(
        locator<ChatRepository>(),
        locator<DocumentRepository>(),
        locator<ComplianceFilterService>(),
        locator<FeedbackRepository>(),
      )..initialize(isAdmin: isAdmin),
      child: const _DemoView(),
    );
  }
}

class _DemoView extends StatefulWidget {
  const _DemoView();
  @override
  State<_DemoView> createState() => _DemoViewState();
}

class _DemoViewState extends State<_DemoView> {
  final TextEditingController _input = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Column(
              children: [
                _TopBar(),
                if (context.select((DemoCubit c) => c.state.infoOpen))
                  const _InfoPanel(),
                Expanded(child: _BodySwitcher()),
                _InputRow(controller: _input),
              ],
            ),
            if (context.select((DemoCubit c) => c.state.limitReached))
              const _LimitOverlay(),
            if (context.select((DemoCubit c) => c.state.leadCaptureOpen))
              const _LeadCaptureOverlay(),
            const _FloatingFeedbackButton(),
          ],
        ),
      ),
    );
  }
}

class _TopBar extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final auth = context.read<AuthCubit>().state;
    final isAdmin = auth.isAdmin;
    final email = (auth.data?['userEmail'] ?? '') as String;
    final first = email.contains('@') ? email.split('@').first : email;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(bottom: BorderSide(color: Color(0xFFE2E8F0))),
      ),
      child: Row(
        children: [
          if (isAdmin)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
              margin: const EdgeInsets.only(right: 12),
              decoration: BoxDecoration(
                color: const Color(0xFFFEF3C7),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '👑 ${DateTime.now().hour < 12 ? 'Good morning' : 'Good afternoon'}, ${first.isEmpty ? 'Admin' : first}',
              ),
            )
          else
            const SizedBox.shrink(),
          const Expanded(
            child: Center(
              child: Text(
                'ArionComply Demo',
                style: TextStyle(
                  color: Color(0xFF059669),
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ),
          BlocBuilder<DemoCubit, DemoState>(
            builder: (_, s) {
              final label = s.isAdmin
                  ? '👑 Admin Mode'
                  : 'Session: ${s.sessionCount}/${s.sessionLimit}';
              return Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: s.isAdmin
                      ? const Color(0xFFECFDF5)
                      : const Color(0xFFEFF6FF),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(label),
              );
            },
          ),
        ],
      ),
    );
  }
}

class _InfoPanel extends StatelessWidget {
  const _InfoPanel();
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 60, vertical: 12),
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF059669),
          borderRadius: BorderRadius.circular(12),
          boxShadow: const [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 12,
              offset: Offset(0, 4),
            ),
          ],
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        '🤖 ArionComply Assistant',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w700,
                          fontSize: 18,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'Your AI-powered compliance automation guide',
                        style: TextStyle(color: Colors.white70),
                      ),
                    ],
                  ),
                ),
                TextButton(
                  onPressed: () => context.read<DemoCubit>().toggleInfo(),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 14,
                      vertical: 8,
                    ),
                    shape: const StadiumBorder(),
                    side: const BorderSide(color: Colors.white70),
                  ),
                  child: const Text('✕ Close Info'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Color(0xFF15803D), Color(0xFF0F766E)],
                ),
              ),
              padding: const EdgeInsets.all(16),
              child: DefaultTextStyle(
                style: const TextStyle(color: Colors.white),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // What this demo includes
                    const _SectionTitle(
                      label: 'What This Demo Includes',
                      icon: '🧪',
                    ),
                    const SizedBox(height: 8),
                    const _Bullet(
                      text:
                          'AI-powered guidance on ISO 27001, GDPR, AI governance & security',
                    ),
                    const _Bullet(
                      text: 'Smart question suggestions to get you started',
                    ),
                    const _Bullet(
                      text:
                          'Contextual follow-up questions based on your interests',
                    ),
                    const _Bullet(
                      text:
                          'Expert knowledge without the need for consultation calls',
                    ),
                    const SizedBox(height: 16),

                    // Demo limitations
                    const _SectionTitle(label: 'Demo Limitations', icon: '📋'),
                    const SizedBox(height: 8),
                    const _Bullet.rich([
                      TextSpan(
                        text: '12 questions per session',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                      TextSpan(text: '  –  Start fresh anytime'),
                    ]),
                    const _Bullet.rich([
                      TextSpan(
                        text: '20 questions per day',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                      TextSpan(text: '  –  Resets at midnight'),
                    ]),
                    const _Bullet.rich([
                      TextSpan(
                        text: '200 questions per month',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                      TextSpan(text: '  –  Resets monthly'),
                    ]),
                    const _Bullet(
                      text: 'Compliance topics only  –  Focused expertise',
                    ),
                    const SizedBox(height: 12),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.all(10),
                      child: const Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('💡 ', style: TextStyle(color: Colors.white)),
                          Expanded(
                            child: Text(
                              'Tip: Make your questions specific! Instead of "Tell me about ISO 27001", ask "What documents do I need for ISO 27001 certification?" for better results.',
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Full version benefits
                    const _SectionTitle(
                      label: 'Full Version Benefits',
                      icon: '🚀',
                    ),
                    const SizedBox(height: 8),
                    const _Bullet(
                      text: 'Unlimited questions and conversations',
                    ),
                    const _Bullet(text: 'Generate actual compliance documents'),
                    const _Bullet(text: 'Save and continue sessions'),
                    const _Bullet(text: 'Schedule expert consultations'),
                    const _Bullet(text: 'Custom compliance roadmaps'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _SectionTitle extends StatelessWidget {
  final String label;
  final String icon;
  const _SectionTitle({required this.label, required this.icon});
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(icon, style: const TextStyle(color: Colors.white, fontSize: 16)),
        const SizedBox(width: 8),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w700,
          ),
        ),
      ],
    );
  }
}

class _Bullet extends StatelessWidget {
  final String? text;
  final List<TextSpan>? spans;
  const _Bullet({required String this.text}) : spans = null;
  const _Bullet.rich(List<TextSpan> this.spans) : text = null;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 3),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '• ',
            style: TextStyle(color: Colors.white70, height: 1.4),
          ),
          Expanded(
            child: spans != null
                ? RichText(
                    text: TextSpan(
                      style: const TextStyle(color: Colors.white),
                      children: spans,
                    ),
                  )
                : Text(text ?? '', style: const TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}

class _BodySwitcher extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final s = context.watch<DemoCubit>().state;
    if (s.view == DemoView.documents) return const _DocumentGallery();
    if (s.view == DemoView.viewer) return const _DocumentViewer();
    return const _ChatView();
  }
}

class _ChatView extends StatelessWidget {
  const _ChatView();
  @override
  Widget build(BuildContext context) {
    final infoOpen = context.select((DemoCubit c) => c.state.infoOpen);
    final showSuggestions = !context.select(
      (DemoCubit c) => c.state.suggestionsHidden,
    );
    final cubit = context.read<DemoCubit>();
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 60, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            offset: Offset(-2, -2),
            blurRadius: 8,
          ),
          BoxShadow(
            color: Colors.black12,
            offset: Offset(2, -2),
            blurRadius: 8,
          ),
        ],
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Column(
        children: [
          if (!infoOpen) _AssistantHeader(onInfo: cubit.toggleInfo),
          if (showSuggestions)
            _Suggestions()
          else
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(top: 12),
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Align(
                alignment: Alignment.centerLeft,
                child: OutlinedButton.icon(
                  onPressed: cubit.showSuggestions,
                  icon: const Icon(Icons.lightbulb_outline),
                  label: const Text('Show example questions'),
                  style: ButtonStyles.outlineChip,
                ),
              ),
            ),
          // Right-side CTA
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Align(
              alignment: Alignment.centerRight,
              child: Padding(
                padding: const EdgeInsets.only(top: 8),
                child: ElevatedButton(
                  style: ButtonStyles.primary,
                  onPressed: () =>
                      cubit.sendQuickReply('Get ISO 27001 certified'),
                  child: const Text('Get ISO 27001 certified'),
                ),
              ),
            ),
          ),
          Expanded(
            child: Builder(
              builder: (context) {
                final s = context.watch<DemoCubit>().state;
                final List<Widget> items = s.messages
                    .map<Widget>((m) => _MessageTile(m))
                    .toList();
                if (s.waiting) {
                  items.add(const _AssistantThinking());
                }
                return ListView(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 16,
                  ),
                  children: items,
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

// Optional banner removed to simplify header per new UI

class _AssistantHeader extends StatelessWidget {
  final VoidCallback onInfo;
  const _AssistantHeader({required this.onInfo});
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE2E8F0)),
      ),
      child: Column(
        children: [
          Container(
            decoration: const BoxDecoration(
              color: Color(0xFF059669),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        '🤖 ArionComply Assistant',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w700,
                          fontSize: 18,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        'Your AI-powered compliance automation guide',
                        style: TextStyle(color: Colors.white70),
                      ),
                    ],
                  ),
                ),
                OutlinedButton.icon(
                  onPressed: onInfo,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.white,
                    side: const BorderSide(color: Colors.white70),
                    shape: const StadiumBorder(),
                  ),
                  icon: const Icon(Icons.info_outline, size: 18),
                  label: const Text('Demo Info'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _Suggestions extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final cubit = context.read<DemoCubit>();
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      decoration: const BoxDecoration(
        color: Color(0xFFF8FAFC),
        border: Border(bottom: BorderSide(color: Color(0xFFE2E8F0))),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text('💡  Need help getting started? Try these questions:'),
              const Spacer(),
              TextButton(
                onPressed: cubit.hideSuggestions,
                style: ButtonStyles.textLink,
                child: const Text('Hide suggestions'),
              ),
            ],
          ),
          const SizedBox(height: 12),
          LayoutBuilder(
            builder: (context, constraints) {
              final double w = constraints.maxWidth;
              final int cols = w >= 980 ? 3 : (w >= 640 ? 2 : 1);
              final double gap = 16;
              final double itemWidth = (w - gap * (cols - 1)) / cols;

              Widget card(String title, String icon, List<String> items) {
                return SizedBox(
                  width: itemWidth,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: const Color(0xFFE2E8F0)),
                    ),
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(icon),
                            const SizedBox(width: 6),
                            Text(
                              title,
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                color: Color(0xFF0F172A),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                        Wrap(
                          spacing: 10,
                          runSpacing: 10,
                          children: [
                            for (final t in items)
                              OutlinedButton(
                                onPressed: () => cubit.sendMessage(t),
                                style: ButtonStyles.outlineChip,
                                child: Text(t),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              }

              final iso = [
                'How to get ISO 27001 certified?',
                'What is a risk register?',
                'ISO 27001 implementation timeline',
                'Statement of Applicability guide',
              ];
              final gdpr = [
                'What is GDPR compliance?',
                'How to create ROPA document?',
                'GDPR data mapping requirements',
                'Privacy impact assessment',
              ];
              final ai = [
                'EU AI Act requirements',
                'AI risk management framework',
                'Ethical AI guidelines',
                'AI bias testing methods',
              ];

              return Wrap(
                spacing: gap,
                runSpacing: gap,
                children: [
                  card('ISO 27001', '🛡️', iso),
                  card('GDPR', '🛡️', gdpr),
                  card('AI Governance', '🤖', ai),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}

class _MessageTile extends StatelessWidget {
  final DemoMessage m;
  const _MessageTile(this.m);
  @override
  Widget build(BuildContext context) {
    if (m.sender == 'assistant_rejection') {
      final rej = m.rejection ?? {};
      final topics = List<String>.from(rej['allowedTopics'] ?? const []);
      return Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: const Color(0xFFFFF1F2),
          border: Border.all(color: const Color(0xFFEF4444)),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              rej['title']?.toString() ?? 'Outside Our Expertise',
              style: const TextStyle(
                color: Color(0xFFDC2626),
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(rej['message']?.toString() ?? ''),
            const SizedBox(height: 8),
            if (topics.isNotEmpty)
              Wrap(
                spacing: 6,
                runSpacing: 6,
                children: topics
                    .map(
                      (t) => OutlinedButton(
                        onPressed: () =>
                            context.read<DemoCubit>().sendMessage(t),
                        child: Text(t),
                      ),
                    )
                    .toList(),
              ),
          ],
        ),
      );
    }
    final isUser = m.sender == 'user';
    return Align(
      alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 700),
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: isUser ? const Color(0xFF059669) : Colors.white,
          borderRadius: BorderRadius.circular(18),
          border: isUser ? null : Border.all(color: const Color(0xFFE2E8F0)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              m.text,
              style: TextStyle(
                color: isUser ? Colors.white : const Color(0xFF334155),
              ),
            ),
            if (!isUser && m.quickReplies.isNotEmpty) ...[
              const SizedBox(height: 8),
              Wrap(
                spacing: 6,
                runSpacing: 6,
                children: [
                  for (final r in m.quickReplies)
                    OutlinedButton(
                      onPressed: () =>
                          context.read<DemoCubit>().sendQuickReply(r),
                      child: Text(r),
                    ),
                ],
              ),
            ],
            if (!isUser) _MsgFeedbackRow(message: m.text),
          ],
        ),
      ),
    );
  }
}

class _FloatingFeedbackButton extends StatelessWidget {
  const _FloatingFeedbackButton();
  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 16,
      bottom: 76,
      child: ElevatedButton(
        onPressed: () => showDialog(
          context: context,
          builder: (dialogCtx) {
            final cubit = context.read<DemoCubit>();
            return BlocProvider.value(
              value: cubit,
              child: const _FeedbackDialog(),
            );
          },
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF059669),
          foregroundColor: Colors.white,
          shape: const StadiumBorder(),
          elevation: 6,
          padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 10),
        ),
        child: const Text('💬 Feedback'),
      ),
    );
  }
}

class _FeedbackDialog extends StatefulWidget {
  const _FeedbackDialog();
  @override
  State<_FeedbackDialog> createState() => _FeedbackDialogState();
}

class _FeedbackDialogState extends State<_FeedbackDialog> {
  final _form = GlobalKey<FormState>();
  String? _type;
  final _subject = TextEditingController();
  final _message = TextEditingController();
  String? _rating;
  String? _priority;
  final _additionalNotes = TextEditingController();
  String? _contactMethod;
  // Admin extras
  String _adminStatus = 'new';
  final _adminAssignee = TextEditingController();
  final _adminNotes = TextEditingController();
  bool _sending = false;
  bool _success = false;
  String? _successId;

  @override
  void dispose() {
    _subject.dispose();
    _message.dispose();
    _additionalNotes.dispose();
    _adminAssignee.dispose();
    _adminNotes.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isAdmin = context.select((DemoCubit c) => c.state.isAdmin);
    return Dialog(
      backgroundColor: Colors.white,
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 560),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFF059669), Color(0xFF047857)],
                ),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: const Text(
                '💬 Send Feedback',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
            ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.75,
              ),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: _success
                    ? _SuccessContent(id: _successId!)
                    : Form(
                        key: _form,
                        child: Column(
                          children: [
                            DropdownButtonFormField<String>(
                              value: _type,
                              decoration: _dec('Type of Feedback'),
                              items: const [
                                DropdownMenuItem(
                                  value: 'bug',
                                  child: Text('🐛 Bug Report'),
                                ),
                                DropdownMenuItem(
                                  value: 'feature',
                                  child: Text('💡 Feature Request'),
                                ),
                                DropdownMenuItem(
                                  value: 'improvement',
                                  child: Text('⚡ Improvement Suggestion'),
                                ),
                                DropdownMenuItem(
                                  value: 'general',
                                  child: Text('💭 General Feedback'),
                                ),
                              ],
                              onChanged: (v) => setState(() => _type = v),
                              validator: (v) =>
                                  v == null || v.isEmpty ? 'Required' : null,
                            ),
                            const SizedBox(height: 12),
                            DropdownButtonFormField<String>(
                              value: _priority,
                              decoration: _dec('Priority'),
                              items: const [
                                DropdownMenuItem(
                                  value: 'low',
                                  child: Text('Low'),
                                ),
                                DropdownMenuItem(
                                  value: 'medium',
                                  child: Text('Medium'),
                                ),
                                DropdownMenuItem(
                                  value: 'high',
                                  child: Text('High'),
                                ),
                              ],
                              onChanged: (v) => setState(() => _priority = v),
                              validator: (v) =>
                                  v == null || v.isEmpty ? 'Required' : null,
                            ),
                            const SizedBox(height: 12),
                            TextFormField(
                              controller: _subject,
                              decoration: _dec('Subject'),
                              validator: (v) => v == null || v.trim().isEmpty
                                  ? 'Required'
                                  : null,
                            ),
                            const SizedBox(height: 12),
                            TextFormField(
                              controller: _message,
                              maxLines: 6,
                              decoration: _dec('Message'),
                              validator: (v) => v == null || v.trim().isEmpty
                                  ? 'Required'
                                  : null,
                            ),
                            const SizedBox(height: 12),
                            TextFormField(
                              controller: _additionalNotes,
                              maxLines: 3,
                              decoration: _dec('Additional Notes (optional)'),
                            ),
                            const SizedBox(height: 12),
                            DropdownButtonFormField<String>(
                              value: _rating,
                              decoration: _dec('Overall Experience (1-5)'),
                              items: const [
                                DropdownMenuItem(
                                  value: '5',
                                  child: Text('⭐⭐⭐⭐⭐ Excellent (5/5)'),
                                ),
                                DropdownMenuItem(
                                  value: '4',
                                  child: Text('⭐⭐⭐⭐ Good (4/5)'),
                                ),
                                DropdownMenuItem(
                                  value: '3',
                                  child: Text('⭐⭐⭐ Average (3/5)'),
                                ),
                                DropdownMenuItem(
                                  value: '2',
                                  child: Text('⭐⭐ Poor (2/5)'),
                                ),
                                DropdownMenuItem(
                                  value: '1',
                                  child: Text('⭐ Very Poor (1/5)'),
                                ),
                              ],
                              onChanged: (v) => setState(() => _rating = v),
                            ),
                            const SizedBox(height: 12),
                            DropdownButtonFormField<String>(
                              value: _contactMethod,
                              decoration: _dec('Preferred contact (optional)'),
                              items: const [
                                DropdownMenuItem(
                                  value: 'email',
                                  child: Text('Email'),
                                ),
                                DropdownMenuItem(
                                  value: 'phone',
                                  child: Text('Phone'),
                                ),
                                DropdownMenuItem(
                                  value: 'none',
                                  child: Text('No contact'),
                                ),
                              ],
                              onChanged: (v) =>
                                  setState(() => _contactMethod = v),
                            ),
                            if (isAdmin) ...[
                              const SizedBox(height: 16),
                              const Align(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  'Admin Tracking',
                                  style: TextStyle(fontWeight: FontWeight.w700),
                                ),
                              ),
                              const SizedBox(height: 8),
                              DropdownButtonFormField<String>(
                                value: _adminStatus,
                                decoration: _dec('Status'),
                                items: const [
                                  DropdownMenuItem(
                                    value: 'new',
                                    child: Text('New'),
                                  ),
                                  DropdownMenuItem(
                                    value: 'triaged',
                                    child: Text('Triaged'),
                                  ),
                                  DropdownMenuItem(
                                    value: 'in-progress',
                                    child: Text('In Progress'),
                                  ),
                                  DropdownMenuItem(
                                    value: 'resolved',
                                    child: Text('Resolved'),
                                  ),
                                ],
                                onChanged: (v) =>
                                    setState(() => _adminStatus = v ?? 'new'),
                              ),
                              const SizedBox(height: 12),
                              TextFormField(
                                controller: _adminAssignee,
                                decoration: _dec('Assignee (optional)'),
                              ),
                              const SizedBox(height: 12),
                              TextFormField(
                                controller: _adminNotes,
                                maxLines: 3,
                                decoration: _dec('Admin notes (optional)'),
                              ),
                            ],
                            const SizedBox(height: 16),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                TextButton(
                                  onPressed: _sending
                                      ? null
                                      : () => Navigator.of(context).pop(),
                                  child: const Text('Cancel'),
                                ),
                                const SizedBox(width: 8),
                                ElevatedButton(
                                  onPressed: _sending
                                      ? null
                                      : () async {
                                          if (!_form.currentState!.validate())
                                            return;
                                          setState(() => _sending = true);
                                          final id = _generateId();
                                          await context
                                              .read<DemoCubit>()
                                              .submitFullFeedback(
                                                isAdmin: isAdmin,
                                                id: id,
                                                type: _type!,
                                                priority: _priority ?? 'medium',
                                                subject: _subject.text.trim(),
                                                message: _message.text.trim(),
                                                additionalNotes:
                                                    _additionalNotes.text
                                                        .trim()
                                                        .isEmpty
                                                    ? null
                                                    : _additionalNotes.text
                                                          .trim(),
                                                rating: _rating,
                                                contactMethod: _contactMethod,
                                                adminStatus: _adminStatus,
                                                adminAssignee:
                                                    _adminAssignee.text
                                                        .trim()
                                                        .isEmpty
                                                    ? null
                                                    : _adminAssignee.text
                                                          .trim(),
                                                adminNotes:
                                                    _adminNotes.text
                                                        .trim()
                                                        .isEmpty
                                                    ? null
                                                    : _adminNotes.text.trim(),
                                              );
                                          if (!mounted) return;
                                          setState(() {
                                            _success = true;
                                            _successId = id;
                                          });
                                          // Auto close after 8s
                                          Future.delayed(
                                            const Duration(seconds: 8),
                                            () {
                                              if (mounted)
                                                Navigator.of(
                                                  context,
                                                ).maybePop();
                                            },
                                          );
                                        },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: const Color(0xFF059669),
                                    foregroundColor: Colors.white,
                                  ),
                                  child: Text(
                                    _sending ? 'Sending...' : 'Send Feedback',
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  InputDecoration _dec(String label) => InputDecoration(
    labelText: label,
    filled: true,
    fillColor: Colors.white,
    contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 14),
    border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(10),
      borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(10),
      borderSide: const BorderSide(color: Color(0xFF059669), width: 2),
    ),
  );

  String _generateId() {
    final ts = DateTime.now().millisecondsSinceEpoch;
    final rand = DateTime.now().microsecondsSinceEpoch % 1000;
    return 'FB' +
        ts.toRadixString(36).toUpperCase() +
        rand.toRadixString(36).toUpperCase();
  }
}

class _MsgFeedbackRow extends StatefulWidget {
  final String message;
  const _MsgFeedbackRow({required this.message});
  @override
  State<_MsgFeedbackRow> createState() => _MsgFeedbackRowState();
}

class _MsgFeedbackRowState extends State<_MsgFeedbackRow> {
  String? _selected; // 'helpful' | 'not-helpful'
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 6),
      child: Row(
        children: [
          _emojiBtn(context, 'helpful', '👍'),
          const SizedBox(width: 6),
          _emojiBtn(context, 'not-helpful', '👎'),
          const SizedBox(width: 6),
          _reportBtn(context),
        ],
      ),
    );
  }

  Widget _emojiBtn(BuildContext context, String value, String emoji) {
    final selected = _selected == value;
    return InkWell(
      borderRadius: BorderRadius.circular(6),
      onTap: () async {
        setState(() => _selected = value);
        final preview = widget.message.length > 100
            ? widget.message.substring(0, 100) + '...'
            : widget.message;
        await context.read<DemoCubit>().submitMessageFeedback(
          messagePreview: preview,
          feedback: value,
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: selected ? const Color(0xFFECFDF5) : null,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: selected ? const Color(0xFF059669) : const Color(0xFFE2E8F0),
          ),
        ),
        child: Text(emoji, style: const TextStyle(fontSize: 14)),
      ),
    );
  }

  Widget _reportBtn(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(6),
      onTap: () async {
        final excerpt = widget.message.length > 220
            ? widget.message.substring(0, 220) + '...'
            : widget.message;
        await showDialog(
          context: context,
          builder: (dialogCtx) {
            final cubit = context.read<DemoCubit>();
            return BlocProvider.value(
              value: cubit,
              child: _QuickReportDialog(messageExcerpt: excerpt),
            );
          },
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: const Color(0xFFFFFBEB),
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: const Color(0xFFE2E8F0)),
        ),
        child: const Text('⚠', style: TextStyle(fontSize: 14)),
      ),
    );
  }
}

class _QuickReportDialog extends StatefulWidget {
  final String messageExcerpt;
  const _QuickReportDialog({required this.messageExcerpt});
  @override
  State<_QuickReportDialog> createState() => _QuickReportDialogState();
}

class _QuickReportDialogState extends State<_QuickReportDialog> {
  final _form = GlobalKey<FormState>();
  String? _issueType;
  final _description = TextEditingController();
  bool _sending = false;

  @override
  void dispose() {
    _description.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 520),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0xFF7C2D12), Color(0xFFB45309)],
                ),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: const Text(
                '⚠ Report Message Issue',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
            ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.6,
              ),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _form,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Text(
                        'Message excerpt',
                        style: TextStyle(color: Colors.grey[700]),
                      ),
                      const SizedBox(height: 6),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: const Color(0xFFFEFCE8),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: const Color(0xFFF59E0B)),
                        ),
                        child: Text(widget.messageExcerpt),
                      ),
                      const SizedBox(height: 12),
                      DropdownButtonFormField<String>(
                        value: _issueType,
                        decoration: _dec('Issue type'),
                        items: const [
                          DropdownMenuItem(
                            value: 'incorrect',
                            child: Text('Incorrect or misleading'),
                          ),
                          DropdownMenuItem(
                            value: 'offensive',
                            child: Text('Offensive or unsafe'),
                          ),
                          DropdownMenuItem(
                            value: 'irrelevant',
                            child: Text('Irrelevant/low quality'),
                          ),
                          DropdownMenuItem(
                            value: 'other',
                            child: Text('Other'),
                          ),
                        ],
                        onChanged: (v) => setState(() => _issueType = v),
                        validator: (v) =>
                            v == null || v.isEmpty ? 'Required' : null,
                      ),
                      const SizedBox(height: 12),
                      TextFormField(
                        controller: _description,
                        maxLines: 4,
                        decoration: _dec('Describe the issue (optional)')
                            .copyWith(
                              hintText: 'What is wrong with this message?',
                            ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: _sending
                                ? null
                                : () => Navigator.of(context).pop(),
                            child: const Text('Cancel'),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: _sending
                                ? null
                                : () async {
                                    if (!_form.currentState!.validate()) return;
                                    setState(() => _sending = true);
                                    await context
                                        .read<DemoCubit>()
                                        .submitQuickMessageReport(
                                          messageContent: widget.messageExcerpt,
                                          issueType: _issueType!,
                                          description: _description.text.trim(),
                                        );
                                    if (!mounted) return;
                                    Navigator.of(context).pop();
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text(
                                          'Thanks. Report submitted.',
                                        ),
                                      ),
                                    );
                                  },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFFB45309),
                              foregroundColor: Colors.white,
                            ),
                            child: Text(
                              _sending ? 'Submitting...' : 'Submit Report',
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  InputDecoration _dec(String label) => InputDecoration(
    labelText: label,
    filled: true,
    fillColor: Colors.white,
    contentPadding: const EdgeInsets.symmetric(horizontal: 14, vertical: 14),
    border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(10),
      borderSide: const BorderSide(color: Color(0xFFE2E8F0)),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(10),
      borderSide: const BorderSide(color: Color(0xFFB45309), width: 2),
    ),
  );
}

class _AssistantThinking extends StatelessWidget {
  const _AssistantThinking();
  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.centerLeft,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 360),
        padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
        margin: const EdgeInsets.only(bottom: 14),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: const Color(0xFFE2E8F0)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: const [
            SizedBox(
              height: 14,
              width: 14,
              child: CircularProgressIndicator(strokeWidth: 2.2),
            ),
            SizedBox(width: 10),
            Text('Assistant is thinking...'),
          ],
        ),
      ),
    );
  }
}

class _SuccessContent extends StatelessWidget {
  final String id;
  const _SuccessContent({required this.id});
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const SizedBox(height: 8),
        const Icon(Icons.check_circle, color: Color(0xFF059669), size: 40),
        const SizedBox(height: 8),
        const Text(
          'Thanks! Your feedback was submitted.',
          textAlign: TextAlign.center,
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 6),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 24),
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: const Color(0xFFECFDF5),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: const Color(0xFF059669)),
          ),
          child: Column(
            children: [
              const Text(
                'Feedback ID',
                style: TextStyle(color: Color(0xFF065F46)),
              ),
              const SizedBox(height: 4),
              SelectableText(
                id,
                style: const TextStyle(fontFamily: 'monospace'),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            'What happens next: Our team reviews submissions regularly. If contact details were provided, we may reach out for more info.',
            textAlign: TextAlign.center,
            style: TextStyle(color: Color(0xFF334155)),
          ),
        ),
        const SizedBox(height: 16),
        Align(
          alignment: Alignment.center,
          child: TextButton(
            onPressed: () => Navigator.of(context).maybePop(),
            child: const Text('Close'),
          ),
        ),
      ],
    );
  }
}

class _InputRow extends StatefulWidget {
  final TextEditingController controller;
  const _InputRow({required this.controller});
  @override
  State<_InputRow> createState() => _InputRowState();
}

class _InputRowState extends State<_InputRow> {
  @override
  Widget build(BuildContext context) {
    final s = context.watch<DemoCubit>().state;
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 60, vertical: 12),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Color(0xFFE2E8F0))),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: widget.controller,
              enabled: !s.waiting && !s.limitReached,
              decoration: const InputDecoration(
                hintText: 'Ask me about compliance requirements...',
              ),
              onSubmitted: (t) {
                final text = t.trim();
                if (text.isNotEmpty) {
                  context.read<DemoCubit>().sendMessage(text);
                  widget.controller.clear();
                }
              },
            ),
          ),
          const SizedBox(width: 8),
          ElevatedButton(
            onPressed: s.waiting || s.limitReached
                ? null
                : () {
                    final t = widget.controller.text.trim();
                    if (t.isNotEmpty) {
                      context.read<DemoCubit>().sendMessage(t);
                      widget.controller.clear();
                    }
                  },
            child: const Text('Send'),
          ),
        ],
      ),
    );
  }
}

class _DocumentGallery extends StatelessWidget {
  const _DocumentGallery();
  @override
  Widget build(BuildContext context) {
    final docs = context.watch<DemoCubit>().state.documents;
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          const Text(
            '📄 Your Generated Documents',
            style: TextStyle(fontSize: 22, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),
          LayoutBuilder(
            builder: (_, c) {
              return Wrap(
                spacing: 16,
                runSpacing: 16,
                children: [
                  for (final d in docs)
                    InkWell(
                      onTap: () => context.read<DemoCubit>().viewDocument(
                        d['id'].toString(),
                      ),
                      child: Container(
                        width: 300,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(color: const Color(0xFFE2E8F0)),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  (d['icon'] ?? '📄') as String,
                                  style: const TextStyle(fontSize: 20),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    (d['title'] ?? '') as String,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              d['description']?.toString() ?? '',
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 12),
                            Align(
                              alignment: Alignment.centerRight,
                              child: OutlinedButton(
                                onPressed: () => context
                                    .read<DemoCubit>()
                                    .viewDocument(d['id'].toString()),
                                child: const Text('View Document'),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}

class _DocumentViewer extends StatelessWidget {
  const _DocumentViewer();
  @override
  Widget build(BuildContext context) {
    final doc = context.watch<DemoCubit>().state.currentDocument ?? {};
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Color(0xFFF8FAFC),
            border: Border(bottom: BorderSide(color: Color(0xFFE2E8F0))),
          ),
          child: Row(
            children: [
              OutlinedButton(
                onPressed: () => context.read<DemoCubit>().backToDocuments(),
                child: const Text('← Back to Documents'),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  (doc['title'] ?? 'Document') as String,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              TextButton(
                onPressed: () => context.read<DemoCubit>().backToChat(),
                child: const Text('Back to Chat'),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () => context.read<DemoCubit>().openLeadCapture(),
                child: const Text('Request Full Access'),
              ),
            ],
          ),
        ),
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Text((doc['content'] ?? '') as String),
          ),
        ),
      ],
    );
  }
}

class _LimitOverlay extends StatelessWidget {
  const _LimitOverlay();
  @override
  Widget build(BuildContext context) {
    final s = context.watch<DemoCubit>().state;
    return Container(
      color: Colors.black54,
      child: Center(
        child: Container(
          width: 420,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Demo Limit Reached',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700),
              ),
              const SizedBox(height: 8),
              Text(
                'You\'ve reached the demo ${s.limitType ?? 'usage'} limit. You can continue exploring, or request full access.',
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () =>
                        context.read<DemoCubit>().openLeadFromLimit(),
                    child: const Text('Request Access'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () => context.read<DemoCubit>().clearLimit(),
                    child: const Text('Continue'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _LeadCaptureOverlay extends StatelessWidget {
  const _LeadCaptureOverlay();
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black54,
      child: Center(
        child: Container(
          width: 460,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Request Full Access',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700),
              ),
              const SizedBox(height: 8),
              const Text(
                'Want to see the full platform and download documents? Request access and our team will reach out.',
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () =>
                        context.read<DemoCubit>().closeLeadCapture(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () async {
                      await launchUrl(Uri.parse('contact.html'));
                      context.read<DemoCubit>().closeLeadCapture();
                    },
                    child: const Text('Go to Contact Page'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ButtonStyles {
  static final ButtonStyle primary = ElevatedButton.styleFrom(
    backgroundColor: const Color(0xFF059669),
    foregroundColor: Colors.white,
    shape: const StadiumBorder(),
    padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 12),
  );

  static final ButtonStyle outlineChip = OutlinedButton.styleFrom(
    foregroundColor: const Color(0xFF0F172A),
    side: const BorderSide(color: Color(0xFFE2E8F0)),
    shape: const StadiumBorder(),
    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
    backgroundColor: const Color(0xFFFFFFFF),
  );

  static final ButtonStyle textLink = TextButton.styleFrom(
    foregroundColor: const Color(0xFF64748B),
    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
    shape: const StadiumBorder(),
  );
}
