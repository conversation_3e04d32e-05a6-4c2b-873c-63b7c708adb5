<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Register for Our Program</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
      background: linear-gradient(135deg, var(--primary-color, #667eea) 0%, var(--secondary-color, #764ba2) 100%);
      min-height: 100vh;
      padding: 20px;
      line-height: 1.6;
    }

    .container {
      max-width: 700px;
      margin: 0 auto;
      background: white;
      border-radius: 12px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      overflow: hidden;
    }

    .header {
      background: linear-gradient(135deg, var(--primary-color, #667eea) 0%, var(--secondary-color, #764ba2) 100%);
      color: white;
      padding: 40px 30px;
      text-align: center;
      position: relative;
    }

    .product-logo {
      width: 60px;
      height: 60px;
      background: rgba(255,255,255,0.2);
      border-radius: 12px;
      margin: 0 auto 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: bold;
    }

    .header h1 {
      font-size: 2.2em;
      margin-bottom: 10px;
      font-weight: 700;
    }

    .header p {
      font-size: 1.1em;
      opacity: 0.9;
      margin-bottom: 10px;
    }

    .program-badge {
      display: inline-block;
      background: rgba(255,255,255,0.2);
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 0.9em;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      margin-top: 10px;
    }

    .form-content {
      padding: 40px 30px;
    }

    .product-info {
      background: #f8f9fa;
      border-left: 4px solid var(--primary-color, #667eea);
      padding: 20px;
      margin-bottom: 30px;
      border-radius: 0 8px 8px 0;
    }

    .product-info h3 {
      color: #333;
      margin-bottom: 10px;
      font-size: 16px;
    }

    .product-info p {
      color: #666;
      font-size: 14px;
      line-height: 1.5;
    }

    .form-group {
      margin-bottom: 25px;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #333;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    input, textarea, select {
      width: 100%;
      padding: 15px;
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      font-size: 16px;
      transition: all 0.3s ease;
      background: #f8f9fa;
    }

    input:focus, textarea:focus, select:focus {
      outline: none;
      border-color: var(--primary-color, #667eea);
      background: white;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    textarea {
      resize: vertical;
      min-height: 120px;
      font-family: inherit;
    }

    .char-counter {
      text-align: right;
      font-size: 12px;
      color: #666;
      margin-top: 5px;
    }

    .submit-btn {
      width: 100%;
      background: linear-gradient(135deg, var(--primary-color, #667eea) 0%, var(--secondary-color, #764ba2) 100%);
      color: white;
      padding: 18px;
      border: none;
      border-radius: 8px;
      font-size: 18px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 1px;
      position: relative;
      overflow: hidden;
    }

    .submit-btn:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .submit-btn:disabled {
      opacity: 0.7;
      cursor: not-allowed;
      transform: none;
    }

    .status {
      margin-top: 20px;
      padding: 15px;
      border-radius: 8px;
      font-weight: 500;
      display: none;
      animation: slideIn 0.3s ease;
    }

    .status.success {
      background: #d4edda;
      color: #155724;
      border-left: 4px solid #28a745;
    }

    .status.error {
      background: #f8d7da;
      color: #721c24;
      border-left: 4px solid #dc3545;
    }

    .required {
      color: #dc3545;
      font-weight: bold;
    }

    .honeypot {
      position: absolute;
      left: -10000px;
      width: 1px;
      height: 1px;
      overflow: hidden;
    }

    .loading-spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid rgba(255,255,255,.3);
      border-radius: 50%;
      border-top: 3px solid #fff;
      animation: spin 1s linear infinite;
      margin-right: 10px;
    }

    .privacy-notice {
      margin-top: 30px;
      padding: 25px;
      background: #f8f9fa;
      border-radius: 8px;
      border-left: 4px solid #007bff;
    }

    .privacy-notice h3 {
      color: #333;
      margin-bottom: 10px;
      font-size: 16px;
    }

    .privacy-notice p {
      font-size: 13px;
      color: #666;
      line-height: 1.5;
    }

    .privacy-notice a {
      color: #007bff;
      text-decoration: none;
    }

    .privacy-notice a:hover {
      text-decoration: underline;
    }

    /* Product-specific color schemes */
    .arioncomply {
      --primary-color: #2563eb;
      --secondary-color: #1d4ed8;
    }

    .arionsecure {
      --primary-color: #dc2626;
      --secondary-color: #b91c1c;
    }

    .arionanalytics {
      --primary-color: #059669;
      --secondary-color: #047857;
    }

    .arionplatform {
      --primary-color: #7c3aed;
      --secondary-color: #6d28d9;
    }

    .back-link {
      position: absolute;
      top: 20px;
      left: 20px;
      color: rgba(255,255,255,0.8);
      text-decoration: none;
      font-size: 14px;
      transition: color 0.3s ease;
    }

    .back-link:hover {
      color: white;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    @keyframes slideIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @media (max-width: 768px) {
      body {
        padding: 10px;
      }

      .container {
        margin: 10px 0;
      }

      .header {
        padding: 30px 20px;
      }

      .header h1 {
        font-size: 1.8em;
      }

      .form-content {
        padding: 30px 20px;
      }

      input, textarea, select {
        padding: 12px;
        font-size: 16px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <a href="#" id="back-link" class="back-link">← Back to Product Page</a>
      <div class="product-logo" id="product-logo">🔒</div>
      <h1 id="form-heading">Register for Our Program</h1>
      <p id="form-description">Join our exclusive program and be among the first to experience our innovative solution.</p>
      <div class="program-badge" id="program-badge">Pilot Program</div>
    </div>

    <div class="form-content">
      <div class="product-info" id="product-info">
        <h3 id="product-info-title">About This Program</h3>
        <p id="product-info-description">Get early access to our latest features and help shape the future of our platform.</p>
      </div>

      <form id="registration-form" novalidate>
        <!-- Honeypot field -->
        <input type="text" name="hp_field" class="honeypot" tabindex="-1" autocomplete="off">

        <!-- Hidden fields for tracking -->
        <input type="hidden" name="product_id" id="product_id" value="default">
        <input type="hidden" name="program_type" id="program_type" value="pilot">
        <input type="hidden" name="source_url" id="source_url" value="">
        <input type="hidden" name="utm_source" id="utm_source" value="">
        <input type="hidden" name="utm_campaign" id="utm_campaign" value="">

        <div class="form-group">
          <label for="full_name">Full Name <span class="required">*</span></label>
          <input type="text" id="full_name" name="full_name" required maxlength="100" autocomplete="name">
        </div>

        <div class="form-group">
          <label for="email">Email Address <span class="required">*</span></label>
          <input type="email" id="email" name="email" required maxlength="255" autocomplete="email">
        </div>

        <div class="form-group">
          <label for="company">Company <span class="required">*</span></label>
          <input type="text" id="company" name="company" required maxlength="100" autocomplete="organization">
        </div>

        <div class="form-group">
          <label for="job_title">Job Title <span class="required">*</span></label>
          <input type="text" id="job_title" name="job_title" required maxlength="100" autocomplete="organization-title">
        </div>

        <div class="form-group">
          <label for="primary_business">Primary Business/Industry <span class="required">*</span></label>
          <select id="primary_business" name="primary_business" required>
            <option value="">Select your industry...</option>
            <option value="Technology">Technology</option>
            <option value="Financial Services">Financial Services</option>
            <option value="Healthcare">Healthcare</option>
            <option value="Manufacturing">Manufacturing</option>
            <option value="Government">Government</option>
            <option value="Education">Education</option>
            <option value="Retail">Retail</option>
            <option value="Energy">Energy</option>
            <option value="Telecommunications">Telecommunications</option>
            <option value="Other">Other</option>
          </select>
        </div>

        <div class="form-group">
          <label for="company_size">Company Size <span class="required">*</span></label>
          <select id="company_size" name="company_size" required>
            <option value="">Select company size...</option>
            <option value="1-10">1-10 employees</option>
            <option value="11-50">11-50 employees</option>
            <option value="51-200">51-200 employees</option>
            <option value="201-1000">201-1,000 employees</option>
            <option value="1001-5000">1,001-5,000 employees</option>
            <option value="5000+">5,000+ employees</option>
          </select>
        </div>

        <div class="form-group">
          <label for="phone">Phone Number <span class="required">*</span></label>
          <input type="tel" id="phone" name="phone" required maxlength="20" autocomplete="tel">
        </div>

        <div class="form-group">
          <label for="use_case">Describe Your Use Case <span class="required">*</span></label>
          <textarea id="use_case" name="use_case" required maxlength="1000"
                    placeholder="Please describe your current challenges, how you plan to use our solution, and what outcomes you're hoping to achieve..."></textarea>
          <div class="char-counter">
            <span id="char-count">0</span>/1000 characters
          </div>
        </div>

        <div class="form-group">
          <label for="timeline">Expected Implementation Timeline</label>
          <select id="timeline" name="timeline">
            <option value="">Select timeline...</option>
            <option value="Immediately">Immediately (within 1 month)</option>
            <option value="1-3 months">1-3 months</option>
            <option value="3-6 months">3-6 months</option>
            <option value="6-12 months">6-12 months</option>
            <option value="12+ months">12+ months</option>
            <option value="Just exploring">Just exploring options</option>
          </select>
        </div>

        <button type="submit" class="submit-btn" id="submit-btn">
          <span id="submit-text">Submit Registration</span>
          <div id="submit-loading" class="loading-spinner" style="display: none;"></div>
        </button>

        <div id="status" class="status"></div>
      </form>

      <div class="privacy-notice">
        <h3>🔒 Privacy Notice</h3>
        <p>We collect the information above solely to evaluate participants for our programs. Your data is encrypted in transit (TLS) and at rest (AES-256). We retain data for 3 years and review annually. You have rights of access, rectification, erasure, restriction, portability, and objection under GDPR. Contact our Data Protection Officer at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
      </div>
    </div>
  </div>

  <script>
    // ===== CONFIGURATION =====
    const CONFIG = {
      SUPABASE_URL: 'https://dxncozbhwppvwpugoqjk.supabase.co',

      // Product configurations
      PRODUCT_CONFIGS: {
        'arioncomply': {
          name: 'ArionComply',
          logo: '🛡️',
          domain: 'iso.arionetworks.com',
          colors: 'arioncomply',
          pilot: {
            title: 'Join the ArionComply Pilot Program',
            description: 'Be among the first to experience our comprehensive ISO compliance management platform.',
            badge: 'ISO Compliance Pilot',
            info: {
              title: 'About ArionComply Pilot',
              description: 'ArionComply streamlines ISO 27001, 27701, and other compliance frameworks with automated controls, evidence collection, and audit preparation. Pilot participants get 6 months free access and priority support.'
            }
          },
          waitlist: {
            title: 'Join the ArionComply Waitlist',
            description: 'Get notified when ArionComply becomes available for your organization.',
            badge: 'ISO Compliance Waitlist',
            info: {
              title: 'About ArionComply',
              description: 'ArionComply is our comprehensive ISO compliance management platform launching soon. Join the waitlist to be notified of availability and special launch pricing.'
            }
          }
        },

        'arionsecure': {
          name: 'ArionSecure',
          logo: '🔐',
          domain: 'security.arionetworks.com',
          colors: 'arionsecure',
          pilot: {
            title: 'ArionSecure Pilot Program',
            description: 'Experience next-generation security monitoring and threat detection.',
            badge: 'Security Pilot',
            info: {
              title: 'About ArionSecure Pilot',
              description: 'ArionSecure provides advanced threat detection, incident response automation, and security orchestration. Pilot participants receive full platform access and dedicated security consultant support.'
            }
          },
          waitlist: {
            title: 'ArionSecure Waitlist',
            description: 'Be the first to know when our advanced security platform launches.',
            badge: 'Security Waitlist',
            info: {
              title: 'About ArionSecure',
              description: 'Our advanced security platform with AI-powered threat detection and automated response capabilities. Join the waitlist for early access and special pricing.'
            }
          }
        },

        'arionanalytics': {
          name: 'ArionAnalytics',
          logo: '📊',
          domain: 'analytics.arionetworks.com',
          colors: 'arionanalytics',
          pilot: {
            title: 'ArionAnalytics Pilot Program',
            description: 'Transform your data into actionable business intelligence.',
            badge: 'Analytics Pilot',
            info: {
              title: 'About ArionAnalytics Pilot',
              description: 'ArionAnalytics delivers real-time business intelligence with custom dashboards, predictive analytics, and automated reporting. Pilot includes unlimited data sources and premium support.'
            }
          },
          waitlist: {
            title: 'ArionAnalytics Waitlist',
            description: 'Get early access to our powerful analytics platform.',
            badge: 'Analytics Waitlist',
            info: {
              title: 'About ArionAnalytics',
              description: 'Comprehensive business intelligence platform with AI-powered insights and predictive analytics. Join the waitlist to be among the first to access our analytics suite.'
            }
          }
        },

        'arionplatform': {
          name: 'ArionPlatform',
          logo: '🚀',
          domain: 'platform.arionetworks.com',
          colors: 'arionplatform',
          pilot: {
            title: 'ArionPlatform Early Access',
            description: 'Get exclusive access to our integrated business operations platform.',
            badge: 'Platform Pilot',
            info: {
              title: 'About ArionPlatform Pilot',
              description: 'ArionPlatform integrates compliance, security, and analytics into one unified workspace. Early access participants help shape the platform with direct input to our development team.'
            }
          },
          waitlist: {
            title: 'ArionPlatform Waitlist',
            description: 'Be notified when our integrated platform becomes available.',
            badge: 'Platform Waitlist',
            info: {
              title: 'About ArionPlatform',
              description: 'The unified platform that brings together all ArionNetworks solutions. Join the waitlist to be among the first to experience our integrated approach to business operations.'
            }
          }
        },

        'default': {
          name: 'ArionNetworks',
          logo: '🌐',
          domain: 'arionetworks.com',
          colors: 'default',
          pilot: {
            title: 'Join Our Pilot Program',
            description: 'Experience the future of business technology solutions.',
            badge: 'Pilot Program',
            info: {
              title: 'About Our Pilot Program',
              description: 'Get early access to our innovative business solutions and help shape their development with your feedback and requirements.'
            }
          },
          waitlist: {
            title: 'Join Our Waitlist',
            description: 'Be the first to know about our latest innovations.',
            badge: 'Product Waitlist',
            info: {
              title: 'About Our Products',
              description: 'ArionNetworks delivers comprehensive business solutions for compliance, security, and analytics. Join our waitlist to be notified of new product launches and opportunities.'
            }
          }
        }
      }
    };

    // ===== UTILITY FUNCTIONS =====
    function getQueryParam(param) {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get(param);
    }

    function showStatus(message, type) {
      const statusEl = document.getElementById('status');
      statusEl.textContent = message;
      statusEl.className = `status ${type}`;
      statusEl.style.display = 'block';

      if (type === 'success') {
        setTimeout(() => {
          statusEl.style.display = 'none';
        }, 8000);
      }

      statusEl.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    function setLoading(isLoading) {
      const submitBtn = document.getElementById('submit-btn');
      const submitText = document.getElementById('submit-text');
      const submitLoading = document.getElementById('submit-loading');

      submitBtn.disabled = isLoading;
      submitText.style.display = isLoading ? 'none' : 'inline';
      submitLoading.style.display = isLoading ? 'inline-block' : 'none';
    }

    function validateForm(formData) {
      const errors = [];

      const requiredFields = {
        'full_name': 'Full Name',
        'email': 'Email Address',
        'company': 'Company',
        'job_title': 'Job Title',
        'primary_business': 'Primary Business/Industry',
        'company_size': 'Company Size',
        'phone': 'Phone Number',
        'use_case': 'Use Case Description'
      };

      Object.entries(requiredFields).forEach(([field, label]) => {
        if (!formData[field] || !formData[field].trim()) {
          errors.push(`${label} is required`);
        }
      });

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (formData.email && !emailRegex.test(formData.email.trim())) {
        errors.push('Please enter a valid email address');
      }

      const phoneRegex = /^[\d\s\-\+\(\)]{10,20}$/;
      if (formData.phone && !phoneRegex.test(formData.phone.replace(/\s/g, ''))) {
        errors.push('Please enter a valid phone number (minimum 10 digits)');
      }

      if (formData.use_case && formData.use_case.trim().length < 20) {
        errors.push('Please provide a more detailed use case description (minimum 20 characters)');
      }

      return errors;
    }

    function updateCharacterCount() {
      const textarea = document.getElementById('use_case');
      const counter = document.getElementById('char-count');
      const currentLength = textarea.value.length;
      counter.textContent = currentLength;

      if (currentLength > 900) {
        counter.style.color = '#dc3545';
      } else if (currentLength > 800) {
        counter.style.color = '#ffc107';
      } else {
        counter.style.color = '#666';
      }
    }

    // ===== INITIALIZATION =====
    document.addEventListener('DOMContentLoaded', async () => {
      // Get URL parameters
      const productId = getQueryParam('product') || getQueryParam('product_id') || 'default';
      const programType = getQueryParam('type') || getQueryParam('program_type') || 'pilot';
      const sourceUrl = getQueryParam('source') || document.referrer || '';
      const utmSource = getQueryParam('utm_source') || '';
      const utmCampaign = getQueryParam('utm_campaign') || '';

      // Get product configuration
      const productConfig = CONFIG.PRODUCT_CONFIGS[productId] || CONFIG.PRODUCT_CONFIGS['default'];
      const programConfig = productConfig[programType] || productConfig['pilot'];

      // Apply product colors
      document.body.className = productConfig.colors;

      // Set form values
      document.getElementById('product_id').value = productId;
      document.getElementById('program_type').value = programType;
      document.getElementById('source_url').value = sourceUrl;
      document.getElementById('utm_source').value = utmSource;
      document.getElementById('utm_campaign').value = utmCampaign;

      // Update UI elements
      document.getElementById('product-logo').textContent = productConfig.logo;
      document.getElementById('form-heading').textContent = programConfig.title;
      document.getElementById('form-description').textContent = programConfig.description;
      document.getElementById('program-badge').textContent = programConfig.badge;

      // Update product info section
      document.getElementById('product-info-title').textContent = programConfig.info.title;
      document.getElementById('product-info-description').textContent = programConfig.info.description;

      // Set back link
      const backLink = document.getElementById('back-link');
      if (sourceUrl) {
        backLink.href = sourceUrl;
        backLink.style.display = 'block';
      } else if (productConfig.domain) {
        backLink.href = `https://${productConfig.domain}`;
        backLink.style.display = 'block';
      } else {
        backLink.style.display = 'none';
      }

      // Update page title
      document.title = `${programConfig.title} - ${productConfig.name}`;

      // Set up character counter
      const textarea = document.getElementById('use_case');
      textarea.addEventListener('input', updateCharacterCount);

      // Focus first input
      document.getElementById('full_name').focus();

      // Track page view
      if (window.gtag) {
        window.gtag('event', 'page_view', {
          page_title: document.title,
          product_id: productId,
          program_type: programType
        });
      }
    });

    // ===== FORM SUBMISSION =====
    document.getElementById('registration-form').addEventListener('submit', async (e) => {
      e.preventDefault();

      document.getElementById('status').style.display = 'none';

      const form = e.target;
      const formData = new FormData(form);
      const data = Object.fromEntries(formData.entries());

      // Trim whitespace
      Object.keys(data).forEach(key => {
        if (typeof data[key] === 'string') {
          data[key] = data[key].trim();
        }
      });

      // Client-side validation
      const validationErrors = validateForm(data);
      if (validationErrors.length > 0) {
        showStatus(`Please fix the following errors: ${validationErrors.join('. ')}`, 'error');
        return;
      }

      setLoading(true);

      try {
        const response = await fetch(`${CONFIG.SUPABASE_URL}/functions/v1/PilotRegistration-processor`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify(data)
        });

        const result = await response.json();

        if (response.ok) {
          const productConfig = CONFIG.PRODUCT_CONFIGS[data.product_id] || CONFIG.PRODUCT_CONFIGS['default'];
          showStatus(`🎉 Thank you! Your ${productConfig.name} registration has been received successfully. We'll be in touch soon!`, 'success');
          form.reset();

          // Reset hidden fields
          document.getElementById('product_id').value = data.product_id;
          document.getElementById('program_type').value = data.program_type;
          document.getElementById('source_url').value = data.source_url;
          updateCharacterCount();

          // Track successful submission
          if (window.gtag) {
            window.gtag('event', 'form_submit', {
              event_category: 'engagement',
              event_label: `${data.product_id}_${data.program_type}`,
              product_id: data.product_id,
              program_type: data.program_type
            });
          }
        } else {
          const errorMessage = result.error || 'Registration failed. Please try again.';
          showStatus(`❌ ${errorMessage}`, 'error');

          console.error('Registration failed:', {
            status: response.status,
            error: result.error
          });
        }

      } catch (error) {
        console.error('Submission error:', error);

        if (error.name === 'TypeError' && error.message.includes('fetch')) {
          showStatus('❌ Network error. Please check your internet connection and try again.', 'error');
        } else {
          showStatus('❌ An unexpected error occurred. Please try again.', 'error');
        }
      } finally {
        setLoading(false);
      }
    });

    // ===== UX ENHANCEMENTS =====

    // Email validation feedback
    document.getElementById('email').addEventListener('blur', function() {
      const email = this.value.trim();
      if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        this.style.borderColor = '#dc3545';
      } else {
        this.style.borderColor = '#e1e5e9';
      }
    });

    // Phone number formatting
    document.getElementById('phone').addEventListener('input', function() {
      let value = this.value.replace(/\D/g, '');
      if (value.length >= 10) {
        value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
      }
      this.value = value;
    });

    // Prevent Enter submission in inputs
    document.querySelectorAll('input[type="text"], input[type="email"], input[type="tel"]').forEach(input => {
      input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          e.preventDefault();
          const inputs = Array.from(document.querySelectorAll('input, textarea, select'));
          const currentIndex = inputs.indexOf(this);
          if (currentIndex < inputs.length - 1) {
            inputs[currentIndex + 1].focus();
          }
        }
      });
    });
  </script>
</body>
</html>
