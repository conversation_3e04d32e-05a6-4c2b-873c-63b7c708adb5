<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arion A5EP - Autonomous 5G Enterprise Platform</title>
    <style>
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
            color: #1f2937;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }
        
        .logo-header {
            height: 80px;
            background: transparent;
            display: flex;
            justify-content: center;
            align-items: center;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .logo-placeholder {
            width: 200px;
            height: 60px;
            background: transparent;
            border: 2px dashed rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(0,0,0,0.4);
            font-size: 1.2em;
            font-weight: 600;
        }
        
        nav {
            background: transparent;
            padding: 16px 0;
            box-shadow: none;
            position: relative;
            width: 100%;
            z-index: 100;
        }
        
        nav .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        nav .logo {
            font-weight: 800;
            font-size: 1.4em;
            color: #ea580c;
            letter-spacing: -0.5px;
        }
        
        nav .family {
            color: #64748b;
            font-size: 0.8em;
            margin-left: 12px;
        }
        
        nav a {
            text-decoration: none;
            color: #475569;
            margin: 0 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        nav a:hover {
            color: #ea580c;
        }
        
        .back-link {
            color: #64748b !important;
            font-size: 0.9em;
        }
        
        .back-link:hover {
            color: #ea580c !important;
        }
        
        .hero {
            text-align: center;
            margin: 40px 0 60px;
            padding: 60px 0;
        }
        
        .hero h1 {
            font-size: 3.5em;
            font-weight: 900;
            margin: 0 0 20px;
            color: #0f172a;
            letter-spacing: -2px;
        }
        
        .hero .subtitle {
            font-size: 1.4em;
            color: #ea580c;
            font-weight: 600;
            margin-bottom: 16px;
        }
        
        .hero .description {
            font-size: 1.2em;
            color: #64748b;
            max-width: 800px;
            margin: 0 auto 40px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 60px 0;
        }
        
        .stat-card {
            background: white;
            padding: 30px;
            border-radius: 16px;
            text-align: center;
            box-shadow: 0 4px 25px rgba(0,0,0,0.06);
            border: 1px solid rgba(234, 88, 12, 0.1);
            border-top: 4px solid #ea580c;
        }
        
        .stat-number {
            font-size: 3em;
            font-weight: 900;
            color: #ea580c;
            margin: 0;
            line-height: 1;
        }
        
        .stat-label {
            color: #64748b;
            font-weight: 500;
            margin-top: 8px;
        }
        
        .demo-section {
            background: white;
            border-radius: 20px;
            padding: 60px 0;
            margin: 80px 0;
            box-shadow: 0 8px 40px rgba(0,0,0,0.06);
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .demo-header h2 {
            font-size: 2.5em;
            font-weight: 800;
            color: #0f172a;
            margin-bottom: 16px;
        }
        
        .chat-interface {
            max-width: 800px;
            margin: 0 auto;
            background: #f8fafc;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
        }
        
        .chat-header {
            background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);
            color: white;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .avatar {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .chat-info h3 {
            margin: 0;
            font-size: 1.1em;
        }
        
        .chat-info p {
            margin: 0;
            opacity: 0.9;
            font-size: 0.9em;
        }
        
        .chat-messages {
            padding: 30px;
            height: 650px;
            overflow-y: auto;
            background: #f8fafc;
        }
        
        .message {
            margin-bottom: 20px;
            display: flex;
            gap: 12px;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message.user .bubble {
            background: #3b82f6;
            color: white;
            margin-left: 80px;
        }
        
        .message.ai .bubble {
            background: white;
            border: 1px solid #e5e7eb;
            margin-right: 80px;
        }
        
        .bubble {
            padding: 16px 20px;
            border-radius: 18px;
            max-width: 500px;
            line-height: 1.5;
        }
        
        .source-note {
            font-size: 0.8em;
            color: #64748b;
            margin-top: 8px;
            font-style: italic;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 80px 0;
        }
        
        .feature-card {
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 4px 25px rgba(0,0,0,0.06);
            border-left: 6px solid #ea580c;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 40px rgba(0,0,0,0.1);
        }
        
        .feature-card h3 {
            color: #0f172a;
            font-size: 1.4em;
            font-weight: 700;
            margin-bottom: 16px;
        }
        
        .feature-card p {
            color: #64748b;
            line-height: 1.7;
        }
        
        .architecture-section {
            background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
            padding: 80px 0;
            margin: 80px 0;
            border-radius: 20px;
        }
        
        .architecture-stack {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .layer {
            background: white;
            margin: 8px 0;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 2px 15px rgba(0,0,0,0.05);
            border-left: 4px solid #ea580c;
            transition: all 0.3s ease;
        }
        
        .layer:hover {
            transform: translateX(10px);
            box-shadow: 0 4px 25px rgba(0,0,0,0.1);
        }
        
        .layer-title {
            font-size: 1.2em;
            font-weight: 700;
            color: #0f172a;
            margin-bottom: 8px;
        }
        
        .layer-desc {
            color: #64748b;
            font-size: 0.9em;
        }
        
        .trust-section {
            text-align: center;
            margin: 80px 0;
        }
        
        .trust-section h2 {
            font-size: 2.5em;
            font-weight: 800;
            color: #0f172a;
            margin-bottom: 40px;
        }
        
        .trust-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        
        .trust-card {
            background: white;
            padding: 40px;
            border-radius: 16px;
            box-shadow: 0 4px 25px rgba(0,0,0,0.06);
            border-top: 4px solid #ea580c;
        }
        
        .trust-card h3 {
            color: #0f172a;
            font-size: 1.3em;
            font-weight: 700;
            margin-bottom: 16px;
        }
        
        .cta-section {
            background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
            border-radius: 20px;
            margin: 80px 0;
        }
        
        .cta-section h2 {
            font-size: 2.5em;
            font-weight: 800;
            margin-bottom: 20px;
        }
        
        .cta-section p {
            font-size: 1.3em;
            margin-bottom: 40px;
            opacity: 0.95;
        }
        
        .btn {
            display: inline-block;
            background: white;
            color: #ea580c;
            padding: 16px 40px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 700;
            font-size: 1.1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(255,255,255,0.3);
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(255,255,255,0.4);
        }
        
        footer {
            background: #1f2937;
            color: white;
            padding: 60px 0 40px;
            text-align: center;
        }
        
        footer .logo {
            font-size: 1.8em;
            font-weight: 800;
            color: #ea580c;
            margin-bottom: 12px;
        }
        
        footer .family {
            color: #9ca3af;
            margin-bottom: 20px;
        }
        
        footer a {
            color: #9ca3af;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        footer a:hover {
            color: #ea580c;
        }
        
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5em;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .chat-messages {
                height: 400px;
            }
            
            .message.user .bubble,
            .message.ai .bubble {
                margin-left: 0;
                margin-right: 0;
                max-width: 280px;
            }
        }
    </style>
</head>
<body>

    <!-- Logo Header -->
    <div class="logo-header">
        <img src="/images/Arion_Logo_icononly_transparent_nobuffer-xd8KJ6FXDvFFGPnNzegd0g.png" alt="Arion Networks" style="height: 60px; width: auto;">
    </div>

    <nav>
        <div class="container">
            <div>
                <span class="logo">Arion A5EP</span>
                <span class="family">Family of Arion Networks</span>
            </div>
            <div>
                <a href="#demo">AI Demo</a>
                <a href="#features">Features</a>
                <a href="#architecture">Architecture</a>
                <a href="#contact">Contact</a>
                <a href="https://arionetworks.com" class="back-link">← Back to Arion Networks</a>
            </div>
        </div>
    </nav>

    <div class="container">
        
        <div class="hero">
            <h1>Arion A5EP</h1>
            <p class="subtitle">Autonomous 5G Enterprise Platform</p>
            <p class="description">AI-driven network management that deploys, optimizes, and maintains itself with unprecedented reliability. Experience the future of truly autonomous 5G infrastructure.</p>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">95%</div>
                <div class="stat-label">Reduction in Manual Interventions</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">10x</div>
                <div class="stat-label">Faster Deployment & Configuration</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">80%</div>
                <div class="stat-label">Lower Mean Time To Repair (MTTR)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">60%+</div>
                <div class="stat-label">Total Cost of Ownership (TCO) Reduction</div>
            </div>
        </div>

    </div>

    <div class="demo-section" id="demo">
        <div class="container">
            <div class="demo-header">
                <h2>Meet Your AI Network Operations Assistant</h2>
                <p style="font-size: 1.2em; color: #64748b;">Experience conversational 5G network management with full transparency and control</p>
            </div>
            
            <div class="chat-interface">
                <div class="chat-header">
                    <div class="avatar">A5</div>
                    <div class="chat-info">
                        <h3>Arion A5EP AI Assistant</h3>
                        <p>Network Operations Expert • Autonomous & Transparent</p>
                    </div>
                    <div style="margin-left: auto; width: 12px; height: 12px; background: #f97316; border-radius: 50%; animation: pulse 2s infinite;"></div>
                </div>
                
                <div class="chat-messages">
                    <div class="message user">
                        <div class="bubble">
                            Show me the current network status for our manufacturing facility
                        </div>
                    </div>
                    
                    <div class="message ai">
                        <div class="bubble">
                            I've analyzed your manufacturing facility network. Here's the current status:
                            <br><br>
                            <strong>✅ Network Health:</strong> 98% optimal performance<br>
                            <strong>✅ 5G Coverage:</strong> 100% facility coverage, 47 active gNBs<br>
                            <strong>⚠️ Capacity:</strong> 3 gNBs approaching 80% utilization<br>
                            <strong>✅ Latency:</strong> Average 2.1ms (target: <5ms)
                            <div class="source-note">Source: Real-time network telemetry, gNB performance metrics, traffic analytics</div>
                        </div>
                    </div>
                    
                    <div class="message user">
                        <div class="bubble">
                            Optimize network for new robotic assembly line deployment
                        </div>
                    </div>
                    
                    <div class="message ai">
                        <div class="bubble">
                            <strong>🤖 Network Optimization Initiated</strong>
                            <br><br>
                            I'm configuring your network for robotic assembly line requirements:
                            <br><br>
                            <strong>✅ Actions Completed:</strong><br>
                            • Created dedicated network slice (URLLC profile)<br>
                            • Allocated 200 MHz spectrum for ultra-low latency<br>
                            • Configured QoS policies for robotic traffic<br>
                            • Activated edge computing resources
                            <br><br>
                            <strong>📊 Expected Performance:</strong><br>
                            • Latency: <1ms guaranteed<br>
                            • Reliability: 99.999% uptime<br>
                            • Bandwidth: 10 Gbps dedicated capacity
                            <br><br>
                            Network slice is ready for robotic deployment. All configurations have been automatically documented and logged.
                            <div class="source-note">Source: Network slicing engine, spectrum management, QoS orchestrator</div>
                        </div>
                    </div>
                    
                    <div class="message user">
                        <div class="bubble">
                            A gNB went offline unexpectedly, what's happening?
                        </div>
                    </div>
                    
                    <div class="message ai">
                        <div class="bubble">
                            <strong>🔧 Self-Healing Protocol Activated</strong>
                            <br><br>
                            I've detected and resolved the gNB failure automatically:
                            <br><br>
                            <strong>🔍 Root Cause Analysis:</strong><br>
                            • gNB-047 lost power due to electrical fault<br>
                            • Detected at 14:23:15 via heartbeat monitoring<br>
                            • Coverage gap identified in Sector 7
                            <br><br>
                            <strong>⚡ Immediate Actions Taken:</strong><br>
                            • Adjacent gNBs increased power output by 15%<br>
                            • Traffic automatically rerouted to backup paths<br>
                            • Maintenance ticket created (Priority: High)<br>
                            • Backup RRU activated for temporary coverage
                            <br><br>
                            <strong>📈 Current Status:</strong><br>
                            • Network coverage: 100% maintained<br>
                            • Service interruption: 0 seconds<br>
                            • Estimated repair time: 2 hours
                            <div class="source-note">Source: Self-healing engine, fault management system, maintenance scheduler</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <div id="features">
            <div style="text-align: center; margin-bottom: 60px;">
                <h2 style="font-size: 2.5em; font-weight: 800; color: #0f172a; margin-bottom: 20px;">Autonomous Operations Cycle</h2>
                <p style="font-size: 1.2em; color: #64748b; max-width: 800px; margin: 0 auto;">Complete lifecycle automation for enterprise 5G networks with AI-driven intelligence at every stage.</p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <h3>Self-Deploying Infrastructure</h3>
                    <p>AI-driven orchestration automatically deploys 5G infrastructure on bare-metal servers. Zero-touch provisioning with intelligent resource allocation and configuration management.</p>
                </div>
                
                <div class="feature-card">
                    <h3>Self-Managing Networks</h3>
                    <p>Cognitive AI continuously optimizes network performance, manages traffic flows, and adjusts configurations in real-time based on usage patterns and business requirements.</p>
                </div>
                
                <div class="feature-card">
                    <h3>Self-Healing Operations</h3>
                    <p>Predictive analytics detect and resolve issues before they impact services. Automatic fault isolation, traffic rerouting, and resource reallocation ensure continuous operation.</p>
                </div>
                
                <div class="feature-card">
                    <h3>Intelligent Network Slicing</h3>
                    <p>Dynamic creation and management of network slices for different use cases. Automated QoS enforcement and resource allocation based on application requirements.</p>
                </div>
                
                <div class="feature-card">
                    <h3>Edge Computing Integration</h3>
                    <p>Seamless orchestration of edge computing resources with 5G infrastructure. AI-driven workload placement and resource optimization for ultra-low latency applications.</p>
                </div>
                
                <div class="feature-card">
                    <h3>Conversational Operations</h3>
                    <p>Natural language interface for network management. Ask questions, request optimizations, and receive detailed explanations of all network operations and decisions.</p>
                </div>
            </div>
        </div>

    </div>

    <div class="architecture-section" id="architecture">
        <div class="container">
            <div style="text-align: center; margin-bottom: 60px;">
                <h2 style="font-size: 2.5em; font-weight: 800; color: #0f172a; margin-bottom: 20px;">Platform Architecture</h2>
                <p style="font-size: 1.2em; color: #64748b;">Layered architecture designed for autonomy, scalability, and enterprise reliability</p>
            </div>
            
            <div class="architecture-stack">
                <div class="layer">
                    <div class="layer-title">AI/ML Cognitive Layer</div>
                    <div class="layer-desc">Predictive Analytics • Self-Healing Logic • Natural Language Processing • Decision Engine</div>
                </div>
                
                <div class="layer">
                    <div class="layer-title">Management & Orchestration (MANO)</div>
                    <div class="layer-desc">AI-Assisted Control Plane • Avatar Interface • Network Slicing • APIs & Integration</div>
                </div>
                
                <div class="layer">
                    <div class="layer-title">5G Core & RAN Management</div>
                    <div class="layer-desc">Proven 5G Core • O-RAN Control • Subscriber Management • Edge Computing</div>
                </div>
                
                <div class="layer">
                    <div class="layer-title">Infrastructure Abstraction</div>
                    <div class="layer-desc">Container Orchestration • SDN/NFV • Resource Management • Security Framework</div>
                </div>
                
                <div class="layer" style="background: #f3f4f6; color: #6b7280;">
                    <div class="layer-title">Standard Enterprise Hardware</div>
                    <div class="layer-desc">COTS Servers • High-Speed NICs • Storage Systems • Network Equipment</div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <div class="trust-section">
            <h2>Autonomous Yet Transparent</h2>
            <p style="font-size: 1.2em; color: #64748b; max-width: 800px; margin: 0 auto 40px;">Full automation with complete visibility into every decision and action taken by the AI system.</p>
            
            <div class="trust-grid">
                <div class="trust-card">
                    <h3>Decision Transparency</h3>
                    <p>Every automated action includes detailed reasoning and data sources. Understand exactly why and how the AI made each network optimization decision.</p>
                </div>
                
                <div class="trust-card">
                    <h3>Performance Predictability</h3>
                    <p>AI models provide confidence intervals and performance predictions for all changes, ensuring reliable and predictable network behavior.</p>
                </div>
                
                <div class="trust-card">
                    <h3>Human Override Control</h3>
                    <p>Maintain full control with the ability to override any automated decision, set operational boundaries, and define custom policies for AI behavior.</p>
                </div>
            </div>
        </div>

    </div>

    <div class="cta-section" id="contact">
        <div class="container">
            <h2>Join Our Exclusive Pilot Program</h2>
            <p>Be among the first enterprises to deploy truly autonomous 5G infrastructure. Limited pilot program available for forward-thinking organizations.</p>
            <a href="#" class="btn">Apply for Pilot Program</a>
        </div>
    </div>

    <footer>
        <div class="container">
            <div class="logo">Arion A5EP</div>
            <div class="family">Family of Arion Networks</div>
            <p style="color: #9ca3af; margin-bottom: 30px;">Autonomous 5G Enterprise Platform with AI-driven network management and transparent operations.</p>
            <div style="border-top: 1px solid #374151; padding-top: 30px;">
                <p style="color: #6b7280;">&copy; 2025 Arion Networks. All rights reserved.</p>
                <a href="https://arionetworks.com">Visit Arion Networks →</a>
            </div>
        </div>
    </footer>

    <style>
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>

</body>
</html>
