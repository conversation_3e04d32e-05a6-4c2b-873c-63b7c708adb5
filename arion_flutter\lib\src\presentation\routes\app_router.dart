import 'package:go_router/go_router.dart';
import '../../features/landing/presentation/views/landing_page.dart';
import '../../features/demo/presentation/views/demo_page.dart';
import '../../features/admin/presentation/views/admin_page.dart';
import '../../features/auth/presentation/views/verify_page.dart';

class AppRoutes {
  static const String landing = '/';
  static const String demo = '/demo';
  static const String admin = '/admin';
  static const String verify = '/verify';
}

class AppRouter {
  static final GoRouter router = GoRouter(
    routes: [
      GoRoute(
        path: AppRoutes.landing,
        name: 'landing',
        builder: (context, state) => const LandingPage(),
      ),
      GoRoute(
        path: AppRoutes.demo,
        name: 'demo',
        builder: (context, state) {
          final adminParam = state.uri.queryParameters['admin'] == 'true';
          return DemoPage(adminFromLink: adminParam);
        },
      ),
      GoRoute(
        path: AppRoutes.admin,
        name: 'admin',
        builder: (context, state) => const AdminPage(),
      ),
      GoRoute(
        path: AppRoutes.verify,
        name: 'verify',
        builder: (context, state) =>
            VerifyPage(token: state.uri.queryParameters['token']),
      ),
    ],
    // Normalize trailing slash
    redirect: (context, state) {
      final p = state.uri.path;
      if (p != '/' && p.endsWith('/')) {
        final q = state.uri.hasQuery ? '?${state.uri.query}' : '';
        return p.substring(0, p.length - 1) + q;
      }
      return null;
    },
    errorBuilder: (context, state) => const LandingPage(),
    debugLogDiagnostics: false,
  );
}
