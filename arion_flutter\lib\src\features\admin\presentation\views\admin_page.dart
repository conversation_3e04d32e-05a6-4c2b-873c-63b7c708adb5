import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import '../../../../domain/repositories/admin_repository.dart';
import '../blocs/admin/admin_cubit.dart';

class AdminPage extends StatelessWidget {
  const AdminPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => AdminCubit(GetIt.I<AdminRepository>()),
      child: const _AdminView(),
    );
  }
}

class _AdminView extends StatefulWidget {
  const _AdminView();
  @override
  State<_AdminView> createState() => _AdminViewState();
}

class _AdminViewState extends State<_AdminView> {
  final _email = TextEditingController();
  final _password = TextEditingController();
  final _code = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AdminCubit, AdminState>(
      builder: (context, state) {
        final cubit = context.read<AdminCubit>();
        if (state.adminToken == null) {
          return _LoginScreen(
            stepMfa: state.stepMfa,
            busy: state.busy,
            error: state.error,
            email: _email,
            password: _password,
            code: _code,
            onContinue: () => cubit.requestCode(
              email: _email.text.trim(),
              password: _password.text.trim(),
            ),
            onBack: cubit.backToPassword,
            onVerify: () => cubit.verifyCode(_code.text.trim()),
            onResend: () => cubit.requestCode(
              email: _email.text.trim(),
              password: _password.text.trim(),
            ),
          );
        }
        return _Dashboard(
          state: state,
          onRefresh: cubit.loadInitialData,
          onCleanup: cubit.cleanup,
          onLogout: cubit.logout,
          onTab: cubit.setTab,
          onManage: cubit.manageUser,
        );
      },
    );
  }
}

// Minimal UI by reusing styles from legacy file (copy-paste of helpers removed for brevity)
class _LoginScreen extends StatelessWidget {
  final bool stepMfa;
  final bool busy;
  final String? error;
  final TextEditingController email;
  final TextEditingController password;
  final TextEditingController code;
  final VoidCallback onContinue;
  final VoidCallback onBack;
  final VoidCallback onVerify;
  final VoidCallback onResend;
  const _LoginScreen({
    required this.stepMfa,
    required this.busy,
    this.error,
    required this.email,
    required this.password,
    required this.code,
    required this.onContinue,
    required this.onBack,
    required this.onVerify,
    required this.onResend,
  });
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 420),
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: const [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 16,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const Text(
                  '🔒 ArionComply Admin Access',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: Color(0xFF059669),
                  ),
                ),
                const SizedBox(height: 20),
                if (!stepMfa) ...[
                  const Text(
                    'Admin Email',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 6),
                  TextField(
                    controller: email,
                    enabled: !busy,
                    keyboardType: TextInputType.emailAddress,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      hintText: '<EMAIL>',
                    ),
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    'Password',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 6),
                  TextField(
                    controller: password,
                    enabled: !busy,
                    obscureText: true,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      hintText: '••••••••',
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: busy ? null : onContinue,
                    child: Text(busy ? 'Verifying...' : 'Continue to MFA'),
                  ),
                ] else ...[
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF1F5F9),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        const Text(
                          '📧 Security code sent to',
                          style: TextStyle(color: Color(0xFF475569)),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          email.text.trim().isEmpty
                              ? 'your email'
                              : email.text.trim(),
                          style: const TextStyle(fontWeight: FontWeight.w700),
                        ),
                        const SizedBox(height: 4),
                        const Text(
                          'Check your email and enter the 6-digit code below',
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(0xFF475569),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    'Enter 6-digit code',
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                  const SizedBox(height: 6),
                  TextField(
                    controller: code,
                    enabled: !busy,
                    maxLength: 6,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      hintText: '123456',
                      counterText: '',
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: busy ? null : onBack,
                          child: const Text('Back'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: busy ? null : onVerify,
                          child: Text(busy ? 'Verifying...' : 'Login'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Align(
                    alignment: Alignment.center,
                    child: OutlinedButton(
                      onPressed: busy ? null : onResend,
                      child: const Text('Resend Code'),
                    ),
                  ),
                ],
                if (error != null) ...[
                  const SizedBox(height: 12),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFEF2F2),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: const Color(0xFFEF4444)),
                    ),
                    child: Text(
                      error!,
                      style: const TextStyle(color: Color(0xFFEF4444)),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _Dashboard extends StatelessWidget {
  final AdminState state;
  final VoidCallback onRefresh;
  final VoidCallback onCleanup;
  final VoidCallback onLogout;
  final void Function(int) onTab;
  final Future<void> Function(Map, String) onManage;
  const _Dashboard({
    required this.state,
    required this.onRefresh,
    required this.onCleanup,
    required this.onLogout,
    required this.onTab,
    required this.onManage,
  });
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Center(
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 1400),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'ArionComply Admin Dashboard',
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        Wrap(
                          spacing: 8,
                          children: [
                            ElevatedButton(
                              onPressed: state.busy ? null : onRefresh,
                              child: const Text('Refresh'),
                            ),
                            ElevatedButton(
                              onPressed: state.busy ? null : onCleanup,
                              child: const Text('Cleanup Data'),
                            ),
                            OutlinedButton(
                              onPressed: state.busy ? null : onLogout,
                              child: const Text('Logout'),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Container(
                      decoration: const BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: Color(0xFFE2E8F0),
                            width: 2,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          _tab(
                            '👥 User Management',
                            active: state.tabIndex == 0,
                            onTap: () => onTab(0),
                          ),
                          _tab(
                            '📊 Lead Management',
                            active: state.tabIndex == 1,
                            onTap: () => onTab(1),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    if (state.error != null)
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: const Color(0xFFFEF2F2),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: const Color(0xFFEF4444)),
                        ),
                        child: Text(
                          state.error!,
                          style: const TextStyle(color: Color(0xFFEF4444)),
                        ),
                      ),
                    if (state.tabIndex == 0)
                      _UsersTab(state: state, onManage: onManage)
                    else
                      _LeadsTab(state: state),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _tab(
    String label, {
    required bool active,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: active ? const Color(0xFF059669) : Colors.transparent,
              width: 3,
            ),
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: active ? const Color(0xFF059669) : const Color(0xFF475569),
          ),
        ),
      ),
    );
  }
}

class _UsersTab extends StatelessWidget {
  final AdminState state;
  final Future<void> Function(Map, String) onManage;
  const _UsersTab({required this.state, required this.onManage});
  @override
  Widget build(BuildContext context) {
    final users = (state.usersData?['users'] as List?) ?? const [];
    final summary = (state.usersData?['summary'] as Map?) ?? const {};
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        LayoutBuilder(
          builder: (context, c) {
            final w = c.maxWidth;
            final cross = w >= 1200
                ? 4
                : w >= 900
                ? 3
                : w >= 600
                ? 2
                : 1;
            return GridView.count(
              crossAxisCount: cross,
              childAspectRatio: 3,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              mainAxisSpacing: 12,
              crossAxisSpacing: 12,
              children: [
                _stat('Total Users', (summary['total'] ?? '-').toString()),
                _stat('Active Users', (summary['active'] ?? '-').toString()),
                _stat('Pending Users', (summary['pending'] ?? '-').toString()),
                _stat(
                  'Suspended Users',
                  (summary['suspended'] ?? '-').toString(),
                ),
              ],
            );
          },
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: const [BoxShadow(color: Colors.black12, blurRadius: 4)],
          ),
          child: Column(
            children: [
              if (state.busy && users.isEmpty)
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Text('Loading users...'),
                ),
              if (users.isNotEmpty)
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: SizedBox(
                    width: 1200,
                    child: Column(
                      children: [
                        _tableHeader(const [
                          'Name',
                          'Email',
                          'Company',
                          'Job Title',
                          'Status',
                          'Type',
                          'Created',
                          'Actions',
                        ]),
                        const Divider(height: 1),
                        ...users
                            .map(
                              (u) => _tableRow([
                                Text('${u['first_name']} ${u['last_name']}'),
                                Text(u['email'] ?? '-'),
                                Text(u['company_name'] ?? '-'),
                                Text(u['job_title'] ?? '-'),
                                _badge((u['status'] ?? '-').toString()),
                                Text((u['type'] ?? '-').toString()),
                                Text(_fmtDate(u['created_at'])),
                                Wrap(
                                  spacing: 6,
                                  runSpacing: 6,
                                  children: [
                                    if (u['status'] == 'pending' ||
                                        u['status'] == 'suspended')
                                      ElevatedButton(
                                        onPressed: () =>
                                            onManage(u as Map, 'activate'),
                                        child: const Text('Activate'),
                                      ),
                                    if (u['status'] == 'active')
                                      ElevatedButton(
                                        onPressed: () =>
                                            onManage(u as Map, 'suspend'),
                                        child: const Text('Suspend'),
                                      ),
                                    ElevatedButton(
                                      onPressed: () =>
                                          onManage(u as Map, 'delete'),
                                      child: const Text('Delete'),
                                    ),
                                  ],
                                ),
                              ]),
                            )
                            .toList(),
                      ],
                    ),
                  ),
                ),
              if (!state.busy && users.isEmpty)
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: Color(0xFF64748B)),
                      SizedBox(width: 8),
                      Text('No users to display.'),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}

class _LeadsTab extends StatelessWidget {
  final AdminState state;
  const _LeadsTab({required this.state});
  @override
  Widget build(BuildContext context) {
    final leads = (state.leadsData?['leads'] as List?) ?? const [];
    final summary = (state.leadsData?['summary'] as Map?) ?? const {};
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        LayoutBuilder(
          builder: (context, c) {
            final w = c.maxWidth;
            final cross = w >= 1200
                ? 4
                : w >= 900
                ? 3
                : w >= 600
                ? 2
                : 1;
            return GridView.count(
              crossAxisCount: cross,
              childAspectRatio: 3,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              mainAxisSpacing: 12,
              crossAxisSpacing: 12,
              children: [
                _stat('Total Leads', (summary['total'] ?? '-').toString()),
                _stat('SQL Leads', (summary['sql'] ?? '-').toString()),
                _stat('MQL Leads', (summary['mql'] ?? '-').toString()),
                _stat("Today's Leads", (summary['today'] ?? '-').toString()),
              ],
            );
          },
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          children: [
            ElevatedButton(
              onPressed: state.busy ? null : () {},
              child: const Text('Export CSV'),
            ),
            ElevatedButton(
              onPressed: state.busy ? null : () {},
              child: const Text('Export JSON'),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: const [BoxShadow(color: Colors.black12, blurRadius: 4)],
          ),
          child: Column(
            children: [
              if (state.busy && leads.isEmpty)
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Text('Loading leads...'),
                ),
              if (leads.isNotEmpty)
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: SizedBox(
                    width: 1200,
                    child: Column(
                      children: [
                        _tableHeader(const [
                          'Name',
                          'Email',
                          'Company',
                          'Job Title',
                          'Score',
                          'Qualification',
                          'Frameworks',
                          'Company Size',
                          'Created',
                        ]),
                        const Divider(height: 1),
                        ...leads
                            .map(
                              (l) => _tableRow([
                                Text('${l['first_name']} ${l['last_name']}'),
                                Text(l['email'] ?? '-'),
                                Text(l['company_name'] ?? '-'),
                                Text(l['job_title'] ?? '-'),
                                Text((l['lead_score'] ?? '').toString()),
                                _qualBadge(
                                  (l['lead_qualification_status'] ?? '-')
                                      .toString(),
                                ),
                                Text(
                                  (l['compliance_frameworks'] ?? '-')
                                      .toString(),
                                ),
                                Text((l['company_size'] ?? '-').toString()),
                                Text(_fmtDate(l['created_at'])),
                              ]),
                            )
                            .toList(),
                      ],
                    ),
                  ),
                ),
              if (!state.busy && leads.isEmpty)
                const Padding(
                  padding: EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: Color(0xFF64748B)),
                      SizedBox(width: 8),
                      Text('No leads to display.'),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}

Widget _stat(String title, String value) {
  return Container(
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(8),
      boxShadow: const [BoxShadow(color: Colors.black12, blurRadius: 4)],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: Color(0xFF059669),
          ),
        ),
        const SizedBox(height: 4),
        Text(title),
      ],
    ),
  );
}

Widget _tableHeader(List<String> columns) {
  return Container(
    color: const Color(0xFFF8FAFC),
    child: Row(
      children: columns
          .map(
            (c) => Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Text(
                  c,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
              ),
            ),
          )
          .toList(),
    ),
  );
}

Widget _tableRow(List<Widget> cells) {
  return Column(
    children: [
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: cells
            .map(
              (c) => Expanded(
                child: Padding(padding: const EdgeInsets.all(12), child: c),
              ),
            )
            .toList(),
      ),
      const Divider(height: 1),
    ],
  );
}

Widget _badge(String status) {
  Color bg = Colors.grey;
  switch (status) {
    case 'active':
      bg = const Color(0xFF10B981);
      break;
    case 'pending':
      bg = const Color(0xFFF59E0B);
      break;
    case 'suspended':
      bg = const Color(0xFF6B7280);
      break;
    case 'expired':
      bg = const Color(0xFFEF4444);
      break;
  }
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    decoration: BoxDecoration(
      color: bg,
      borderRadius: BorderRadius.circular(4),
    ),
    child: Text(
      status,
      style: const TextStyle(color: Colors.white, fontSize: 12),
    ),
  );
}

String _fmtDate(dynamic value) {
  if (value == null) return '-';
  final s = value.toString();
  try {
    final dt = DateTime.parse(s);
    return '${dt.month}/${dt.day}/${dt.year}';
  } catch (_) {
    return s;
  }
}

Widget _qualBadge(String label) {
  final lower = label.toLowerCase();
  Color bg;
  switch (lower) {
    case 'sql':
      bg = const Color(0xFF10B981);
      break;
    case 'mql':
      bg = const Color(0xFF3B82F6);
      break;
    case 'lead':
      bg = const Color(0xFFF59E0B);
      break;
    default:
      bg = const Color(0xFF6B7280);
  }
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    decoration: BoxDecoration(
      color: bg,
      borderRadius: BorderRadius.circular(4),
    ),
    child: Text(
      lower,
      style: const TextStyle(color: Colors.white, fontSize: 12),
    ),
  );
}
