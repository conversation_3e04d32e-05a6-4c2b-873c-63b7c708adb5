# 🧠 ArionComply Demo System — Complete User Guide

**Version:** v2.0  
**Module:** Demo System (Complete User & Developer Guide)  
**Date:** 2025-08-26  
**Purpose:** Define the complete system architecture, user workflows, technical implementation, and administrative features of the ArionComply AI-powered compliance automation demo platform. Focus is on registration requirements, demo limitations (12/20/200 questions), document generation capabilities (preview-only with watermarks), compliance topic filtering, email verification processes, and integration with Supabase-powered backend and Claude AI services.

---

## 🔑 1. Accessing the Demo

### Landing Page (`index.html`)
- The first page you see is the **ArionComply Demo Landing Page**.
- Here you have two main options:
  - **🚀 Get Started** → "New to ArionComply? Create your free account" - Opens registration modal
  - **🔐 Sign In** → "Already registered? Access the demo platform" - Opens login modal for existing users

### Demo Access Registration
- New users must fill in:
  - **First Name / Last Name**
  - **Work Email Address**
  - **Company Name & Job Title**
  - **Company Size** (1-10, 11-50, 51-200, etc.)
  - **Compliance Interest** (ISO 27001, GDPR, AI Governance, etc.)
  - **Terms & Conditions Agreement**
- After registration, you'll receive a **verification email with temporary login credentials**.

---

## 📧 2. Email Verification Process (`verify.html`)

### What You'll Receive
- **Verification email** with:
  - Click-to-verify link (expires in 24 hours)
  - **Your registered email**
  - **Temporary password** for demo access

### Verification Results
- ✅ **Success** → Your email is verified, redirect to demo with active session.
- ❌ **Failure** → If token is expired/invalid, you'll be prompted to request a new verification email.

---

## 💬 3. Demo Platform (`demo.html`)

The main AI-powered demo experience with strict limitations:

### Demo Limitations (Enforced)
- **Session**: 12 questions per session
- **Daily**: 20 questions per day
- **Monthly**: 200 questions per month
- **Documents**: 5 preview-only documents per session
- **Topics**: Compliance-related only (non-compliance questions blocked)

### AI Chat Assistant
- Ask questions like:
  - "How do I get ISO 27001 certified?"
  - "What GDPR documents do I need?"
  - "Help me with AI governance framework"
  - "Create a risk register for my company"
- The assistant:
  - **Detects your compliance goal** (ISO 27001, GDPR, AI governance, etc.)
  - **Guides you through intake questions** (company size, industry, current status)
  - **Generates preview-only documents** with watermarks and copy protection

### Interface Features
- **Question Counter**: Shows remaining questions (12/20/200 limits)
- **Quick Replies**: Suggested buttons based on conversation stage
- **Typing Indicators**: Shows when AI is processing your request
- **Compliance Filter**: Blocks non-compliance topics with helpful redirections

---

## 📄 4. Document Generation (`generate-docs.js`)

### Available Document Types
The demo can generate **preview-only** versions of:

- 📋 **ISO 27001 Risk Register** - Security risk assessment and treatment
- 📄 **Statement of Applicability (SoA)** - ISO controls implementation status
- 📜 **Information Security Policy** - Organizational security policies
- 🛡️ **GDPR Data Protection Documentation** - Comprehensive GDPR guide
- 🔍 **Record of Processing Activities (ROPA)** - GDPR data processing inventory
- 🔒 **Data Protection Impact Assessment (DPIA)** - Privacy impact assessment
- ⚖️ **AI Risk Assessment** - AI system risk evaluation
- 📊 **AI Ethics Framework** - Responsible AI guidelines

### Document Limitations (Demo Only)
- **Maximum 5 documents** per demo session
- **Preview-only mode** with watermarks
- **Copy protection enabled** - no text selection, downloading, or printing
- **Keyboard shortcuts disabled** (Ctrl+C, Ctrl+A, Ctrl+S blocked)
- **Upgrade required** for full document access

---

## 🔒 5. Admin Dashboard (`admin.html`)

### Admin Access Process
- Navigate directly to `admin.html` or add `?admin=true` to any URL
- **Two-step authentication required:**
  1. **Admin Password** - Enter the system admin password
  2. **MFA Code** - 6-digit code sent to `<EMAIL>`
- Session token stored for 24-hour access

### Admin Dashboard Features

#### User Management Tab
- **User Statistics**: Total users, verified users, pending verifications
- **User List**: Name, email, company, verification status, registration date
- **User Actions**: View details, manage verification status, export data
- **Search & Filter**: Find users by name, email, company, or status

#### Lead Management Tab  
- **Lead Statistics**: Total leads, SQL/MQL/Lead/Suspect counts, conversion rates
- **Lead Qualification**: Automatic scoring based on company size, compliance interest
- **Lead List**: Contact details, qualification status, compliance frameworks, company info
- **Export Functions**: CSV/JSON export for CRM integration

#### Admin Controls
- **Data Cleanup**: Remove expired verifications and old sessions (30+ days)
- **Session Management**: View active sessions and user activity
- **System Monitoring**: API performance, error tracking, usage statistics
- **Refresh Data**: Real-time updates of user and lead information

### Admin Limitations
- **Single Admin Email**: All codes sent to `<EMAIL>`
- **Session Timeout**: 24-hour admin session expiration
- **Password Protection**: Single admin password for system access

---

## ✅ 6. Email Verification (`verify.html`)

### Verification Process
- Users receive email with verification link: `verify.html?token=<verification-token>`
- **24-hour expiration** on all verification tokens
- **Three possible states:**

#### Loading State
- Displays while verification API call processes
- Shows spinning loader and "Verifying Your Email" message
- Automatic token extraction from URL parameters

#### Success State ✅
- **Email verified successfully** - User account activated
- **Session created** - Unique session ID generated for demo access
- **User information displayed** - Name, email, company details
- **Demo access button** - Direct link to `demo.html?session=<session-id>`
- **Next steps guidance** - What user can expect in demo

#### Error State ❌
- **Detailed error messages** based on failure type:
  - **EXPIRED_TOKEN**: Link expired (24-hour limit)
  - **INVALID_TOKEN**: Link malformed or already used
  - **NETWORK_ERROR**: Connection issues
  - **MISSING_TOKEN**: No token in URL
- **Recovery options**: Retry verification, request new link, contact support
- **Support contact** - Direct email link to support team

### Verification Token Management
- **Unique tokens** generated with `crypto.randomUUID()`
- **Database storage** in `demo_verification_requests` table
- **Automatic cleanup** of expired tokens via admin dashboard
- **Single-use tokens** - Cannot be reused after successful verification

---

## ⚙️ 7. System Architecture

### Core Files & Functions

#### Frontend Files
- **`demo.html`** - Main chat interface with AI assistant
- **`index.html`** - Landing page with registration forms
- **`admin.html`** - Admin dashboard with user/lead management
- **`verify.html`** - Email verification success/failure page
- **`config.js`** - System configuration (API endpoints, limits: 12/20/200)
- **`utils.js`** - Core utilities (API calls, session management, formatting)
- **`cache.js`** - Intelligent caching (30min chat, 24hr documents)

#### Core System Components
- **`compliance-filter.js`** - Topic validation (blocks non-compliance questions)
- **`generate-docs.js`** - Document creation with demo limitations
- **`demo-enhancements.js`** - UI improvements and personalization
- **`feedback-enhancements.js`** - User feedback collection system

#### Backend Integration
- **`index.ts`** - Supabase Edge Function handling all API requests
  - Email verification with temporary passwords
  - Claude AI integration for chat responses
  - Session management and user tracking
  - Admin authentication with MFA codes

---

## 🛠️ 8. Technical Implementation

### API Architecture
- **Single Endpoint**: `/quick-processor` routes all requests
- **Authentication**: Supabase + Bearer token system
- **AI Integration**: Claude API (`claude-3-5-sonnet`) for conversations
- **Database**: Supabase for user management and session tracking

### Key Configuration
```javascript
// Demo Limits (Strictly Enforced)
sessionLimit: 12    // Questions per session
dailyLimit: 20      // Questions per day  
monthlyLimit: 200   // Questions per month
documentLimit: 5    // Documents per session

// Document Restrictions
previewOnly: true        // No downloads in demo
watermarkEnabled: true   // Copy protection
downloadEnabled: false   // Disabled in demo
```

### Compliance Filter Logic
- **Allowed Topics**: ISO standards, GDPR, AI governance, cybersecurity, audit preparation
- **Blocked Topics**: Personal advice, entertainment, non-compliance technical questions
- **Context Awareness**: Allows follow-up questions within compliance conversations

---

## 🐛 9. Troubleshooting Guide

### Registration Issues
- **Email not received** → Check spam folder, verify email format
- **Verification expired** → Request new verification (24-hour window)
- **Login failing** → Ensure email verification completed first

### Demo Limitations
- **Questions not working** → Check if 12/20/200 limits reached
- **Documents not generating** → Verify under 5-document limit
- **Admin mode failing** → Confirm `?admin=true` and email verification

### Debug Console Commands
```javascript
window.resetLimits()         // Reset all demo limits
window.testDocumentView()    // Test document gallery
window.debugInfo()           // Show current session state
ArionCache.getStats()        // Cache performance metrics
```

---

## 📊 10. Typical User Journey

1. **Visit Landing Page** → Register with work email and company details
2. **Check Email** → Click verification link and note temporary password
3. **Login to Demo** → Use verified email and temporary password
4. **Start Conversation** → Ask compliance questions (12 per session limit)
5. **Goal Detection** → AI identifies your compliance objective
6. **Intake Process** → Answer 3-5 assessment questions about your organization
7. **Document Generation** → Generate up to 5 preview-only documents
8. **Document Review** → View watermarked documents with copy protection
9. **Lead Capture** → Provide contact info for consultation (optional)
10. **Upgrade Path** → Request full access for unlimited features

---

## ⚠️ 11. Demo Limitations Summary

### Question Limits (Strictly Enforced)
- **12 questions per session** - Reset when starting new session
- **20 questions per day** - Reset at midnight
- **200 questions per month** - Reset monthly
- **Admin bypass** - Unlimited with `?admin=true` verification

### Document Restrictions
- **5 documents maximum** per demo session
- **Preview-only display** with watermarks
- **Copy protection** prevents text selection/downloading
- **No export functionality** - upgrade required for full access

### Topic Filtering
- **Compliance topics only** - ISO 27001, GDPR, AI governance, cybersecurity
- **Non-compliance blocked** - Personal advice, entertainment, general tech support
- **Helpful redirections** - Suggests appropriate compliance questions

### Session Management
- **Email verification required** - No anonymous access
- **Temporary passwords** - Provided via verification email
- **Session persistence** - Tracks limits across browser sessions
- **24-hour verification window** - Links expire after 24 hours

---

✅ You are now ready to use the **ArionComply demo platform** with full understanding of its capabilities and limitations!
