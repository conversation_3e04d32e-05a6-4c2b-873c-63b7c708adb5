Database inspection report
Generated: <PERSON><PERSON> Aug 26 12:31:40 CEST 2025
==================================

[Extensions]

                                            List of installed extensions
        Name        | Version |   Schema   |                              Description                               
--------------------+---------+------------+------------------------------------------------------------------------
 pg_graphql         | 1.5.11  | graphql    | pg_graphql: GraphQL support
 pg_stat_statements | 1.11    | extensions | track planning and execution statistics of all SQL statements executed
 pgcrypto           | 1.3     | extensions | cryptographic functions
 plpgsql            | 1.0     | pg_catalog | PL/pgSQL procedural language
 supabase_vault     | 0.3.1   | vault      | Supabase Vault Extension
 uuid-ossp          | 1.1     | extensions | generate universally unique identifiers (UUIDs)
 vector             | 0.8.0   | public     | vector data type and ivfflat and hnsw access methods
(7 rows)


[Table sizes & row estimates]

 schema  |           table            | bytes  |  size  | est_rows 
---------+----------------------------+--------+--------+----------
 public  | knowledge_documents        | 950272 | 928 kB |       -1
 public  | demo_interactions          | 581632 | 568 kB |      514
 public  | product_registrations      | 221184 | 216 kB |       -1
 public  | demo_users                 | 196608 | 192 kB |       -1
 auth    | users                      | 163840 | 160 kB |        4
 public  | demo_sessions              | 163840 | 160 kB |       71
 public  | demo_contacts              | 147456 | 144 kB |       -1
 auth    | refresh_tokens             | 131072 | 128 kB |       -1
 public  | demo_verification_requests | 114688 | 112 kB |       -1
 public  | contextual_prompts         | 114688 | 112 kB |        6
 auth    | one_time_tokens            | 114688 | 112 kB |       -1
 public  | admin_verification_codes   |  81920 | 80 kB  |       25
 auth    | sessions                   |  81920 | 80 kB  |       -1
 auth    | identities                 |  81920 | 80 kB  |       -1
 public  | response_templates         |  65536 | 64 kB  |       -1
 storage | objects                    |  65536 | 64 kB  |       -1
 public  | demo_documents             |  65536 | 64 kB  |       -1
 public  | knowledge_base             |  65536 | 64 kB  |       -1
 public  | faq_items                  |  65536 | 64 kB  |       -1
 public  | admin_sessions             |  57344 | 56 kB  |       -1
 auth    | mfa_factors                |  57344 | 56 kB  |       -1
 auth    | audit_log_entries          |  49152 | 48 kB  |       -1
 auth    | mfa_amr_claims             |  49152 | 48 kB  |       -1
 public  | prompt_usage_logs          |  40960 | 40 kB  |       -1
 public  | event_logs                 |  40960 | 40 kB  |       -1
 storage | migrations                 |  40960 | 40 kB  |       -1
 auth    | saml_relay_states          |  40960 | 40 kB  |       -1
 auth    | flow_state                 |  40960 | 40 kB  |       -1
 public  | goal_analytics             |  32768 | 32 kB  |       -1
 auth    | sso_domains                |  32768 | 32 kB  |       -1
 public  | user_custom_prompts        |  32768 | 32 kB  |       -1
 public  | prompt_performance_metrics |  32768 | 32 kB  |       -1
 public  | demo_embeddings            |  32768 | 32 kB  |       -1
 auth    | saml_providers             |  32768 | 32 kB  |       -1
 public  | user_queries_log           |  32768 | 32 kB  |       -1
 storage | buckets                    |  24576 | 24 kB  |       -1
 auth    | mfa_challenges             |  24576 | 24 kB  |       -1
 storage | s3_multipart_uploads       |  24576 | 24 kB  |       -1
 auth    | schema_migrations          |  24576 | 24 kB  |       61
 storage | prefixes                   |  24576 | 24 kB  |       -1
 auth    | sso_providers              |  24576 | 24 kB  |       -1
 storage | s3_multipart_uploads_parts |  16384 | 16 kB  |       -1
 public  | persistent_admin_sessions  |  16384 | 16 kB  |       -1
 public  | demo_intake_data           |  16384 | 16 kB  |       -1
 auth    | instances                  |  16384 | 16 kB  |       -1
 storage | buckets_analytics          |  16384 | 16 kB  |       -1
 public  | demo_browsing_logs         |  16384 | 16 kB  |       -1
(47 rows)


[RLS flags]

 schema  |           table            | rowsecurity | force_rowsecurity 
---------+----------------------------+-------------+-------------------
 auth    | audit_log_entries          | t           | f
 auth    | flow_state                 | t           | f
 auth    | identities                 | t           | f
 auth    | instances                  | t           | f
 auth    | mfa_amr_claims             | t           | f
 auth    | mfa_challenges             | t           | f
 auth    | mfa_factors                | t           | f
 auth    | one_time_tokens            | t           | f
 auth    | refresh_tokens             | t           | f
 auth    | saml_providers             | t           | f
 auth    | saml_relay_states          | t           | f
 auth    | schema_migrations          | t           | f
 auth    | sessions                   | t           | f
 auth    | sso_domains                | t           | f
 auth    | sso_providers              | t           | f
 auth    | users                      | t           | f
 public  | admin_sessions             | f           | f
 public  | admin_verification_codes   | t           | f
 public  | contextual_prompts         | t           | f
 public  | demo_browsing_logs         | t           | f
 public  | demo_contacts              | t           | f
 public  | demo_documents             | t           | f
 public  | demo_embeddings            | t           | f
 public  | demo_intake_data           | t           | f
 public  | demo_interactions          | t           | f
 public  | demo_sessions              | t           | f
 public  | demo_users                 | t           | f
 public  | demo_verification_requests | t           | f
 public  | event_logs                 | t           | f
 public  | faq_items                  | t           | f
 public  | goal_analytics             | t           | f
 public  | knowledge_base             | t           | f
 public  | knowledge_documents        | t           | f
 public  | persistent_admin_sessions  | f           | f
 public  | product_registrations      | t           | f
 public  | prompt_performance_metrics | t           | f
 public  | prompt_usage_logs          | t           | f
 public  | response_templates         | t           | f
 public  | user_custom_prompts        | t           | f
 public  | user_queries_log           | t           | f
 storage | buckets                    | t           | f
 storage | buckets_analytics          | t           | f
 storage | migrations                 | t           | f
 storage | objects                    | t           | f
 storage | prefixes                   | t           | f
 storage | s3_multipart_uploads       | t           | f
 storage | s3_multipart_uploads_parts | t           | f
(47 rows)


[RLS policies]

 schemaname |         tablename          |                    policyname                    | permissive |  roles   |  cmd   |                                                            qual                                                             |              with_check              
------------+----------------------------+--------------------------------------------------+------------+----------+--------+-----------------------------------------------------------------------------------------------------------------------------+--------------------------------------
 public     | admin_verification_codes   | Allow all admin code operations                  | PERMISSIVE | {public} | ALL    | true                                                                                                                        | 
 public     | contextual_prompts         | Authenticated users can read active prompts      | PERMISSIVE | {public} | SELECT | ((auth.role() = 'authenticated'::text) AND (is_active = true))                                                              | 
 public     | contextual_prompts         | Service role can manage prompts                  | PERMISSIVE | {public} | ALL    | (auth.role() = 'service_role'::text)                                                                                        | 
 public     | demo_browsing_logs         | demo_policy                                      | PERMISSIVE | {public} | ALL    | true                                                                                                                        | 
 public     | demo_contacts              | Allow all demo_contacts operations               | PERMISSIVE | {public} | ALL    | true                                                                                                                        | 
 public     | demo_documents             | Allow all demo_documents operations              | PERMISSIVE | {public} | ALL    | true                                                                                                                        | 
 public     | demo_embeddings            | demo_embeddings_service_role                     | PERMISSIVE | {public} | ALL    | ((auth.jwt() ->> 'role'::text) = 'service_role'::text)                                                                      | 
 public     | demo_intake_data           | demo_policy                                      | PERMISSIVE | {public} | ALL    | true                                                                                                                        | 
 public     | demo_interactions          | Allow all demo_interactions operations           | PERMISSIVE | {public} | ALL    | true                                                                                                                        | 
 public     | demo_sessions              | Admins can access all sessions                   | PERMISSIVE | {public} | ALL    | ((user_type = 'admin'::text) OR (user_email = ANY (ARRAY['<EMAIL>'::text, '<EMAIL>'::text]))) | 
 public     | demo_sessions              | Allow all demo_sessions operations               | PERMISSIVE | {public} | ALL    | true                                                                                                                        | 
 public     | demo_users                 | Admins can access all data                       | PERMISSIVE | {public} | ALL    | ((user_type = 'admin'::text) OR (email = ANY (ARRAY['<EMAIL>'::text, '<EMAIL>'::text])))      | 
 public     | demo_users                 | Allow all demo_users operations                  | PERMISSIVE | {public} | ALL    | true                                                                                                                        | 
 public     | demo_verification_requests | Allow verification operations                    | PERMISSIVE | {public} | ALL    | true                                                                                                                        | 
 public     | event_logs                 | Service role can manage event logs               | PERMISSIVE | {public} | ALL    | (auth.role() = 'service_role'::text)                                                                                        | 
 public     | faq_items                  | Allow read faq_items                             | PERMISSIVE | {public} | SELECT | (status = 'published'::text)                                                                                                | 
 public     | goal_analytics             | Authenticated users can read analytics           | PERMISSIVE | {public} | SELECT | (auth.role() = 'authenticated'::text)                                                                                       | 
 public     | goal_analytics             | Service role can manage analytics                | PERMISSIVE | {public} | ALL    | (auth.role() = 'service_role'::text)                                                                                        | 
 public     | knowledge_base             | Allow read knowledge_base                        | PERMISSIVE | {public} | SELECT | (status = 'published'::text)                                                                                                | 
 public     | knowledge_documents        | Authenticated users can read knowledge docs      | PERMISSIVE | {public} | SELECT | ((auth.role() = 'authenticated'::text) AND (is_active = true))                                                              | 
 public     | knowledge_documents        | Service role can manage knowledge docs           | PERMISSIVE | {public} | ALL    | (auth.role() = 'service_role'::text)                                                                                        | 
 public     | product_registrations      | admin_dashboard_read_access                      | PERMISSIVE | {public} | SELECT | true                                                                                                                        | 
 public     | product_registrations      | admin_read_policy                                | PERMISSIVE | {public} | SELECT | true                                                                                                                        | 
 public     | product_registrations      | block_anonymous_access                           | PERMISSIVE | {public} | ALL    | (auth.role() <> 'anon'::text)                                                                                               | (auth.role() <> 'anon'::text)
 public     | product_registrations      | service_role_full_access                         | PERMISSIVE | {public} | ALL    | (auth.role() = 'service_role'::text)                                                                                        | (auth.role() = 'service_role'::text)
 public     | product_registrations      | users_update_own_registrations                   | PERMISSIVE | {public} | UPDATE | (auth.uid() = user_id)                                                                                                      | (auth.uid() = user_id)
 public     | product_registrations      | users_view_own_registrations                     | PERMISSIVE | {public} | SELECT | (auth.uid() = user_id)                                                                                                      | 
 public     | prompt_performance_metrics | Authenticated users can read performance metrics | PERMISSIVE | {public} | SELECT | (auth.role() = 'authenticated'::text)                                                                                       | 
 public     | prompt_performance_metrics | Service role can manage performance metrics      | PERMISSIVE | {public} | ALL    | (auth.role() = 'service_role'::text)                                                                                        | 
 public     | prompt_usage_logs          | Service role can manage usage logs               | PERMISSIVE | {public} | ALL    | (auth.role() = 'service_role'::text)                                                                                        | 
 public     | response_templates         | Allow read response_templates                    | PERMISSIVE | {public} | SELECT | (status = 'active'::text)                                                                                                   | 
 public     | user_custom_prompts        | Service role can read all custom prompts         | PERMISSIVE | {public} | SELECT | (auth.role() = 'service_role'::text)                                                                                        | 
 public     | user_custom_prompts        | Users can manage their own custom prompts        | PERMISSIVE | {public} | ALL    | (auth.uid() = user_id)                                                                                                      | 
 public     | user_queries_log           | Allow all user_queries_log operations            | PERMISSIVE | {public} | ALL    | true                                                                                                                        | 
(34 rows)


[Triggers]

 event_object_schema |     event_object_table     |                 trigger_name                 | action_timing | event_manipulation 
---------------------+----------------------------+----------------------------------------------+---------------+--------------------
 auth                | users                      | link_registration_trigger                    | AFTER         | INSERT
 auth                | users                      | sync_email_verification_trigger              | AFTER         | UPDATE
 public              | demo_documents             | update_demo_documents_updated_at             | BEFORE        | UPDATE
 public              | demo_sessions              | update_demo_sessions_updated_at              | BEFORE        | UPDATE
 public              | demo_users                 | update_demo_users_updated_at                 | BEFORE        | UPDATE
 public              | demo_verification_requests | update_demo_verification_requests_updated_at | BEFORE        | UPDATE
 public              | faq_items                  | update_faq_items_updated_at                  | BEFORE        | UPDATE
 public              | knowledge_base             | update_knowledge_base_updated_at             | BEFORE        | UPDATE
 public              | product_registrations      | normalize_registration_data_trigger          | BEFORE        | INSERT
 public              | product_registrations      | normalize_registration_data_trigger          | BEFORE        | UPDATE
 public              | product_registrations      | update_product_registrations_updated_at      | BEFORE        | UPDATE
 public              | response_templates         | update_response_templates_updated_at         | BEFORE        | UPDATE
 realtime            | subscription               | tr_check_filters                             | BEFORE        | UPDATE
 realtime            | subscription               | tr_check_filters                             | BEFORE        | INSERT
 storage             | buckets                    | enforce_bucket_name_length_trigger           | BEFORE        | UPDATE
 storage             | buckets                    | enforce_bucket_name_length_trigger           | BEFORE        | INSERT
 storage             | objects                    | objects_delete_delete_prefix                 | AFTER         | DELETE
 storage             | objects                    | objects_insert_create_prefix                 | BEFORE        | INSERT
 storage             | objects                    | objects_update_create_prefix                 | BEFORE        | UPDATE
 storage             | objects                    | update_objects_updated_at                    | BEFORE        | UPDATE
 storage             | prefixes                   | prefixes_create_hierarchy                    | BEFORE        | INSERT
 storage             | prefixes                   | prefixes_delete_hierarchy                    | AFTER         | DELETE
(22 rows)


[Indexes]

 schemaname |         tablename          |                        indexname                         |                                                                                                                              indexdef                                                                                                                              
------------+----------------------------+----------------------------------------------------------+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 auth       | audit_log_entries          | audit_log_entries_pkey                                   | CREATE UNIQUE INDEX audit_log_entries_pkey ON auth.audit_log_entries USING btree (id)
 auth       | audit_log_entries          | audit_logs_instance_id_idx                               | CREATE INDEX audit_logs_instance_id_idx ON auth.audit_log_entries USING btree (instance_id)
 auth       | flow_state                 | flow_state_created_at_idx                                | CREATE INDEX flow_state_created_at_idx ON auth.flow_state USING btree (created_at DESC)
 auth       | flow_state                 | flow_state_pkey                                          | CREATE UNIQUE INDEX flow_state_pkey ON auth.flow_state USING btree (id)
 auth       | flow_state                 | idx_auth_code                                            | CREATE INDEX idx_auth_code ON auth.flow_state USING btree (auth_code)
 auth       | flow_state                 | idx_user_id_auth_method                                  | CREATE INDEX idx_user_id_auth_method ON auth.flow_state USING btree (user_id, authentication_method)
 auth       | identities                 | identities_email_idx                                     | CREATE INDEX identities_email_idx ON auth.identities USING btree (email text_pattern_ops)
 auth       | identities                 | identities_pkey                                          | CREATE UNIQUE INDEX identities_pkey ON auth.identities USING btree (id)
 auth       | identities                 | identities_provider_id_provider_unique                   | CREATE UNIQUE INDEX identities_provider_id_provider_unique ON auth.identities USING btree (provider_id, provider)
 auth       | identities                 | identities_user_id_idx                                   | CREATE INDEX identities_user_id_idx ON auth.identities USING btree (user_id)
 auth       | instances                  | instances_pkey                                           | CREATE UNIQUE INDEX instances_pkey ON auth.instances USING btree (id)
 auth       | mfa_amr_claims             | amr_id_pk                                                | CREATE UNIQUE INDEX amr_id_pk ON auth.mfa_amr_claims USING btree (id)
 auth       | mfa_amr_claims             | mfa_amr_claims_session_id_authentication_method_pkey     | CREATE UNIQUE INDEX mfa_amr_claims_session_id_authentication_method_pkey ON auth.mfa_amr_claims USING btree (session_id, authentication_method)
 auth       | mfa_challenges             | mfa_challenge_created_at_idx                             | CREATE INDEX mfa_challenge_created_at_idx ON auth.mfa_challenges USING btree (created_at DESC)
 auth       | mfa_challenges             | mfa_challenges_pkey                                      | CREATE UNIQUE INDEX mfa_challenges_pkey ON auth.mfa_challenges USING btree (id)
 auth       | mfa_factors                | factor_id_created_at_idx                                 | CREATE INDEX factor_id_created_at_idx ON auth.mfa_factors USING btree (user_id, created_at)
 auth       | mfa_factors                | mfa_factors_last_challenged_at_key                       | CREATE UNIQUE INDEX mfa_factors_last_challenged_at_key ON auth.mfa_factors USING btree (last_challenged_at)
 auth       | mfa_factors                | mfa_factors_pkey                                         | CREATE UNIQUE INDEX mfa_factors_pkey ON auth.mfa_factors USING btree (id)
 auth       | mfa_factors                | mfa_factors_user_friendly_name_unique                    | CREATE UNIQUE INDEX mfa_factors_user_friendly_name_unique ON auth.mfa_factors USING btree (friendly_name, user_id) WHERE (TRIM(BOTH FROM friendly_name) <> ''::text)
 auth       | mfa_factors                | mfa_factors_user_id_idx                                  | CREATE INDEX mfa_factors_user_id_idx ON auth.mfa_factors USING btree (user_id)
 auth       | mfa_factors                | unique_phone_factor_per_user                             | CREATE UNIQUE INDEX unique_phone_factor_per_user ON auth.mfa_factors USING btree (user_id, phone)
 auth       | one_time_tokens            | one_time_tokens_pkey                                     | CREATE UNIQUE INDEX one_time_tokens_pkey ON auth.one_time_tokens USING btree (id)
 auth       | one_time_tokens            | one_time_tokens_relates_to_hash_idx                      | CREATE INDEX one_time_tokens_relates_to_hash_idx ON auth.one_time_tokens USING hash (relates_to)
 auth       | one_time_tokens            | one_time_tokens_token_hash_hash_idx                      | CREATE INDEX one_time_tokens_token_hash_hash_idx ON auth.one_time_tokens USING hash (token_hash)
 auth       | one_time_tokens            | one_time_tokens_user_id_token_type_key                   | CREATE UNIQUE INDEX one_time_tokens_user_id_token_type_key ON auth.one_time_tokens USING btree (user_id, token_type)
 auth       | refresh_tokens             | refresh_tokens_instance_id_idx                           | CREATE INDEX refresh_tokens_instance_id_idx ON auth.refresh_tokens USING btree (instance_id)
 auth       | refresh_tokens             | refresh_tokens_instance_id_user_id_idx                   | CREATE INDEX refresh_tokens_instance_id_user_id_idx ON auth.refresh_tokens USING btree (instance_id, user_id)
 auth       | refresh_tokens             | refresh_tokens_parent_idx                                | CREATE INDEX refresh_tokens_parent_idx ON auth.refresh_tokens USING btree (parent)
 auth       | refresh_tokens             | refresh_tokens_pkey                                      | CREATE UNIQUE INDEX refresh_tokens_pkey ON auth.refresh_tokens USING btree (id)
 auth       | refresh_tokens             | refresh_tokens_session_id_revoked_idx                    | CREATE INDEX refresh_tokens_session_id_revoked_idx ON auth.refresh_tokens USING btree (session_id, revoked)
 auth       | refresh_tokens             | refresh_tokens_token_unique                              | CREATE UNIQUE INDEX refresh_tokens_token_unique ON auth.refresh_tokens USING btree (token)
 auth       | refresh_tokens             | refresh_tokens_updated_at_idx                            | CREATE INDEX refresh_tokens_updated_at_idx ON auth.refresh_tokens USING btree (updated_at DESC)
 auth       | saml_providers             | saml_providers_entity_id_key                             | CREATE UNIQUE INDEX saml_providers_entity_id_key ON auth.saml_providers USING btree (entity_id)
 auth       | saml_providers             | saml_providers_pkey                                      | CREATE UNIQUE INDEX saml_providers_pkey ON auth.saml_providers USING btree (id)
 auth       | saml_providers             | saml_providers_sso_provider_id_idx                       | CREATE INDEX saml_providers_sso_provider_id_idx ON auth.saml_providers USING btree (sso_provider_id)
 auth       | saml_relay_states          | saml_relay_states_created_at_idx                         | CREATE INDEX saml_relay_states_created_at_idx ON auth.saml_relay_states USING btree (created_at DESC)
 auth       | saml_relay_states          | saml_relay_states_for_email_idx                          | CREATE INDEX saml_relay_states_for_email_idx ON auth.saml_relay_states USING btree (for_email)
 auth       | saml_relay_states          | saml_relay_states_pkey                                   | CREATE UNIQUE INDEX saml_relay_states_pkey ON auth.saml_relay_states USING btree (id)
 auth       | saml_relay_states          | saml_relay_states_sso_provider_id_idx                    | CREATE INDEX saml_relay_states_sso_provider_id_idx ON auth.saml_relay_states USING btree (sso_provider_id)
 auth       | schema_migrations          | schema_migrations_pkey                                   | CREATE UNIQUE INDEX schema_migrations_pkey ON auth.schema_migrations USING btree (version)
 auth       | sessions                   | sessions_not_after_idx                                   | CREATE INDEX sessions_not_after_idx ON auth.sessions USING btree (not_after DESC)
 auth       | sessions                   | sessions_pkey                                            | CREATE UNIQUE INDEX sessions_pkey ON auth.sessions USING btree (id)
 auth       | sessions                   | sessions_user_id_idx                                     | CREATE INDEX sessions_user_id_idx ON auth.sessions USING btree (user_id)
 auth       | sessions                   | user_id_created_at_idx                                   | CREATE INDEX user_id_created_at_idx ON auth.sessions USING btree (user_id, created_at)
 auth       | sso_domains                | sso_domains_domain_idx                                   | CREATE UNIQUE INDEX sso_domains_domain_idx ON auth.sso_domains USING btree (lower(domain))
 auth       | sso_domains                | sso_domains_pkey                                         | CREATE UNIQUE INDEX sso_domains_pkey ON auth.sso_domains USING btree (id)
 auth       | sso_domains                | sso_domains_sso_provider_id_idx                          | CREATE INDEX sso_domains_sso_provider_id_idx ON auth.sso_domains USING btree (sso_provider_id)
 auth       | sso_providers              | sso_providers_pkey                                       | CREATE UNIQUE INDEX sso_providers_pkey ON auth.sso_providers USING btree (id)
 auth       | sso_providers              | sso_providers_resource_id_idx                            | CREATE UNIQUE INDEX sso_providers_resource_id_idx ON auth.sso_providers USING btree (lower(resource_id))
 auth       | users                      | confirmation_token_idx                                   | CREATE UNIQUE INDEX confirmation_token_idx ON auth.users USING btree (confirmation_token) WHERE ((confirmation_token)::text !~ '^[0-9 ]*$'::text)
 auth       | users                      | email_change_token_current_idx                           | CREATE UNIQUE INDEX email_change_token_current_idx ON auth.users USING btree (email_change_token_current) WHERE ((email_change_token_current)::text !~ '^[0-9 ]*$'::text)
 auth       | users                      | email_change_token_new_idx                               | CREATE UNIQUE INDEX email_change_token_new_idx ON auth.users USING btree (email_change_token_new) WHERE ((email_change_token_new)::text !~ '^[0-9 ]*$'::text)
 auth       | users                      | reauthentication_token_idx                               | CREATE UNIQUE INDEX reauthentication_token_idx ON auth.users USING btree (reauthentication_token) WHERE ((reauthentication_token)::text !~ '^[0-9 ]*$'::text)
 auth       | users                      | recovery_token_idx                                       | CREATE UNIQUE INDEX recovery_token_idx ON auth.users USING btree (recovery_token) WHERE ((recovery_token)::text !~ '^[0-9 ]*$'::text)
 auth       | users                      | users_email_partial_key                                  | CREATE UNIQUE INDEX users_email_partial_key ON auth.users USING btree (email) WHERE (is_sso_user = false)
 auth       | users                      | users_instance_id_email_idx                              | CREATE INDEX users_instance_id_email_idx ON auth.users USING btree (instance_id, lower((email)::text))
 auth       | users                      | users_instance_id_idx                                    | CREATE INDEX users_instance_id_idx ON auth.users USING btree (instance_id)
 auth       | users                      | users_is_anonymous_idx                                   | CREATE INDEX users_is_anonymous_idx ON auth.users USING btree (is_anonymous)
 auth       | users                      | users_phone_key                                          | CREATE UNIQUE INDEX users_phone_key ON auth.users USING btree (phone)
 auth       | users                      | users_pkey                                               | CREATE UNIQUE INDEX users_pkey ON auth.users USING btree (id)
 public     | admin_sessions             | admin_sessions_pkey                                      | CREATE UNIQUE INDEX admin_sessions_pkey ON public.admin_sessions USING btree (id)
 public     | admin_sessions             | admin_sessions_token_key                                 | CREATE UNIQUE INDEX admin_sessions_token_key ON public.admin_sessions USING btree (token)
 public     | admin_sessions             | idx_admin_sessions_token                                 | CREATE INDEX idx_admin_sessions_token ON public.admin_sessions USING btree (token)
 public     | admin_verification_codes   | admin_verification_codes_pkey                            | CREATE UNIQUE INDEX admin_verification_codes_pkey ON public.admin_verification_codes USING btree (id)
 public     | admin_verification_codes   | idx_admin_codes_code_unused                              | CREATE INDEX idx_admin_codes_code_unused ON public.admin_verification_codes USING btree (code) WHERE (is_used = false)
 public     | admin_verification_codes   | idx_admin_codes_expires                                  | CREATE INDEX idx_admin_codes_expires ON public.admin_verification_codes USING btree (expires_at)
 public     | admin_verification_codes   | idx_admin_codes_lookup                                   | CREATE INDEX idx_admin_codes_lookup ON public.admin_verification_codes USING btree (code, is_used, expires_at)
 public     | contextual_prompts         | contextual_prompts_pkey                                  | CREATE UNIQUE INDEX contextual_prompts_pkey ON public.contextual_prompts USING btree (id)
 public     | contextual_prompts         | idx_contextual_prompts_active                            | CREATE INDEX idx_contextual_prompts_active ON public.contextual_prompts USING btree (is_active)
 public     | contextual_prompts         | idx_contextual_prompts_tone                              | CREATE INDEX idx_contextual_prompts_tone ON public.contextual_prompts USING btree (tone)
 public     | contextual_prompts         | idx_contextual_prompts_usage                             | CREATE INDEX idx_contextual_prompts_usage ON public.contextual_prompts USING btree (usage_count DESC)
 public     | demo_browsing_logs         | demo_browsing_logs_pkey                                  | CREATE UNIQUE INDEX demo_browsing_logs_pkey ON public.demo_browsing_logs USING btree (id)
 public     | demo_contacts              | demo_contacts_pkey                                       | CREATE UNIQUE INDEX demo_contacts_pkey ON public.demo_contacts USING btree (id)
 public     | demo_contacts              | idx_demo_contacts_created                                | CREATE INDEX idx_demo_contacts_created ON public.demo_contacts USING btree (created_at)
 public     | demo_contacts              | idx_demo_contacts_email                                  | CREATE INDEX idx_demo_contacts_email ON public.demo_contacts USING btree (email)
 public     | demo_contacts              | idx_demo_contacts_qualification                          | CREATE INDEX idx_demo_contacts_qualification ON public.demo_contacts USING btree (lead_qualification)
 public     | demo_contacts              | idx_demo_contacts_score                                  | CREATE INDEX idx_demo_contacts_score ON public.demo_contacts USING btree (lead_score DESC)
 public     | demo_contacts              | idx_demo_contacts_type                                   | CREATE INDEX idx_demo_contacts_type ON public.demo_contacts USING btree (application_type)
 public     | demo_documents             | demo_documents_pkey                                      | CREATE UNIQUE INDEX demo_documents_pkey ON public.demo_documents USING btree (id)
 public     | demo_documents             | idx_demo_documents_session                               | CREATE INDEX idx_demo_documents_session ON public.demo_documents USING btree (session_id, created_at)
 public     | demo_documents             | idx_demo_documents_type                                  | CREATE INDEX idx_demo_documents_type ON public.demo_documents USING btree (doc_type)
 public     | demo_embeddings            | demo_embeddings_pkey                                     | CREATE UNIQUE INDEX demo_embeddings_pkey ON public.demo_embeddings USING btree (id)
 public     | demo_embeddings            | idx_demo_embeddings_created_at                           | CREATE INDEX idx_demo_embeddings_created_at ON public.demo_embeddings USING btree (created_at)
 public     | demo_embeddings            | idx_demo_embeddings_session_id                           | CREATE INDEX idx_demo_embeddings_session_id ON public.demo_embeddings USING btree (session_id)
 public     | demo_intake_data           | demo_intake_data_pkey                                    | CREATE UNIQUE INDEX demo_intake_data_pkey ON public.demo_intake_data USING btree (id)
 public     | demo_interactions          | demo_interactions_pkey                                   | CREATE UNIQUE INDEX demo_interactions_pkey ON public.demo_interactions USING btree (id)
 public     | demo_interactions          | idx_demo_interactions_event                              | CREATE INDEX idx_demo_interactions_event ON public.demo_interactions USING btree (event_type, created_at)
 public     | demo_interactions          | idx_demo_interactions_session                            | CREATE INDEX idx_demo_interactions_session ON public.demo_interactions USING btree (session_id, created_at)
 public     | demo_sessions              | demo_sessions_pkey                                       | CREATE UNIQUE INDEX demo_sessions_pkey ON public.demo_sessions USING btree (id)
 public     | demo_sessions              | idx_demo_sessions_email                                  | CREATE INDEX idx_demo_sessions_email ON public.demo_sessions USING btree (user_email)
 public     | demo_sessions              | idx_demo_sessions_goal                                   | CREATE INDEX idx_demo_sessions_goal ON public.demo_sessions USING btree (detected_goal)
 public     | demo_sessions              | idx_demo_sessions_status                                 | CREATE INDEX idx_demo_sessions_status ON public.demo_sessions USING btree (status, updated_at)
 public     | demo_sessions              | idx_demo_sessions_user                                   | CREATE INDEX idx_demo_sessions_user ON public.demo_sessions USING btree (user_id)
 public     | demo_sessions              | idx_demo_sessions_user_type                              | CREATE INDEX idx_demo_sessions_user_type ON public.demo_sessions USING btree (user_type)
 public     | demo_users                 | demo_users_email_key                                     | CREATE UNIQUE INDEX demo_users_email_key ON public.demo_users USING btree (email)
 public     | demo_users                 | demo_users_pkey                                          | CREATE UNIQUE INDEX demo_users_pkey ON public.demo_users USING btree (id)
 public     | demo_users                 | idx_demo_users_admin_role                                | CREATE INDEX idx_demo_users_admin_role ON public.demo_users USING btree (admin_role) WHERE (user_type = 'admin'::text)
 public     | demo_users                 | idx_demo_users_created                                   | CREATE INDEX idx_demo_users_created ON public.demo_users USING btree (created_at)
 public     | demo_users                 | idx_demo_users_email                                     | CREATE INDEX idx_demo_users_email ON public.demo_users USING btree (email)
 public     | demo_users                 | idx_demo_users_last_login                                | CREATE INDEX idx_demo_users_last_login ON public.demo_users USING btree (last_login) WHERE (user_type = 'admin'::text)
 public     | demo_users                 | idx_demo_users_lead_score                                | CREATE INDEX idx_demo_users_lead_score ON public.demo_users USING btree (lead_score DESC)
 public     | demo_users                 | idx_demo_users_session                                   | CREATE INDEX idx_demo_users_session ON public.demo_users USING btree (demo_session_id)
 public     | demo_users                 | idx_demo_users_user_type                                 | CREATE INDEX idx_demo_users_user_type ON public.demo_users USING btree (user_type)
 public     | demo_users                 | idx_demo_users_verification_token                        | CREATE INDEX idx_demo_users_verification_token ON public.demo_users USING btree (verification_token)
 public     | demo_users                 | idx_demo_users_verified                                  | CREATE INDEX idx_demo_users_verified ON public.demo_users USING btree (email_verified, created_at)
 public     | demo_verification_requests | demo_verification_requests_pkey                          | CREATE UNIQUE INDEX demo_verification_requests_pkey ON public.demo_verification_requests USING btree (id)
 public     | demo_verification_requests | demo_verification_requests_verification_token_key        | CREATE UNIQUE INDEX demo_verification_requests_verification_token_key ON public.demo_verification_requests USING btree (verification_token)
 public     | demo_verification_requests | idx_verification_created                                 | CREATE INDEX idx_verification_created ON public.demo_verification_requests USING btree (created_at)
 public     | demo_verification_requests | idx_verification_email                                   | CREATE INDEX idx_verification_email ON public.demo_verification_requests USING btree (email, is_used)
 public     | demo_verification_requests | idx_verification_expires                                 | CREATE INDEX idx_verification_expires ON public.demo_verification_requests USING btree (expires_at)
 public     | demo_verification_requests | idx_verification_token                                   | CREATE INDEX idx_verification_token ON public.demo_verification_requests USING btree (verification_token)
 public     | event_logs                 | event_logs_pkey                                          | CREATE UNIQUE INDEX event_logs_pkey ON public.event_logs USING btree (id)
 public     | event_logs                 | idx_event_logs_event_type                                | CREATE INDEX idx_event_logs_event_type ON public.event_logs USING btree (event_type)
 public     | event_logs                 | idx_event_logs_session_id                                | CREATE INDEX idx_event_logs_session_id ON public.event_logs USING btree (session_id)
 public     | event_logs                 | idx_event_logs_timestamp                                 | CREATE INDEX idx_event_logs_timestamp ON public.event_logs USING btree ("timestamp")
 public     | faq_items                  | faq_items_pkey                                           | CREATE UNIQUE INDEX faq_items_pkey ON public.faq_items USING btree (id)
 public     | faq_items                  | idx_faq_items_category                                   | CREATE INDEX idx_faq_items_category ON public.faq_items USING btree (category, status)
 public     | faq_items                  | idx_faq_items_helpful                                    | CREATE INDEX idx_faq_items_helpful ON public.faq_items USING btree (helpful_votes DESC)
 public     | goal_analytics             | goal_analytics_date_goal_type_key                        | CREATE UNIQUE INDEX goal_analytics_date_goal_type_key ON public.goal_analytics USING btree (date, goal_type)
 public     | goal_analytics             | goal_analytics_pkey                                      | CREATE UNIQUE INDEX goal_analytics_pkey ON public.goal_analytics USING btree (id)
 public     | goal_analytics             | idx_goal_analytics_date                                  | CREATE INDEX idx_goal_analytics_date ON public.goal_analytics USING btree (date)
 public     | goal_analytics             | idx_goal_analytics_goal_type                             | CREATE INDEX idx_goal_analytics_goal_type ON public.goal_analytics USING btree (goal_type)
 public     | knowledge_base             | idx_knowledge_base_category                              | CREATE INDEX idx_knowledge_base_category ON public.knowledge_base USING btree (category, status)
 public     | knowledge_base             | idx_knowledge_base_status                                | CREATE INDEX idx_knowledge_base_status ON public.knowledge_base USING btree (status, updated_at)
 public     | knowledge_base             | knowledge_base_pkey                                      | CREATE UNIQUE INDEX knowledge_base_pkey ON public.knowledge_base USING btree (id)
 public     | knowledge_documents        | idx_knowledge_documents_active                           | CREATE INDEX idx_knowledge_documents_active ON public.knowledge_documents USING btree (is_active)
 public     | knowledge_documents        | idx_knowledge_documents_category                         | CREATE INDEX idx_knowledge_documents_category ON public.knowledge_documents USING btree (category)
 public     | knowledge_documents        | idx_knowledge_documents_embedding                        | CREATE INDEX idx_knowledge_documents_embedding ON public.knowledge_documents USING ivfflat (embedding public.vector_cosine_ops) WITH (lists='100')
 public     | knowledge_documents        | knowledge_documents_pkey                                 | CREATE UNIQUE INDEX knowledge_documents_pkey ON public.knowledge_documents USING btree (id)
 public     | persistent_admin_sessions  | persistent_admin_sessions_pkey                           | CREATE UNIQUE INDEX persistent_admin_sessions_pkey ON public.persistent_admin_sessions USING btree (token)
 public     | product_registrations      | product_registrations_created_at_idx                     | CREATE INDEX product_registrations_created_at_idx ON public.product_registrations USING btree (created_at DESC)
 public     | product_registrations      | product_registrations_email_verified_idx                 | CREATE INDEX product_registrations_email_verified_idx ON public.product_registrations USING btree (email_verified)
 public     | product_registrations      | product_registrations_pkey                               | CREATE UNIQUE INDEX product_registrations_pkey ON public.product_registrations USING btree (id)
 public     | product_registrations      | product_registrations_product_id_idx                     | CREATE INDEX product_registrations_product_id_idx ON public.product_registrations USING btree (product_id)
 public     | product_registrations      | product_registrations_product_status_idx                 | CREATE INDEX product_registrations_product_status_idx ON public.product_registrations USING btree (product_id, status)
 public     | product_registrations      | product_registrations_program_status_idx                 | CREATE INDEX product_registrations_program_status_idx ON public.product_registrations USING btree (program_type, status)
 public     | product_registrations      | product_registrations_program_type_idx                   | CREATE INDEX product_registrations_program_type_idx ON public.product_registrations USING btree (program_type)
 public     | product_registrations      | product_registrations_search_idx                         | CREATE INDEX product_registrations_search_idx ON public.product_registrations USING gin (to_tsvector('english'::regconfig, ((((((((full_name || ' '::text) || company) || ' '::text) || job_title) || ' '::text) || primary_business) || ' '::text) || use_case)))
 public     | product_registrations      | product_registrations_status_idx                         | CREATE INDEX product_registrations_status_idx ON public.product_registrations USING btree (status)
 public     | product_registrations      | product_registrations_unverified_email_idx               | CREATE INDEX product_registrations_unverified_email_idx ON public.product_registrations USING btree (lower(email), email_verified, created_at)
 public     | product_registrations      | product_registrations_user_id_idx                        | CREATE INDEX product_registrations_user_id_idx ON public.product_registrations USING btree (user_id)
 public     | product_registrations      | product_registrations_verified_email_product_program_idx | CREATE UNIQUE INDEX product_registrations_verified_email_product_program_idx ON public.product_registrations USING btree (lower(email), product_id, program_type) WHERE (email_verified = true)
 public     | prompt_performance_metrics | idx_prompt_performance_date                              | CREATE INDEX idx_prompt_performance_date ON public.prompt_performance_metrics USING btree (date)
 public     | prompt_performance_metrics | idx_prompt_performance_prompt                            | CREATE INDEX idx_prompt_performance_prompt ON public.prompt_performance_metrics USING btree (prompt_id)
 public     | prompt_performance_metrics | prompt_performance_metrics_pkey                          | CREATE UNIQUE INDEX prompt_performance_metrics_pkey ON public.prompt_performance_metrics USING btree (id)
 public     | prompt_performance_metrics | prompt_performance_metrics_prompt_id_date_key            | CREATE UNIQUE INDEX prompt_performance_metrics_prompt_id_date_key ON public.prompt_performance_metrics USING btree (prompt_id, date)
 public     | prompt_usage_logs          | idx_prompt_usage_logs_prompt_id                          | CREATE INDEX idx_prompt_usage_logs_prompt_id ON public.prompt_usage_logs USING btree (prompt_id)
 public     | prompt_usage_logs          | idx_prompt_usage_logs_session                            | CREATE INDEX idx_prompt_usage_logs_session ON public.prompt_usage_logs USING btree (session_id)
 public     | prompt_usage_logs          | idx_prompt_usage_logs_timestamp                          | CREATE INDEX idx_prompt_usage_logs_timestamp ON public.prompt_usage_logs USING btree ("timestamp")
 public     | prompt_usage_logs          | prompt_usage_logs_pkey                                   | CREATE UNIQUE INDEX prompt_usage_logs_pkey ON public.prompt_usage_logs USING btree (id)
 public     | response_templates         | idx_response_templates_intent                            | CREATE INDEX idx_response_templates_intent ON public.response_templates USING btree (intent_name, category_name)
 public     | response_templates         | response_templates_intent_name_category_name_key         | CREATE UNIQUE INDEX response_templates_intent_name_category_name_key ON public.response_templates USING btree (intent_name, category_name)
 public     | response_templates         | response_templates_pkey                                  | CREATE UNIQUE INDEX response_templates_pkey ON public.response_templates USING btree (id)
 public     | user_custom_prompts        | idx_user_custom_prompts_active                           | CREATE INDEX idx_user_custom_prompts_active ON public.user_custom_prompts USING btree (is_active)
 public     | user_custom_prompts        | idx_user_custom_prompts_user_id                          | CREATE INDEX idx_user_custom_prompts_user_id ON public.user_custom_prompts USING btree (user_id)
 public     | user_custom_prompts        | user_custom_prompts_pkey                                 | CREATE UNIQUE INDEX user_custom_prompts_pkey ON public.user_custom_prompts USING btree (id)
 public     | user_queries_log           | idx_user_queries_intent                                  | CREATE INDEX idx_user_queries_intent ON public.user_queries_log USING btree (query_intent, query_category)
 public     | user_queries_log           | idx_user_queries_session                                 | CREATE INDEX idx_user_queries_session ON public.user_queries_log USING btree (session_id, created_at)
 public     | user_queries_log           | user_queries_log_pkey                                    | CREATE UNIQUE INDEX user_queries_log_pkey ON public.user_queries_log USING btree (id)
 storage    | buckets                    | bname                                                    | CREATE UNIQUE INDEX bname ON storage.buckets USING btree (name)
 storage    | buckets                    | buckets_pkey                                             | CREATE UNIQUE INDEX buckets_pkey ON storage.buckets USING btree (id)
 storage    | buckets_analytics          | buckets_analytics_pkey                                   | CREATE UNIQUE INDEX buckets_analytics_pkey ON storage.buckets_analytics USING btree (id)
 storage    | migrations                 | migrations_name_key                                      | CREATE UNIQUE INDEX migrations_name_key ON storage.migrations USING btree (name)
 storage    | migrations                 | migrations_pkey                                          | CREATE UNIQUE INDEX migrations_pkey ON storage.migrations USING btree (id)
 storage    | objects                    | bucketid_objname                                         | CREATE UNIQUE INDEX bucketid_objname ON storage.objects USING btree (bucket_id, name)
 storage    | objects                    | idx_name_bucket_level_unique                             | CREATE UNIQUE INDEX idx_name_bucket_level_unique ON storage.objects USING btree (name COLLATE "C", bucket_id, level)
 storage    | objects                    | idx_objects_bucket_id_name                               | CREATE INDEX idx_objects_bucket_id_name ON storage.objects USING btree (bucket_id, name COLLATE "C")
 storage    | objects                    | idx_objects_lower_name                                   | CREATE INDEX idx_objects_lower_name ON storage.objects USING btree ((path_tokens[level]), lower(name) text_pattern_ops, bucket_id, level)
 storage    | objects                    | name_prefix_search                                       | CREATE INDEX name_prefix_search ON storage.objects USING btree (name text_pattern_ops)
 storage    | objects                    | objects_bucket_id_level_idx                              | CREATE UNIQUE INDEX objects_bucket_id_level_idx ON storage.objects USING btree (bucket_id, level, name COLLATE "C")
 storage    | objects                    | objects_pkey                                             | CREATE UNIQUE INDEX objects_pkey ON storage.objects USING btree (id)
 storage    | prefixes                   | idx_prefixes_lower_name                                  | CREATE INDEX idx_prefixes_lower_name ON storage.prefixes USING btree (bucket_id, level, ((string_to_array(name, '/'::text))[level]), lower(name) text_pattern_ops)
 storage    | prefixes                   | prefixes_pkey                                            | CREATE UNIQUE INDEX prefixes_pkey ON storage.prefixes USING btree (bucket_id, level, name)
 storage    | s3_multipart_uploads       | idx_multipart_uploads_list                               | CREATE INDEX idx_multipart_uploads_list ON storage.s3_multipart_uploads USING btree (bucket_id, key, created_at)
 storage    | s3_multipart_uploads       | s3_multipart_uploads_pkey                                | CREATE UNIQUE INDEX s3_multipart_uploads_pkey ON storage.s3_multipart_uploads USING btree (id)
 storage    | s3_multipart_uploads_parts | s3_multipart_uploads_parts_pkey                          | CREATE UNIQUE INDEX s3_multipart_uploads_parts_pkey ON storage.s3_multipart_uploads_parts USING btree (id)
(176 rows)


[Foreign Keys]

 table_schema |         table_name         |   column_name   | fk_schema |      fk_table      | fk_column 
--------------+----------------------------+-----------------+-----------+--------------------+-----------
 public       | demo_contacts              | session_id      | public    | demo_sessions      | id
 public       | demo_documents             | session_id      | public    | demo_sessions      | id
 public       | demo_interactions          | session_id      | public    | demo_sessions      | id
 public       | demo_sessions              | user_id         | public    | demo_users         | id
 public       | demo_users                 | demo_session_id | public    | demo_sessions      | id
 public       | demo_verification_requests | session_id      | public    | demo_sessions      | id
 public       | prompt_performance_metrics | prompt_id       | public    | contextual_prompts | id
 public       | prompt_usage_logs          | prompt_id       | public    | contextual_prompts | id
 public       | user_queries_log           | session_id      | public    | demo_sessions      | id
(9 rows)


[Top queries: pg_stat_statements]

