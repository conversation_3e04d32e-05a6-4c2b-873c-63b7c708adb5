// supabase/functions/save-contact/index.ts
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-api-key, x-session-id, accept',
  'Access-Control-Allow-Methods': 'POST, OPTIONS, GET'
};
// Email validation
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
// Phone validation (basic)
function isValidPhone(phone) {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}
// Privacy compliance checks
function checkPrivacyCompliance(contactData) {
  const issues = [];
  // Check for required privacy consent
  if (!contactData.email) {
    issues.push('Email is required for contact');
  }
  if (contactData.email && !isValidEmail(contactData.email)) {
    issues.push('Invalid email format');
  }
  if (contactData.phone && !isValidPhone(contactData.phone)) {
    issues.push('Invalid phone format');
  }
  // Check for sensitive data patterns (basic implementation)
  const sensitivePatterns = [
    /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/,
    /\b\d{3}[\s-]?\d{2}[\s-]?\d{4}\b/
  ];
  const allText = JSON.stringify(contactData).toLowerCase();
  for (const pattern of sensitivePatterns){
    if (pattern.test(allText)) {
      issues.push('Potentially sensitive data detected');
      break;
    }
  }
  return {
    compliant: issues.length === 0,
    issues
  };
}
// Send webhook to CRM (mock implementation)
async function sendCRMWebhook(payload) {
  const CRM_WEBHOOK_URL = Deno.env.get('CRM_WEBHOOK_URL');
  if (!CRM_WEBHOOK_URL) {
    console.log('CRM webhook not configured, skipping...');
    return true // Continue without webhook
    ;
  }
  try {
    const response = await fetch(CRM_WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Supabase-Edge-Function/1.0'
      },
      body: JSON.stringify(payload)
    });
    if (!response.ok) {
      console.error(`CRM webhook failed: ${response.status}`);
      return false;
    }
    console.log('CRM webhook sent successfully');
    return true;
  } catch (error) {
    console.error('CRM webhook error:', error);
    return false;
  }
}
// Generate lead score based on contact data
function calculateLeadScore(contactData, sessionContext) {
  let score = 0;
  // Base score for providing contact info
  score += 10;
  // Email provided
  if (contactData.email) score += 20;
  // Phone provided
  if (contactData.phone) score += 15;
  // Company provided
  if (contactData.company) score += 25;
  // Message/interest provided
  if (contactData.message && contactData.message.length > 10) score += 20;
  // Interest type specified
  if (contactData.interest_type) score += 15;
  // Session context indicates engagement
  if (sessionContext) {
    if (sessionContext.message_count > 5) score += 10;
    if (sessionContext.documents_generated > 0) score += 20;
    if (sessionContext.goal === 'consultation') score += 15;
  }
  return Math.min(score, 100) // Cap at 100
  ;
}
serve(async (req)=>{
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  try {
    const supabase = createClient(Deno.env.get('SUPABASE_URL') ?? '', Deno.env.get('SUPABASE_ANON_KEY') ?? '');
    const requestData = await req.json();
    const { name, email, phone, company, message, interest_type, preferred_contact_method, session_id, source = 'demo_chat', consent_marketing = false, consent_data_processing = false } = requestData;
    // Validate required fields
    if (!name || !email) {
      return new Response(JSON.stringify({
        error: 'Name and email are required'
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    const contactData = {
      name: name.trim(),
      email: email.trim().toLowerCase(),
      phone: phone?.trim(),
      company: company?.trim(),
      message: message?.trim(),
      interest_type,
      preferred_contact_method,
      session_id
    };
    // Privacy compliance check
    const privacyCheck = checkPrivacyCompliance(contactData);
    if (!privacyCheck.compliant) {
      return new Response(JSON.stringify({
        error: 'Privacy compliance issues',
        issues: privacyCheck.issues
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
    // Get session context if session_id provided
    let sessionContext = null;
    if (session_id) {
      const { data: session } = await supabase.from('demo_sessions').select('context, messages').eq('session_id', session_id).single();
      if (session) {
        sessionContext = {
          ...session.context,
          message_count: session.messages?.length || 0,
          documents_generated: session.context?.documents_generated || 0
        };
      }
    }
    // Calculate lead score
    const leadScore = calculateLeadScore(contactData, sessionContext);
    // Check for existing contact
    const { data: existingContact } = await supabase.from('demo_contacts').select('id, updated_at').eq('email', contactData.email).single();
    let contactRecord;
    if (existingContact) {
      // Update existing contact
      const { data: updatedContact, error: updateError } = await supabase.from('demo_contacts').update({
        name: contactData.name,
        phone: contactData.phone,
        company: contactData.company,
        last_message: contactData.message,
        interest_type: contactData.interest_type,
        preferred_contact_method: contactData.preferred_contact_method,
        lead_score: Math.max(leadScore, existingContact.lead_score || 0),
        last_session_id: session_id,
        contact_count: supabase.raw('contact_count + 1'),
        consent_marketing,
        consent_data_processing,
        updated_at: new Date().toISOString()
      }).eq('id', existingContact.id).select().single();
      if (updateError) throw updateError;
      contactRecord = updatedContact;
    } else {
      // Create new contact
      const { data: newContact, error: insertError } = await supabase.from('demo_contacts').insert({
        name: contactData.name,
        email: contactData.email,
        phone: contactData.phone,
        company: contactData.company,
        message: contactData.message,
        interest_type: contactData.interest_type,
        preferred_contact_method: contactData.preferred_contact_method,
        lead_score: leadScore,
        source,
        session_id,
        consent_marketing,
        consent_data_processing,
        contact_count: 1,
        status: 'new',
        created_at: new Date().toISOString()
      }).select().single();
      if (insertError) throw insertError;
      contactRecord = newContact;
    }
    // Update session with contact info if session exists
    if (session_id && sessionContext) {
      await supabase.from('demo_sessions').update({
        contact_id: contactRecord.id,
        context: {
          ...sessionContext,
          contact_captured: true,
          lead_score: leadScore,
          contact_updated_at: new Date().toISOString()
        }
      }).eq('session_id', session_id);
    }
    // Log the contact interaction
    await supabase.from('demo_interactions').insert({
      session_id: session_id || null,
      user_message: 'Contact form submitted',
      assistant_response: 'Contact information saved',
      detected_goal: 'lead_capture',
      context: {
        contact_id: contactRecord.id,
        lead_score: leadScore,
        is_returning_contact: !!existingContact,
        source
      },
      created_at: new Date().toISOString()
    });
    // Send CRM webhook
    const webhookPayload = {
      contact: contactData,
      source,
      timestamp: new Date().toISOString(),
      session_context: sessionContext
    };
    const webhookSent = await sendCRMWebhook(webhookPayload);
    const response = {
      success: true,
      contact: {
        id: contactRecord.id,
        name: contactRecord.name,
        email: contactRecord.email,
        lead_score: leadScore,
        is_returning: !!existingContact,
        status: contactRecord.status
      },
      privacy: {
        compliant: true,
        consent_marketing,
        consent_data_processing
      },
      integrations: {
        crm_webhook_sent: webhookSent
      },
      session_updated: !!session_id
    };
    return new Response(JSON.stringify(response), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Error in save-contact:', error);
    return new Response(JSON.stringify({
      error: 'Failed to save contact',
      details: error.message
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});
