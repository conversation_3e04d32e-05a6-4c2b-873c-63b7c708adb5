part of 'auth_cubit.dart';

enum AuthStatus { initial, loading, authenticated, failure }

class AuthState extends Equatable {
  final AuthStatus status;
  final bool isAdmin;
  final String? error;
  final Map<String, dynamic>? data;

  const AuthState({
    required this.status,
    this.isAdmin = false,
    this.error,
    this.data,
  });

  const AuthState.initial() : this(status: AuthStatus.initial);

  AuthState copyWith({
    AuthStatus? status,
    bool? isAdmin,
    String? error,
    Map<String, dynamic>? data,
  }) => AuthState(
    status: status ?? this.status,
    isAdmin: isAdmin ?? this.isAdmin,
    error: error,
    data: data ?? this.data,
  );

  @override
  List<Object?> get props => [status, isAdmin, error, data];
}
