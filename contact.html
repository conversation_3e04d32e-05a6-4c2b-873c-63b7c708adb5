<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Contact ArionComply - Get Started</title>
  <style>
    :root {
      --brand-primary: #059669;
      --brand-secondary: #047857;
      --brand-light: #ecfdf5;
      --neutral-50: #f8fafc;
      --neutral-100: #f1f5f9;
      --neutral-200: #e2e8f0;
      --neutral-400: #94a3b8;
      --neutral-600: #475569;
      --neutral-700: #334155;
      --neutral-800: #1e293b;
      --error: #ef4444;
      --success: #10b981;
    }

    * {
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--neutral-50);
      color: var(--neutral-700);
      line-height: 1.6;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
    }

    .header {
      text-align: center;
      margin-bottom: 3rem;
    }

    .header h1 {
      color: var(--brand-primary);
      font-size: 2.5rem;
      margin-bottom: 1rem;
    }

    .header p {
      font-size: 1.2rem;
      color: var(--neutral-600);
    }

    .form-container {
      background: white;
      padding: 2.5rem;
      border-radius: 12px;
      box-shadow: 0 4px 16px rgba(5, 150, 105, 0.1);
    }

    .progress-bar {
      background: var(--neutral-200);
      height: 6px;
      border-radius: 3px;
      margin-bottom: 2rem;
      overflow: hidden;
    }

    .progress-fill {
      background: var(--brand-primary);
      height: 100%;
      width: 0%;
      transition: width 0.3s ease;
    }

    .form-section {
      display: none;
      animation: fadeIn 0.3s ease-in;
    }

    .form-section.active {
      display: block;
    }

    .form-section h2 {
      color: var(--brand-primary);
      margin-bottom: 1.5rem;
      font-size: 1.5rem;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    .form-group label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 600;
      color: var(--neutral-800);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
      width: 100%;
      padding: 0.875rem;
      border: 2px solid var(--neutral-200);
      border-radius: 8px;
      font-size: 1rem;
      transition: border-color 0.2s ease;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
      outline: none;
      border-color: var(--brand-primary);
    }

    .form-group textarea {
      resize: vertical;
      min-height: 100px;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
    }

    .checkbox-group {
      display: flex;
      flex-wrap: wrap;
      gap: 0.5rem;
      margin-top: 0.5rem;
    }

    .checkbox-item {
      display: flex;
      align-items: center;
      background: var(--neutral-100);
      padding: 0.5rem 1rem;
      border-radius: 20px;
      cursor: pointer;
      transition: background 0.2s ease;
    }

    .checkbox-item:hover {
      background: var(--brand-light);
    }

    .checkbox-item input {
      width: auto;
      margin-right: 0.5rem;
    }

    .required {
      color: var(--error);
    }

    .form-navigation {
      display: flex;
      justify-content: space-between;
      margin-top: 2rem;
      padding-top: 2rem;
      border-top: 1px solid var(--neutral-200);
    }

    .btn {
      padding: 0.875rem 2rem;
      border: none;
      border-radius: 8px;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      text-decoration: none;
      display: inline-block;
      text-align: center;
    }

    .btn-primary {
      background: var(--brand-primary);
      color: white;
    }

    .btn-primary:hover {
      background: var(--brand-secondary);
      transform: translateY(-2px);
    }

    .btn-secondary {
      background: var(--neutral-200);
      color: var(--neutral-700);
    }

    .btn-secondary:hover {
      background: var(--neutral-300);
    }

    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    .error-message {
      color: var(--error);
      font-size: 0.875rem;
      margin-top: 0.25rem;
      display: none;
    }

    .success-message {
      text-align: center;
      padding: 2rem;
      background: var(--success);
      color: white;
      border-radius: 8px;
      margin-top: 2rem;
      display: none;
    }

    .loading {
      display: none;
      text-align: center;
      padding: 2rem;
    }

    .spinner {
      border: 3px solid var(--neutral-200);
      border-top: 3px solid var(--brand-primary);
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 0 auto 1rem;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @media (max-width: 768px) {
      .container {
        padding: 1rem;
      }
      
      .form-container {
        padding: 1.5rem;
      }
      
      .form-row {
        grid-template-columns: 1fr;
      }
      
      .header h1 {
        font-size: 2rem;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Get Started with ArionComply</h1>
      <p>Tell us about your compliance needs and we'll show you how our platform can help</p>
    </div>

    <div class="form-container">
      <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
      </div>

      <form id="contactForm">
        <!-- Section 1: Contact Information -->
        <div class="form-section active" data-section="1">
          <h2>Contact Information</h2>
          <div class="form-row">
            <div class="form-group">
              <label for="firstName">First Name <span class="required">*</span></label>
              <input type="text" id="firstName" name="first_name" required>
              <div class="error-message"></div>
            </div>
            <div class="form-group">
              <label for="lastName">Last Name <span class="required">*</span></label>
              <input type="text" id="lastName" name="last_name" required>
              <div class="error-message"></div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label for="email">Email <span class="required">*</span></label>
              <input type="email" id="email" name="email" required>
              <div class="error-message"></div>
            </div>
            <div class="form-group">
              <label for="phone">Phone Number</label>
              <input type="tel" id="phone" name="phone_number">
              <div class="error-message"></div>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label for="company">Company <span class="required">*</span></label>
              <input type="text" id="company" name="company_name" required>
              <div class="error-message"></div>
            </div>
            <div class="form-group">
              <label for="jobTitle">Job Title <span class="required">*</span></label>
              <input type="text" id="jobTitle" name="job_title" required>
              <div class="error-message"></div>
            </div>
          </div>
        </div>

        <!-- Section 2: Company Profile -->
        <div class="form-section" data-section="2">
          <h2>Company Profile</h2>
          <div class="form-row">
            <div class="form-group">
              <label for="companySize">Company Size <span class="required">*</span></label>
              <select id="companySize" name="company_size" required>
                <option value="">Select size</option>
                <option value="1-10">1-10 employees</option>
                <option value="11-50">11-50 employees</option>
                <option value="51-200">51-200 employees</option>
                <option value="201-500">201-500 employees</option>
                <option value="501-1000">501-1000 employees</option>
                <option value="1000+">1000+ employees</option>
              </select>
            </div>
            <div class="form-group">
              <label for="industry">Industry <span class="required">*</span></label>
              <select id="industry" name="industry" required>
                <option value="">Select industry</option>
                <option value="technology">Technology</option>
                <option value="financial-services">Financial Services</option>
                <option value="healthcare">Healthcare</option>
                <option value="manufacturing">Manufacturing</option>
                <option value="retail">Retail</option>
                <option value="government">Government</option>
                <option value="education">Education</option>
                <option value="nonprofit">Nonprofit</option>
                <option value="other">Other</option>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label for="revenue">Annual Revenue</label>
            <select id="revenue" name="annual_revenue">
              <option value="">Select range</option>
              <option value="under-1m">Under $1M</option>
              <option value="1m-10m">$1M - $10M</option>
              <option value="10m-50m">$10M - $50M</option>
              <option value="50m-100m">$50M - $100M</option>
              <option value="100m-500m">$100M - $500M</option>
              <option value="500m+">$500M+</option>
            </select>
          </div>
        </div>

        <!-- Section 3: Compliance Context -->
        <div class="form-section" data-section="3">
          <h2>Compliance Context</h2>
          <div class="form-group">
            <label>Which frameworks are you interested in? <span class="required">*</span></label>
            <div class="checkbox-group">
              <div class="checkbox-item">
                <input type="checkbox" id="iso27001" name="compliance_frameworks" value="iso27001">
                <label for="iso27001">ISO 27001</label>
              </div>
              <div class="checkbox-item">
                <input type="checkbox" id="gdpr" name="compliance_frameworks" value="gdpr">
                <label for="gdpr">GDPR</label>
              </div>
              <div class="checkbox-item">
                <input type="checkbox" id="soc2" name="compliance_frameworks" value="soc2">
                <label for="soc2">SOC 2</label>
              </div>
              <div class="checkbox-item">
                <input type="checkbox" id="hipaa" name="compliance_frameworks" value="hipaa">
                <label for="hipaa">HIPAA</label>
              </div>
              <div class="checkbox-item">
                <input type="checkbox" id="pcidss" name="compliance_frameworks" value="pcidss">
                <label for="pcidss">PCI DSS</label>
              </div>
              <div class="checkbox-item">
                <input type="checkbox" id="aiact" name="compliance_frameworks" value="aiact">
                <label for="aiact">EU AI Act</label>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label for="complianceStatus">Current Compliance Status</label>
            <select id="complianceStatus" name="current_compliance_status">
              <option value="">Select status</option>
              <option value="not-started">Not started</option>
              <option value="researching">Researching options</option>
              <option value="planning">Planning implementation</option>
              <option value="in-progress">Implementation in progress</option>
              <option value="certified">Already certified</option>
              <option value="maintaining">Maintaining compliance</option>
            </select>
          </div>
          <div class="form-group">
            <label>What are your main compliance challenges?</label>
            <div class="checkbox-group">
              <div class="checkbox-item">
                <input type="checkbox" id="lack-expertise" name="pain_points" value="lack-expertise">
                <label for="lack-expertise">Lack of expertise</label>
              </div>
              <div class="checkbox-item">
                <input type="checkbox" id="time-consuming" name="pain_points" value="time-consuming">
                <label for="time-consuming">Too time consuming</label>
              </div>
              <div class="checkbox-item">
                <input type="checkbox" id="expensive" name="pain_points" value="expensive">
                <label for="expensive">Too expensive</label>
              </div>
              <div class="checkbox-item">
                <input type="checkbox" id="complex" name="pain_points" value="complex">
                <label for="complex">Too complex</label>
              </div>
              <div class="checkbox-item">
                <input type="checkbox" id="maintaining" name="pain_points" value="maintaining">
                <label for="maintaining">Maintaining compliance</label>
              </div>
            </div>
          </div>
        </div>

        <!-- Section 4: Decision Context -->
        <div class="form-section" data-section="4">
          <h2>Decision Context</h2>
          <div class="form-row">
            <div class="form-group">
              <label for="timeline">Implementation Timeline</label>
              <select id="timeline" name="decision_timeline">
                <option value="">Select timeline</option>
                <option value="immediate">Immediate (within 1 month)</option>
                <option value="short-term">Short-term (1-3 months)</option>
                <option value="medium-term">Medium-term (3-6 months)</option>
                <option value="long-term">Long-term (6+ months)</option>
                <option value="exploring">Just exploring options</option>
              </select>
            </div>
            <div class="form-group">
              <label for="authority">Decision Making Authority</label>
              <select id="authority" name="decision_authority">
                <option value="">Select authority</option>
                <option value="decision-maker">I make the final decision</option>
                <option value="influencer">I influence the decision</option>
                <option value="recommender">I recommend solutions</option>
                <option value="researcher">I research options</option>
                <option value="other">Other</option>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label for="currentSolution">Current Solution</label>
            <select id="currentSolution" name="current_solution">
              <option value="">Select current approach</option>
              <option value="manual">Manual processes</option>
              <option value="consultants">External consultants</option>
              <option value="internal-team">Internal team</option>
              <option value="other-software">Other software</option>
              <option value="none">No current solution</option>
            </select>
          </div>
          <div class="form-group">
            <label for="budget">Budget Range</label>
            <select id="budget" name="budget_range">
              <option value="">Select budget</option>
              <option value="under-10k">Under $10K</option>
              <option value="10k-50k">$10K - $50K</option>
              <option value="50k-100k">$50K - $100K</option>
              <option value="100k-250k">$100K - $250K</option>
              <option value="250k+">$250K+</option>
            </select>
          </div>
        </div>

        <!-- Section 5: Engagement Preferences -->
        <div class="form-section" data-section="5">
          <h2>How We Can Help</h2>
          <div class="form-group">
            <label for="contactMethod">Preferred Contact Method</label>
            <select id="contactMethod" name="preferred_contact_method">
              <option value="">Select preference</option>
              <option value="email">Email</option>
              <option value="phone">Phone call</option>
              <option value="video">Video call</option>
              <option value="demo">Schedule a demo</option>
              <option value="pilot">Discuss pilot program</option>
            </select>
          </div>
          <div class="form-group">
            <label for="interests">What would you like to learn about?</label>
            <div class="checkbox-group">
              <div class="checkbox-item">
                <input type="checkbox" id="platform-demo" name="interest_area" value="platform-demo">
                <label for="platform-demo">Platform demo</label>
              </div>
              <div class="checkbox-item">
                <input type="checkbox" id="pilot-program" name="interest_area" value="pilot-program">
                <label for="pilot-program">Free pilot program</label>
              </div>
              <div class="checkbox-item">
                <input type="checkbox" id="pricing" name="interest_area" value="pricing">
                <label for="pricing">Pricing information</label>
              </div>
              <div class="checkbox-item">
                <input type="checkbox" id="implementation" name="interest_area" value="implementation">
                <label for="implementation">Implementation support</label>
              </div>
              <div class="checkbox-item">
                <input type="checkbox" id="custom-demo" name="interest_area" value="custom-demo">
                <label for="custom-demo">Custom demo</label>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label for="message">Additional Information</label>
            <textarea id="message" name="message" placeholder="Tell us more about your specific needs or questions..."></textarea>
          </div>
          <div class="form-group">
            <div class="checkbox-item">
              <input type="checkbox" id="dataConsent" name="data_processing_consented" required>
              <label for="dataConsent">I consent to processing of my personal data <span class="required">*</span></label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="marketingConsent" name="marketing_consented">
              <label for="marketingConsent">I would like to receive marketing communications</label>
            </div>
          </div>
        </div>

        <div class="form-navigation">
          <button type="button" class="btn btn-secondary" id="prevBtn" onclick="changeSection(-1)">Previous</button>
          <button type="button" class="btn btn-primary" id="nextBtn" onclick="changeSection(1)">Next</button>
          <button type="submit" class="btn btn-primary" id="submitBtn" style="display: none;">Submit</button>
        </div>
      </form>

      <div class="loading" id="loading">
        <div class="spinner"></div>
        <p>Submitting your information...</p>
      </div>

      <div class="success-message" id="successMessage">
        <h3>Thank you!</h3>
        <p>We've received your information and will be in touch soon.</p>
        <a href="index.html" class="btn btn-primary">Return to Demo</a>
      </div>
    </div>
  </div>

  <script src="src/config.js"></script>
  <script src="src/utils.js"></script>
  <script>
    let currentSection = 1;
    const totalSections = 5;

    function updateProgress() {
      const progress = (currentSection / totalSections) * 100;
      document.getElementById('progressFill').style.width = progress + '%';
    }

    function showSection(sectionNumber) {
      document.querySelectorAll('.form-section').forEach(section => {
        section.classList.remove('active');
      });
      document.querySelector(`[data-section="${sectionNumber}"]`).classList.add('active');
      
      // Update navigation buttons
      document.getElementById('prevBtn').style.display = sectionNumber === 1 ? 'none' : 'inline-block';
      document.getElementById('nextBtn').style.display = sectionNumber === totalSections ? 'none' : 'inline-block';
      document.getElementById('submitBtn').style.display = sectionNumber === totalSections ? 'inline-block' : 'none';
      
      updateProgress();
    }

    function changeSection(direction) {
      if (direction === 1 && currentSection < totalSections) {
        if (validateCurrentSection()) {
          currentSection++;
          showSection(currentSection);
        }
      } else if (direction === -1 && currentSection > 1) {
        currentSection--;
        showSection(currentSection);
      }
    }

    function validateCurrentSection() {
      const section = document.querySelector(`[data-section="${currentSection}"]`);
      const requiredFields = section.querySelectorAll('[required]');
      let isValid = true;

      requiredFields.forEach(field => {
        if (field.type === 'checkbox') {
          if (field.name === 'compliance_frameworks') {
            const checkboxes = section.querySelectorAll('[name="compliance_frameworks"]');
            const hasChecked = Array.from(checkboxes).some(cb => cb.checked);
            if (!hasChecked) {
              isValid = false;
              showError(field, 'Please select at least one framework');
            } else {
              clearError(field);
            }
          } else if (!field.checked) {
            isValid = false;
            showError(field, 'This field is required');
          } else {
            clearError(field);
          }
        } else if (!field.value.trim()) {
          isValid = false;
          showError(field, 'This field is required');
        } else {
          clearError(field);
        }
      });

      return isValid;
    }

    function showError(field, message) {
      const errorDiv = field.closest('.form-group').querySelector('.error-message');
      if (errorDiv) {
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
      }
      field.style.borderColor = 'var(--error)';
    }

    function clearError(field) {
      const errorDiv = field.closest('.form-group').querySelector('.error-message');
      if (errorDiv) {
        errorDiv.style.display = 'none';
      }
      field.style.borderColor = 'var(--neutral-200)';
    }

    function collectFormData() {
      const formData = new FormData(document.getElementById('contactForm'));
      const data = {};
      
      // Handle regular fields
      for (let [key, value] of formData.entries()) {
        if (data[key]) {
          if (Array.isArray(data[key])) {
            data[key].push(value);
          } else {
            data[key] = [data[key], value];
          }
        } else {
          data[key] = value;
        }
      }
      
      // Handle checkboxes that weren't checked
      const checkboxFields = ['compliance_frameworks', 'pain_points', 'interest_area'];
      checkboxFields.forEach(field => {
        if (!data[field]) {
          data[field] = [];
        } else if (!Array.isArray(data[field])) {
          data[field] = [data[field]];
        }
      });
      
      // Calculate completion percentage
      const totalFields = document.querySelectorAll('input, select, textarea').length;
      const filledFields = Object.values(data).filter(val => 
        val !== '' && val !== null && val !== undefined && 
        (Array.isArray(val) ? val.length > 0 : true)
      ).length;
      
      data.form_completion_percentage = Math.round((filledFields / totalFields) * 100) / 100;
      data.lead_source = 'contact_form';
      
      return data;
    }

    async function submitForm() {
      if (!validateCurrentSection()) return;
      
      const formData = collectFormData();
      const loadingDiv = document.getElementById('loading');
      const formContainer = document.querySelector('.form-container');
      const successDiv = document.getElementById('successMessage');
      
      try {
        loadingDiv.style.display = 'block';
        formContainer.style.display = 'none';
        
// Get or create session ID
let sessionId = ArionUtils.session.getSessionFromStorage();
if (!sessionId) {
  console.log('🔄 No session found, creating new session...');
  
  // Create a new session via the API
  const sessionResponse = await ArionUtils.api.call('init-session', {
    userAgent: navigator.userAgent,
    referrer: document.referrer,
    timestamp: new Date().toISOString()
  });
  
  if (sessionResponse.success) {
    sessionId = sessionResponse.data.sessionId;
    ArionUtils.session.saveSessionToStorage(sessionId);
    console.log('✅ New session created:', sessionId);
  } else {
    throw new Error('Failed to create session');
  }
}
        
        const response = await ArionUtils.api.call('save-contact', formData, sessionId);
        
        if (response.success) {
          loadingDiv.style.display = 'none';
          successDiv.style.display = 'block';
          
          // Track successful submission
          ArionUtils.analytics.track('contact_form_submitted', {
            completionPercentage: formData.form_completion_percentage,
            frameworks: formData.compliance_frameworks,
            companySize: formData.company_size,
            industry: formData.industry
          }, sessionId);
        } else {
          throw new Error(response.error || 'Submission failed');
        }
        
      } catch (error) {
        console.error('Form submission error:', error);
        loadingDiv.style.display = 'none';
        formContainer.style.display = 'block';
        alert('There was an error submitting your form. Please try again.');
      }
    }

    // Event listeners
    document.getElementById('contactForm').addEventListener('submit', (e) => {
      e.preventDefault();
      submitForm();
    });

    // Clear errors on input
    document.querySelectorAll('input, select, textarea').forEach(field => {
      field.addEventListener('input', () => clearError(field));
      field.addEventListener('change', () => clearError(field));
    });

    // Initialize
    showSection(1);
  </script>
</body>
</html>